import { test, expect } from '@playwright/test';

test.describe('Connection Test', () => {
  test('should connect to localhost:3000', async ({ page }) => {
    console.log('Attempting to connect to http://localhost:3000');
    
    try {
      await page.goto('http://localhost:3000', { 
        waitUntil: 'networkidle',
        timeout: 10000 
      });
      
      console.log('Successfully connected to localhost:3000');
      console.log('Page title:', await page.title());
      console.log('Page URL:', page.url());
      
      expect(page.url()).toContain('localhost:3000');
    } catch (error) {
      console.error('Connection failed:', error);
      throw error;
    }
  });

  test('should access dashboard and get redirected to signin', async ({ page }) => {
    console.log('Testing dashboard redirect...');
    
    try {
      // Try to access dashboard without authentication
      await page.goto('http://localhost:3000/dashboard', { 
        waitUntil: 'networkidle',
        timeout: 10000 
      });
      
      console.log('Dashboard access URL:', page.url());
      
      // Should be redirected to signin
      expect(page.url()).toContain('/auth/signin');
    } catch (error) {
      console.error('Dashboard redirect test failed:', error);
      throw error;
    }
  });
});
