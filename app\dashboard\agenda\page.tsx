'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { 
  Calendar as CalendarIcon, 
  Clock, 
  Plus, 
  Edit, 
  Trash2, 
  Save,
  User,
  MapPin,
  Phone,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface AvailabilitySlot {
  id: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  slotType: 'WORK' | 'BREAK' | 'UNAVAILABLE';
}

interface Booking {
  id: string;
  scheduledDate: string;
  scheduledTime: string;
  duration: number;
  status: 'SCHEDULED' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  notes?: string;
  match: {
    id: string;
    client: {
      id: string;
      name: string;
      image?: string;
      phone?: string;
    };
    request: {
      id: string;
      title: string;
      category: string;
      locationAddress?: string;
      locationCity?: string;
    };
  };
}

const DAYS_OF_WEEK = [
  { value: 0, label: 'Dimanche' },
  { value: 1, label: 'Lundi' },
  { value: 2, label: 'Mardi' },
  { value: 3, label: 'Mercredi' },
  { value: 4, label: 'Jeudi' },
  { value: 5, label: 'Vendredi' },
  { value: 6, label: 'Samedi' },
];

const SLOT_TYPES = [
  { value: 'WORK', label: 'Travail', color: 'bg-green-100 text-green-800' },
  { value: 'BREAK', label: 'Pause', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'UNAVAILABLE', label: 'Indisponible', color: 'bg-red-100 text-red-800' },
];

const STATUS_COLORS = {
  SCHEDULED: 'bg-blue-100 text-blue-800',
  CONFIRMED: 'bg-green-100 text-green-800',
  IN_PROGRESS: 'bg-orange-100 text-orange-800',
  COMPLETED: 'bg-gray-100 text-gray-800',
  CANCELLED: 'bg-red-100 text-red-800',
};

const STATUS_LABELS = {
  SCHEDULED: 'Planifié',
  CONFIRMED: 'Confirmé',
  IN_PROGRESS: 'En cours',
  COMPLETED: 'Terminé',
  CANCELLED: 'Annulé',
};

export default function AgendaPage() {
  const { data: session } = useSession();
  const [availabilitySlots, setAvailabilitySlots] = useState<AvailabilitySlot[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [isSlotDialogOpen, setIsSlotDialogOpen] = useState(false);
  const [editingSlot, setEditingSlot] = useState<AvailabilitySlot | null>(null);
  const [selectedDay, setSelectedDay] = useState<number>(1); // Lundi par défaut

  // Form state pour les créneaux
  const [slotForm, setSlotForm] = useState({
    dayOfWeek: 1,
    startTime: '09:00',
    endTime: '17:00',
    isAvailable: true,
    slotType: 'WORK' as const,
  });

  useEffect(() => {
    if (session?.user?.id) {
      fetchAvailabilitySlots();
      fetchBookings();
    }
  }, [session]);

  const fetchAvailabilitySlots = async () => {
    try {
      const response = await fetch(`/api/availability?userId=${session?.user?.id}`);
      const data = await response.json();
      
      if (data.success) {
        setAvailabilitySlots(data.data.slots);
      } else {
        toast.error('Erreur lors du chargement des disponibilités');
      }
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors du chargement des disponibilités');
    }
  };

  const fetchBookings = async () => {
    try {
      const response = await fetch('/api/bookings?upcoming=true');
      const data = await response.json();
      
      if (data.success) {
        setBookings(data.data);
      } else {
        toast.error('Erreur lors du chargement des réservations');
      }
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors du chargement des réservations');
    } finally {
      setLoading(false);
    }
  };

  const handleSlotSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const url = editingSlot ? `/api/availability/${editingSlot.id}` : '/api/availability';
      const method = editingSlot ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(slotForm),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success(editingSlot ? 'Créneau mis à jour' : 'Créneau ajouté');
        setIsSlotDialogOpen(false);
        setEditingSlot(null);
        resetSlotForm();
        fetchAvailabilitySlots();
      } else {
        toast.error(data.error || 'Erreur lors de la sauvegarde');
      }
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors de la sauvegarde');
    }
  };

  const handleDeleteSlot = async (id: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce créneau ?')) {
      return;
    }

    try {
      const response = await fetch(`/api/availability/${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Créneau supprimé');
        fetchAvailabilitySlots();
      } else {
        toast.error(data.error || 'Erreur lors de la suppression');
      }
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors de la suppression');
    }
  };

  const resetSlotForm = () => {
    setSlotForm({
      dayOfWeek: selectedDay,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      slotType: 'WORK',
    });
  };

  const startEditSlot = (slot: AvailabilitySlot) => {
    setEditingSlot(slot);
    setSlotForm({
      dayOfWeek: slot.dayOfWeek,
      startTime: slot.startTime,
      endTime: slot.endTime,
      isAvailable: slot.isAvailable,
      slotType: slot.slotType,
    });
    setIsSlotDialogOpen(true);
  };

  const getSlotsByDay = (dayOfWeek: number) => {
    return availabilitySlots
      .filter(slot => slot.dayOfWeek === dayOfWeek)
      .sort((a, b) => a.startTime.localeCompare(b.startTime));
  };

  const getBookingsForDay = (dayOfWeek: number) => {
    const today = new Date();
    const targetDate = new Date(today);
    
    // Calculer la prochaine occurrence de ce jour
    const daysUntilTarget = (dayOfWeek - today.getDay() + 7) % 7;
    targetDate.setDate(today.getDate() + daysUntilTarget);
    
    const targetDateString = targetDate.toISOString().split('T')[0];
    
    return bookings.filter(booking => 
      booking.scheduledDate.split('T')[0] === targetDateString
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mon Agenda</h1>
          <p className="text-gray-600 mt-2">
            Gérez vos disponibilités et vos réservations
          </p>
        </div>
      </div>

      <Tabs defaultValue="availability" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="availability">Disponibilités</TabsTrigger>
          <TabsTrigger value="bookings">Réservations</TabsTrigger>
        </TabsList>

        <TabsContent value="availability" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Mes disponibilités</h2>
            <Dialog open={isSlotDialogOpen} onOpenChange={setIsSlotDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => { resetSlotForm(); setEditingSlot(null); }}>
                  <Plus className="w-4 h-4 mr-2" />
                  Ajouter un créneau
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>
                    {editingSlot ? 'Modifier le créneau' : 'Ajouter un créneau'}
                  </DialogTitle>
                </DialogHeader>
                
                <form onSubmit={handleSlotSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="dayOfWeek">Jour de la semaine</Label>
                    <Select 
                      value={slotForm.dayOfWeek.toString()} 
                      onValueChange={(value) => setSlotForm({ ...slotForm, dayOfWeek: parseInt(value) })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {DAYS_OF_WEEK.map((day) => (
                          <SelectItem key={day.value} value={day.value.toString()}>
                            {day.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="startTime">Heure de début</Label>
                      <Input
                        id="startTime"
                        type="time"
                        value={slotForm.startTime}
                        onChange={(e) => setSlotForm({ ...slotForm, startTime: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="endTime">Heure de fin</Label>
                      <Input
                        id="endTime"
                        type="time"
                        value={slotForm.endTime}
                        onChange={(e) => setSlotForm({ ...slotForm, endTime: e.target.value })}
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="slotType">Type de créneau</Label>
                    <Select 
                      value={slotForm.slotType} 
                      onValueChange={(value: 'WORK' | 'BREAK' | 'UNAVAILABLE') => setSlotForm({ ...slotForm, slotType: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {SLOT_TYPES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isAvailable"
                      checked={slotForm.isAvailable}
                      onCheckedChange={(checked) => setSlotForm({ ...slotForm, isAvailable: checked })}
                    />
                    <Label htmlFor="isAvailable">Disponible</Label>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={() => setIsSlotDialogOpen(false)}>
                      Annuler
                    </Button>
                    <Button type="submit">
                      <Save className="w-4 h-4 mr-2" />
                      {editingSlot ? 'Mettre à jour' : 'Ajouter'}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {/* Planning hebdomadaire */}
          <div className="grid grid-cols-1 lg:grid-cols-7 gap-4">
            {DAYS_OF_WEEK.map((day) => {
              const daySlots = getSlotsByDay(day.value);
              const dayBookings = getBookingsForDay(day.value);
              
              return (
                <Card key={day.value} className="min-h-[300px]">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-center">
                      {day.label}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {daySlots.length === 0 ? (
                      <div className="text-center text-gray-500 text-sm py-4">
                        Aucun créneau défini
                      </div>
                    ) : (
                      daySlots.map((slot) => {
                        const slotType = SLOT_TYPES.find(t => t.value === slot.slotType);
                        return (
                          <div
                            key={slot.id}
                            className="relative group p-2 rounded border border-gray-200 hover:shadow-sm"
                          >
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="text-xs font-medium">
                                  {slot.startTime} - {slot.endTime}
                                </div>
                                <Badge 
                                  variant="secondary" 
                                  className={`text-xs mt-1 ${slotType?.color}`}
                                >
                                  {slotType?.label}
                                </Badge>
                              </div>
                              <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => startEditSlot(slot)}
                                  className="h-6 w-6 p-0"
                                >
                                  <Edit className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleDeleteSlot(slot.id)}
                                  className="h-6 w-6 p-0 text-red-600"
                                >
                                  <Trash2 className="w-3 h-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        );
                      })
                    )}
                    
                    {/* Réservations pour ce jour */}
                    {dayBookings.map((booking) => (
                      <div
                        key={booking.id}
                        className="p-2 rounded bg-blue-50 border border-blue-200"
                      >
                        <div className="text-xs font-medium text-blue-800">
                          {booking.scheduledTime} - Réservé
                        </div>
                        <div className="text-xs text-blue-600 mt-1">
                          {booking.match.client.name}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="bookings" className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-4">Mes réservations</h2>
            
            {bookings.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <CalendarIcon className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Aucune réservation
                  </h3>
                  <p className="text-gray-600">
                    Vos prochaines réservations apparaîtront ici.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {bookings.map((booking) => (
                  <Card key={booking.id}>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-3">
                            <Badge className={STATUS_COLORS[booking.status]}>
                              {STATUS_LABELS[booking.status]}
                            </Badge>
                            <div className="flex items-center text-sm text-gray-500">
                              <CalendarIcon className="w-4 h-4 mr-1" />
                              {new Date(booking.scheduledDate).toLocaleDateString()}
                            </div>
                            <div className="flex items-center text-sm text-gray-500">
                              <Clock className="w-4 h-4 mr-1" />
                              {booking.scheduledTime} ({booking.duration} min)
                            </div>
                          </div>
                          
                          <h3 className="font-semibold text-lg mb-2">
                            {booking.match.request.title}
                          </h3>
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <div className="flex items-center">
                              <User className="w-4 h-4 mr-1" />
                              {booking.match.client.name}
                            </div>
                            {booking.match.client.phone && (
                              <div className="flex items-center">
                                <Phone className="w-4 h-4 mr-1" />
                                {booking.match.client.phone}
                              </div>
                            )}
                            {booking.match.request.locationCity && (
                              <div className="flex items-center">
                                <MapPin className="w-4 h-4 mr-1" />
                                {booking.match.request.locationCity}
                              </div>
                            )}
                          </div>
                          
                          {booking.notes && (
                            <div className="mt-3 p-3 bg-gray-50 rounded text-sm">
                              <strong>Notes:</strong> {booking.notes}
                            </div>
                          )}
                        </div>
                        
                        <div className="flex space-x-2">
                          {booking.status === 'SCHEDULED' && (
                            <>
                              <Button size="sm" variant="outline">
                                <CheckCircle className="w-4 h-4 mr-1" />
                                Confirmer
                              </Button>
                              <Button size="sm" variant="destructive">
                                <XCircle className="w-4 h-4 mr-1" />
                                Annuler
                              </Button>
                            </>
                          )}
                          {booking.status === 'CONFIRMED' && (
                            <Button size="sm">
                              <AlertCircle className="w-4 h-4 mr-1" />
                              Commencer
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
