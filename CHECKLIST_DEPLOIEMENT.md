# ✅ Checklist de Déploiement - Nouvelles Fonctionnalités QribLik

## 🎯 **Étapes Obligatoires**

### **1. Préparation de la Base de Données** 🗄️
```bash
# Dans le terminal, à la racine du projet :
npx prisma generate
npx prisma db push
```
- [ ] Client Prisma généré sans erreur
- [ ] Nouveaux modèles appliqués à la base de données
- [ ] Vérification avec `npx prisma studio`

### **2. Configuration des Variables d'Environnement** 🔧
```bash
# Copier le fichier d'exemple
cp .env.example .env.local
```
- [ ] Fichier `.env.local` créé
- [ ] `DATABASE_URL` configuré
- [ ] `NEXTAUTH_SECRET` défini (minimum 32 caractères)
- [ ] `NEXTAUTH_URL` défini (`http://localhost:3000` en dev)

### **3. Test de Base** 🧪
```bash
# Ajouter des données de test
npx tsx scripts/seed-new-features.ts

# Tester la configuration
npx tsx scripts/test-new-features.ts
```
- [ ] Données de test créées
- [ ] Tous les modèles fonctionnent
- [ ] Aucune erreur critique

### **4. Démarrage du Serveur** 🚀
```bash
npm run dev
```
- [ ] Serveur démarre sans erreur
- [ ] Accessible sur http://localhost:3000
- [ ] Authentification fonctionne

---

## 🌟 **Test des Nouvelles Fonctionnalités**

### **Portfolio Artisans** 📸
**URL:** http://localhost:3000/dashboard/portfolio

- [ ] Page se charge correctement
- [ ] Peut créer un nouvel élément de portfolio
- [ ] Upload d'images fonctionne (ou message d'erreur si UploadThing non configuré)
- [ ] Filtres par catégorie fonctionnent
- [ ] Statistiques s'affichent (vues, likes, partages)
- [ ] Boutons de partage social présents

### **Agenda Intégré** 📅
**URL:** http://localhost:3000/dashboard/agenda

- [ ] Page se charge correctement
- [ ] Peut créer des créneaux de disponibilité
- [ ] Planning hebdomadaire s'affiche
- [ ] Gestion des conflits de créneaux
- [ ] Onglet réservations accessible
- [ ] Formulaires de création fonctionnent

### **Carte des Demandes** 🗺️
**URL:** http://localhost:3000/dashboard/map

- [ ] Page se charge correctement
- [ ] Cartes des villes marocaines s'affichent
- [ ] Filtres par catégorie fonctionnent
- [ ] Sélection d'une zone affiche les détails
- [ ] Recommandations générées
- [ ] Calcul de distance (si géolocalisation activée)

### **Partage Réseaux Sociaux** 📱
**Test depuis le portfolio :**

- [ ] Modal de partage s'ouvre
- [ ] Templates de texte disponibles
- [ ] Personnalisation du message fonctionne
- [ ] Boutons des plateformes fonctionnent
- [ ] Copie du lien fonctionne
- [ ] Compteurs de partage s'incrémentent

### **Notifications Améliorées** 🔔
**Cliquer sur l'icône cloche dans la navigation :**

- [ ] Centre de notifications s'ouvre
- [ ] Notifications de test s'affichent
- [ ] Onglets (Toutes/Non lues/Lues) fonctionnent
- [ ] Marquer comme lu fonctionne
- [ ] Badge de compteur se met à jour
- [ ] Groupement par date fonctionne

---

## 🔧 **Configuration Optionnelle**

### **Google Maps API** (pour la carte des demandes)
```env
GOOGLE_MAPS_API_KEY="your-google-maps-api-key"
GOOGLE_PLACES_API_KEY="your-google-places-api-key"
```
- [ ] Clés API obtenues sur Google Cloud Console
- [ ] APIs activées : Maps JavaScript, Places, Geocoding
- [ ] Restrictions de domaine configurées
- [ ] Facturation activée sur Google Cloud

### **UploadThing** (pour l'upload d'images)
```env
UPLOADTHING_SECRET="your-uploadthing-secret"
UPLOADTHING_APP_ID="your-uploadthing-app-id"
```
- [ ] Compte UploadThing créé
- [ ] Projet configuré
- [ ] Restrictions de fichiers définies (images, 10MB max)
- [ ] Clés API copiées

---

## 🚨 **Résolution des Problèmes Courants**

### **Erreur : "Cannot find module"**
```bash
npm install date-fns sonner
```

### **Erreur Prisma : "Schema drift detected"**
```bash
npx prisma db push --force-reset
# ⚠️ Attention : Supprime toutes les données !
```

### **Erreur : "NextAuth configuration error"**
- Vérifier que `NEXTAUTH_SECRET` est défini
- Vérifier que `NEXTAUTH_URL` correspond à votre domaine

### **Page 404 sur les nouvelles routes**
- Vérifier que le serveur est redémarré après les changements
- Vérifier que les fichiers de pages existent

### **Upload d'images ne fonctionne pas**
- Normal si UploadThing n'est pas configuré
- Alternative : utiliser des URLs d'images externes pour les tests

---

## 🎯 **Validation Finale**

### **Fonctionnalités de Base** ✅
- [ ] Authentification fonctionne
- [ ] Navigation mise à jour avec nouvelles pages
- [ ] Aucune erreur console critique
- [ ] Design responsive sur mobile

### **Nouvelles Fonctionnalités** ✅
- [ ] Portfolio : Création et affichage
- [ ] Agenda : Gestion des créneaux
- [ ] Carte : Visualisation des zones
- [ ] Partage : Modal et boutons
- [ ] Notifications : Centre fonctionnel

### **Performance** ✅
- [ ] Pages se chargent rapidement (< 3 secondes)
- [ ] Pas de fuites mémoire visibles
- [ ] Images optimisées
- [ ] Interactions fluides

---

## 🚀 **Déploiement en Production**

### **Avant le Déploiement**
- [ ] Build de production réussi : `npm run build`
- [ ] Tests complets effectués
- [ ] Variables d'environnement de production configurées
- [ ] Backup de la base de données effectué

### **Configuration Production**
```env
# Variables spécifiques à la production
NEXTAUTH_URL="https://votre-domaine.com"
DATABASE_URL="********************************/qriblik_prod"
```

### **Après le Déploiement**
- [ ] Migration de base de données appliquée
- [ ] Toutes les pages accessibles
- [ ] Fonctionnalités testées en production
- [ ] Monitoring des erreurs activé
- [ ] Performance vérifiée

---

## 📞 **Support**

En cas de problème :

1. **Vérifier les logs** dans la console du navigateur et le terminal
2. **Consulter la documentation** :
   - `GUIDE_CONFIGURATION.md`
   - `NOUVELLES_FONCTIONNALITES.md`
3. **Tester étape par étape** avec les scripts fournis
4. **Vérifier les variables d'environnement**

---

## 🎉 **Félicitations !**

Une fois cette checklist complétée, votre plateforme QribLik sera équipée de toutes les nouvelles fonctionnalités pour offrir une expérience complète aux artisans marocains !

**Nouvelles capacités débloquées :**
- 📸 **Portfolio professionnel** pour valoriser le savoir-faire
- 📅 **Agenda intelligent** pour optimiser le temps
- 🗺️ **Carte des opportunités** pour maximiser les revenus
- 📱 **Partage social** pour développer la visibilité
- 🔔 **Notifications avancées** pour ne rien manquer

---

*Checklist mise à jour : Septembre 2024*
