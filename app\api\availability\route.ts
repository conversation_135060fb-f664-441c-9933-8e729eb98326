import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const createAvailabilitySchema = z.object({
  dayOfWeek: z.number().min(0).max(6), // 0 = Dimanche, 6 = Samedi
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format d\'heure invalide (HH:MM)'),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format d\'heure invalide (HH:MM)'),
  isAvailable: z.boolean().default(true),
  slotType: z.enum(['WORK', 'BREAK', 'UNAVAILABLE']).default('WORK'),
});

const updateAvailabilitySchema = createAvailabilitySchema.partial();

// GET /api/availability - Récupérer les créneaux de disponibilité
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const dayOfWeek = searchParams.get('dayOfWeek');

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'ID utilisateur requis' },
        { status: 400 }
      );
    }

    const where: any = { userId };
    
    if (dayOfWeek !== null) {
      where.dayOfWeek = parseInt(dayOfWeek);
    }

    const availabilitySlots = await prisma.availabilitySlot.findMany({
      where,
      orderBy: [
        { dayOfWeek: 'asc' },
        { startTime: 'asc' },
      ],
    });

    // Organiser par jour de la semaine
    const organizedSlots = availabilitySlots.reduce((acc, slot) => {
      const day = slot.dayOfWeek;
      if (!acc[day]) {
        acc[day] = [];
      }
      acc[day].push(slot);
      return acc;
    }, {} as Record<number, typeof availabilitySlots>);

    return NextResponse.json({
      success: true,
      data: {
        slots: availabilitySlots,
        organized: organizedSlots,
      },
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des disponibilités:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}

// POST /api/availability - Créer un nouveau créneau de disponibilité
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Non authentifié' },
        { status: 401 }
      );
    }

    // Vérifier que l'utilisateur est un prestataire
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { userType: true },
    });

    if (!user || (user.userType !== 'PRESTATAIRE' && user.userType !== 'BOTH')) {
      return NextResponse.json(
        { success: false, error: 'Seuls les prestataires peuvent gérer leur disponibilité' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = createAvailabilitySchema.parse(body);

    // Vérifier qu'il n'y a pas de conflit avec les créneaux existants
    const conflictingSlots = await prisma.availabilitySlot.findMany({
      where: {
        userId: session.user.id,
        dayOfWeek: validatedData.dayOfWeek,
        OR: [
          {
            AND: [
              { startTime: { lte: validatedData.startTime } },
              { endTime: { gt: validatedData.startTime } },
            ],
          },
          {
            AND: [
              { startTime: { lt: validatedData.endTime } },
              { endTime: { gte: validatedData.endTime } },
            ],
          },
          {
            AND: [
              { startTime: { gte: validatedData.startTime } },
              { endTime: { lte: validatedData.endTime } },
            ],
          },
        ],
      },
    });

    if (conflictingSlots.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Ce créneau entre en conflit avec un créneau existant' },
        { status: 400 }
      );
    }

    const availabilitySlot = await prisma.availabilitySlot.create({
      data: {
        ...validatedData,
        userId: session.user.id,
      },
    });

    return NextResponse.json({
      success: true,
      data: availabilitySlot,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Données invalides', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Erreur lors de la création du créneau:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}

// PUT /api/availability - Mettre à jour les créneaux de disponibilité en masse
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { slots } = body;

    if (!Array.isArray(slots)) {
      return NextResponse.json(
        { success: false, error: 'Format de données invalide' },
        { status: 400 }
      );
    }

    // Supprimer tous les créneaux existants de l'utilisateur
    await prisma.availabilitySlot.deleteMany({
      where: { userId: session.user.id },
    });

    // Créer les nouveaux créneaux
    const newSlots = [];
    for (const slot of slots) {
      const validatedSlot = createAvailabilitySchema.parse(slot);
      newSlots.push({
        ...validatedSlot,
        userId: session.user.id,
      });
    }

    if (newSlots.length > 0) {
      await prisma.availabilitySlot.createMany({
        data: newSlots,
      });
    }

    // Récupérer les créneaux créés
    const createdSlots = await prisma.availabilitySlot.findMany({
      where: { userId: session.user.id },
      orderBy: [
        { dayOfWeek: 'asc' },
        { startTime: 'asc' },
      ],
    });

    return NextResponse.json({
      success: true,
      data: createdSlots,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Données invalides', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Erreur lors de la mise à jour des créneaux:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
