'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Share2, 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin,
  MessageCircle,
  Copy,
  Download,
  Palette,
  Type,
  Image as ImageIcon
} from 'lucide-react';
import { toast } from 'sonner';

interface SocialShareProps {
  title: string;
  description?: string;
  imageUrl?: string;
  url: string;
  hashtags?: string[];
  onShare?: (platform: string) => void;
}

interface SharePlatform {
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  shareUrl: (params: ShareParams) => string;
  supportsImage: boolean;
  maxLength?: number;
}

interface ShareParams {
  url: string;
  text: string;
  hashtags?: string;
  image?: string;
}

const PLATFORMS: SharePlatform[] = [
  {
    name: 'Facebook',
    icon: Facebook,
    color: 'bg-blue-600 hover:bg-blue-700',
    shareUrl: ({ url, text }) => 
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(text)}`,
    supportsImage: true,
  },
  {
    name: 'Twitter',
    icon: Twitter,
    color: 'bg-sky-500 hover:bg-sky-600',
    shareUrl: ({ url, text, hashtags }) => 
      `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}${hashtags ? `&hashtags=${hashtags}` : ''}`,
    supportsImage: true,
    maxLength: 280,
  },
  {
    name: 'LinkedIn',
    icon: Linkedin,
    color: 'bg-blue-700 hover:bg-blue-800',
    shareUrl: ({ url, text }) => 
      `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}&summary=${encodeURIComponent(text)}`,
    supportsImage: true,
  },
  {
    name: 'WhatsApp',
    icon: MessageCircle,
    color: 'bg-green-500 hover:bg-green-600',
    shareUrl: ({ url, text }) => 
      `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`,
    supportsImage: false,
  },
  {
    name: 'Instagram',
    icon: Instagram,
    color: 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600',
    shareUrl: ({ url }) => url, // Instagram ne supporte pas le partage direct d'URL
    supportsImage: true,
  },
];

export function SocialShare({ 
  title, 
  description, 
  imageUrl, 
  url, 
  hashtags = [], 
  onShare 
}: SocialShareProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [customText, setCustomText] = useState(description || title);
  const [customHashtags, setCustomHashtags] = useState(hashtags.join(' '));
  const [selectedTemplate, setSelectedTemplate] = useState('default');

  const templates = {
    default: `${title}\n\n${description || ''}\n\n🔗 Découvrez sur QribLik`,
    professional: `✨ ${title}\n\n${description || ''}\n\n🏆 Service professionnel au Maroc\n📍 Disponible sur QribLik`,
    casual: `Hey ! 👋\n\nRegardez ce que j'ai trouvé : ${title}\n\n${description || ''}\n\n💯 Sur QribLik !`,
    promotional: `🚀 ${title}\n\n${description || ''}\n\n✅ Qualité garantie\n💰 Prix compétitif\n📱 Réservation facile sur QribLik`,
  };

  const handleShare = async (platform: SharePlatform) => {
    const shareText = customText || templates[selectedTemplate as keyof typeof templates];
    const shareParams: ShareParams = {
      url,
      text: shareText,
      hashtags: customHashtags.replace('#', ''),
      image: imageUrl,
    };

    // Cas spécial pour Instagram
    if (platform.name === 'Instagram') {
      if (imageUrl) {
        // Copier le texte dans le presse-papiers pour Instagram
        try {
          await navigator.clipboard.writeText(shareText);
          toast.success('Texte copié ! Collez-le dans votre story Instagram avec l\'image.');
          // Ouvrir Instagram (si sur mobile)
          if (navigator.userAgent.match(/Android|iPhone/i)) {
            window.open('instagram://story-camera', '_blank');
          }
        } catch (error) {
          toast.error('Impossible de copier le texte');
        }
      } else {
        toast.error('Une image est requise pour partager sur Instagram');
        return;
      }
    } else {
      // Partage normal pour les autres plateformes
      const shareUrl = platform.shareUrl(shareParams);
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }

    // Callback pour tracking
    if (onShare) {
      onShare(platform.name.toLowerCase());
    }

    toast.success(`Partage sur ${platform.name} ouvert !`);
    setIsOpen(false);
  };

  const copyToClipboard = async () => {
    const textToCopy = `${customText}\n\n${url}`;
    try {
      await navigator.clipboard.writeText(textToCopy);
      toast.success('Lien copié dans le presse-papiers !');
    } catch (error) {
      toast.error('Impossible de copier le lien');
    }
  };

  const downloadImage = () => {
    if (!imageUrl) {
      toast.error('Aucune image disponible');
      return;
    }

    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `qriblik-${title.replace(/\s+/g, '-').toLowerCase()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('Image téléchargée !');
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Share2 className="w-4 h-4 mr-2" />
          Partager
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Share2 className="w-5 h-5" />
            <span>Partager sur les réseaux sociaux</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Aperçu */}
          <div className="border rounded-lg p-4 bg-gray-50">
            <h3 className="font-semibold mb-2">Aperçu</h3>
            <div className="flex space-x-3">
              {imageUrl && (
                <div className="w-20 h-20 bg-gray-200 rounded-lg flex-shrink-0 overflow-hidden">
                  <img 
                    src={imageUrl} 
                    alt={title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              <div className="flex-1">
                <h4 className="font-medium text-sm">{title}</h4>
                <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                  {customText}
                </p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {customHashtags.split(' ').filter(tag => tag.trim()).map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag.startsWith('#') ? tag : `#${tag}`}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Templates de texte */}
          <div>
            <Label className="text-sm font-medium mb-2 block">
              <Type className="w-4 h-4 inline mr-1" />
              Modèles de texte
            </Label>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(templates).map(([key, template]) => (
                <Button
                  key={key}
                  variant={selectedTemplate === key ? "default" : "outline"}
                  size="sm"
                  onClick={() => {
                    setSelectedTemplate(key);
                    setCustomText(template);
                  }}
                  className="text-xs h-auto py-2 px-3"
                >
                  {key === 'default' && '📝 Standard'}
                  {key === 'professional' && '💼 Professionnel'}
                  {key === 'casual' && '😊 Décontracté'}
                  {key === 'promotional' && '🚀 Promotionnel'}
                </Button>
              ))}
            </div>
          </div>

          {/* Personnalisation du texte */}
          <div>
            <Label htmlFor="customText">Personnaliser le texte</Label>
            <Textarea
              id="customText"
              value={customText}
              onChange={(e) => setCustomText(e.target.value)}
              placeholder="Votre message personnalisé..."
              rows={4}
              className="mt-1"
            />
          </div>

          {/* Hashtags */}
          <div>
            <Label htmlFor="hashtags">Hashtags</Label>
            <Input
              id="hashtags"
              value={customHashtags}
              onChange={(e) => setCustomHashtags(e.target.value)}
              placeholder="#QribLik #Maroc #Services"
              className="mt-1"
            />
            <p className="text-xs text-gray-500 mt-1">
              Séparez les hashtags par des espaces
            </p>
          </div>

          {/* Boutons de partage */}
          <div>
            <h3 className="font-medium mb-3">Choisir une plateforme</h3>
            <div className="grid grid-cols-2 gap-3">
              {PLATFORMS.map((platform) => {
                const Icon = platform.icon;
                return (
                  <Button
                    key={platform.name}
                    onClick={() => handleShare(platform)}
                    className={`${platform.color} text-white justify-start h-12`}
                  >
                    <Icon className="w-5 h-5 mr-3" />
                    <div className="text-left">
                      <div className="font-medium">{platform.name}</div>
                      {platform.maxLength && (
                        <div className="text-xs opacity-90">
                          Max {platform.maxLength} caractères
                        </div>
                      )}
                    </div>
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Actions supplémentaires */}
          <div className="flex space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={copyToClipboard} className="flex-1">
              <Copy className="w-4 h-4 mr-2" />
              Copier le lien
            </Button>
            {imageUrl && (
              <Button variant="outline" onClick={downloadImage} className="flex-1">
                <Download className="w-4 h-4 mr-2" />
                Télécharger l'image
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
