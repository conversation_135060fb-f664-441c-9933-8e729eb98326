import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createPaymentIntent, calculatePlatformFee } from '@/lib/stripe'
import { z } from 'zod'

const createPaymentIntentSchema = z.object({
  matchId: z.string(),
  amount: z.number().positive('Le montant doit être positif'),
  currency: z.string().default('mad'),
  description: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createPaymentIntentSchema.parse(body)

    // Vérifier que le match existe et que l'utilisateur est le client
    const match = await prisma.match.findUnique({
      where: { id: validatedData.matchId },
      include: {
        request: true,
        prestataire: true,
        client: true
      }
    })

    if (!match) {
      return NextResponse.json({ error: 'Match non trouvé' }, { status: 404 })
    }

    if (match.clientId !== session.user.id) {
      return NextResponse.json({ error: 'Accès refusé' }, { status: 403 })
    }

    if (match.status !== 'ACCEPTED') {
      return NextResponse.json({ error: 'Le match doit être accepté pour effectuer un paiement' }, { status: 400 })
    }

    // Calculer les frais de plateforme
    const platformFee = calculatePlatformFee(validatedData.amount)

    // Créer le payment intent
    const paymentIntent = await createPaymentIntent(
      validatedData.amount,
      validatedData.currency,
      {
        matchId: validatedData.matchId,
        clientId: session.user.id,
        prestataireId: match.prestataireId,
        platformFee: platformFee.toString(),
        description: validatedData.description || `Paiement pour ${match.request.title}`
      }
    )

    // Enregistrer la transaction en base
    const payment = await prisma.payment.create({
      data: {
        id: paymentIntent.id,
        matchId: validatedData.matchId,
        clientId: session.user.id,
        prestataireId: match.prestataireId,
        amount: validatedData.amount,
        platformFee,
        currency: validatedData.currency,
        status: 'PENDING',
        paymentMethod: 'STRIPE',
        stripePaymentIntentId: paymentIntent.id,
        description: validatedData.description || `Paiement pour ${match.request.title}`
      }
    })

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      paymentId: payment.id,
      amount: validatedData.amount,
      platformFee
    })

  } catch (error) {
    console.error('Erreur lors de la création du payment intent:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Données invalides', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
