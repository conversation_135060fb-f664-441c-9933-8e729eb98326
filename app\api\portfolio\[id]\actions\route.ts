import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const actionSchema = z.object({
  action: z.enum(['like', 'share']),
});

// POST /api/portfolio/[id]/actions - Actions sur un élément de portfolio
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = actionSchema.parse(body);

    const portfolioItem = await prisma.portfolioItem.findUnique({
      where: { id: params.id },
      select: { id: true, likeCount: true, shareCount: true },
    });

    if (!portfolioItem) {
      return NextResponse.json(
        { success: false, error: 'Élément de portfolio non trouvé' },
        { status: 404 }
      );
    }

    let updateData: any = {};
    let message = '';

    switch (action) {
      case 'like':
        updateData = { likeCount: { increment: 1 } };
        message = 'Like ajouté';
        break;
      case 'share':
        updateData = { shareCount: { increment: 1 } };
        message = 'Partage comptabilisé';
        break;
    }

    const updatedItem = await prisma.portfolioItem.update({
      where: { id: params.id },
      data: updateData,
      select: {
        id: true,
        likeCount: true,
        shareCount: true,
        viewCount: true,
      },
    });

    return NextResponse.json({
      success: true,
      message,
      data: updatedItem,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Action invalide', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Erreur lors de l\'action sur le portfolio:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}

// GET /api/portfolio/[id]/actions - Obtenir les statistiques d'un élément
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const portfolioItem = await prisma.portfolioItem.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        viewCount: true,
        likeCount: true,
        shareCount: true,
        createdAt: true,
      },
    });

    if (!portfolioItem) {
      return NextResponse.json(
        { success: false, error: 'Élément de portfolio non trouvé' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: portfolioItem,
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
