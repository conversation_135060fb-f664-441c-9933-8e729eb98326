import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const paymentsQuerySchema = z.object({
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('10'),
  status: z.enum(['PENDING', 'COMPLETED', 'FAILED', 'CANCELLED']).optional(),
  type: z.enum(['sent', 'received']).optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const { page, limit, status, type } = paymentsQuerySchema.parse({
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
      status: searchParams.get('status'),
      type: searchParams.get('type')
    })

    // Construire les conditions de filtrage
    const where: any = {}
    
    if (type === 'sent') {
      where.clientId = session.user.id
    } else if (type === 'received') {
      where.prestataireId = session.user.id
    } else {
      where.OR = [
        { clientId: session.user.id },
        { prestataireId: session.user.id }
      ]
    }

    if (status) {
      where.status = status
    }

    // Récupérer les paiements avec pagination
    const [payments, total] = await Promise.all([
      prisma.payment.findMany({
        where,
        include: {
          client: {
            select: { id: true, name: true, image: true }
          },
          prestataire: {
            select: { id: true, name: true, image: true }
          },
          match: {
            include: {
              request: {
                select: { id: true, title: true }
              },
              service: {
                select: { id: true, title: true }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.payment.count({ where })
    ])

    return NextResponse.json({
      payments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Erreur lors de la récupération des paiements:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Paramètres invalides', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
