'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, AlertCircle, Loader2, ArrowLeft, ExternalLink } from 'lucide-react'

export default function ConnectAccountPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [status, setStatus] = useState<'loading' | 'success' | 'refresh' | 'error'>('loading')
  const [accountStatus, setAccountStatus] = useState<any>(null)

  const isSuccess = searchParams.get('success') === 'true'
  const isRefresh = searchParams.get('refresh') === 'true'

  useEffect(() => {
    if (isSuccess) {
      setStatus('success')
      checkAccountStatus()
    } else if (isRefresh) {
      setStatus('refresh')
    } else {
      checkAccountStatus()
    }
  }, [isSuccess, isRefresh])

  const checkAccountStatus = async () => {
    try {
      const response = await fetch('/api/payments/connect-account')
      if (response.ok) {
        const data = await response.json()
        setAccountStatus(data)
        
        if (data.canReceivePayments) {
          setStatus('success')
        } else if (data.hasConnectedAccount && data.accountStatus === 'PENDING') {
          setStatus('refresh')
        }
      }
    } catch (error) {
      console.error('Erreur lors de la vérification du statut:', error)
      setStatus('error')
    }
  }

  const handleRetryOnboarding = async () => {
    try {
      const response = await fetch('/api/payments/connect-account', {
        method: 'POST'
      })

      if (response.ok) {
        const data = await response.json()
        window.location.href = data.onboardingUrl
      }
    } catch (error) {
      console.error('Erreur lors de la reprise de l\'onboarding:', error)
    }
  }

  const renderContent = () => {
    switch (status) {
      case 'success':
        return (
          <Card className="max-w-md mx-auto">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle className="text-green-900">Compte configuré avec succès !</CardTitle>
              <CardDescription>
                Votre compte Stripe est maintenant configuré et vous pouvez recevoir des paiements.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Vous pouvez maintenant accepter des paiements pour vos services sur QirbLik.
                </AlertDescription>
              </Alert>
              
              <div className="flex flex-col gap-2">
                <Button onClick={() => router.push('/dashboard/payments')} className="w-full">
                  Voir mes paiements
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => router.push('/dashboard')}
                  className="w-full"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Retour au dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        )

      case 'refresh':
        return (
          <Card className="max-w-md mx-auto">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                <AlertCircle className="h-6 w-6 text-yellow-600" />
              </div>
              <CardTitle className="text-yellow-900">Configuration en cours</CardTitle>
              <CardDescription>
                Votre compte Stripe est en cours de configuration. Vous devez compléter le processus d'onboarding.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Il semble que vous n'ayez pas terminé la configuration de votre compte. 
                  Cliquez sur le bouton ci-dessous pour reprendre le processus.
                </AlertDescription>
              </Alert>
              
              <div className="flex flex-col gap-2">
                <Button onClick={handleRetryOnboarding} className="w-full">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Reprendre la configuration
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => router.push('/dashboard/payments')}
                  className="w-full"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Retour aux paiements
                </Button>
              </div>
            </CardContent>
          </Card>
        )

      case 'error':
        return (
          <Card className="max-w-md mx-auto">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <CardTitle className="text-red-900">Erreur de configuration</CardTitle>
              <CardDescription>
                Une erreur s'est produite lors de la configuration de votre compte.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Impossible de vérifier le statut de votre compte. Veuillez réessayer ou contacter le support.
                </AlertDescription>
              </Alert>
              
              <div className="flex flex-col gap-2">
                <Button onClick={checkAccountStatus} className="w-full">
                  Réessayer
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => router.push('/dashboard/payments')}
                  className="w-full"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Retour aux paiements
                </Button>
              </div>
            </CardContent>
          </Card>
        )

      default:
        return (
          <Card className="max-w-md mx-auto">
            <CardContent className="flex flex-col items-center justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin mb-4" />
              <p className="text-gray-600">Vérification du statut de votre compte...</p>
            </CardContent>
          </Card>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {renderContent()}
      </div>
    </div>
  )
}
