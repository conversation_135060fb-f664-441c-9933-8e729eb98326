const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧪 Configuration des Tests QribLik\n');

// 1. Vérifier les dépendances de test
console.log('📦 Vérification des dépendances de test...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const devDeps = packageJson.devDependencies || {};

const requiredTestDeps = [
  'jest',
  '@testing-library/react',
  '@testing-library/jest-dom',
  '@testing-library/user-event',
  'jest-environment-jsdom',
  '@playwright/test',
  '@types/jest',
  'msw'
];

const missingDeps = requiredTestDeps.filter(dep => !devDeps[dep]);

if (missingDeps.length > 0) {
  console.log('❌ Dépendances manquantes:', missingDeps.join(', '));
  console.log('🔧 Installation des dépendances manquantes...');
  
  try {
    execSync(`npm install --save-dev ${missingDeps.join(' ')}`, { stdio: 'inherit' });
    console.log('✅ Dépendances installées avec succès');
  } catch (error) {
    console.error('❌ Erreur lors de l\'installation:', error.message);
    process.exit(1);
  }
} else {
  console.log('✅ Toutes les dépendances de test sont installées');
}

// 2. Vérifier la structure des dossiers
console.log('\n📁 Vérification de la structure des tests...');
const testDirs = [
  'tests',
  'tests/unit',
  'tests/unit/components',
  'tests/unit/utils',
  'tests/unit/lib',
  'tests/integration',
  'tests/integration/api',
  'tests/integration/auth',
  'tests/e2e',
  'tests/fixtures',
  'tests/mocks',
  'tests/helpers'
];

testDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✅ Créé: ${dir}`);
  } else {
    console.log(`✅ Existe: ${dir}`);
  }
});

// 3. Vérifier les fichiers de configuration
console.log('\n⚙️ Vérification des fichiers de configuration...');
const configFiles = [
  'jest.config.js',
  'jest.setup.js',
  'playwright.config.ts'
];

configFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} existe`);
  } else {
    console.log(`⚠️  ${file} manquant - Créez-le manuellement`);
  }
});

// 4. Installer Playwright browsers
console.log('\n🎭 Installation des navigateurs Playwright...');
try {
  execSync('npx playwright install', { stdio: 'inherit' });
  console.log('✅ Navigateurs Playwright installés');
} catch (error) {
  console.log('⚠️  Erreur lors de l\'installation de Playwright:', error.message);
  console.log('💡 Exécutez manuellement: npx playwright install');
}

// 5. Créer des exemples de tests si nécessaire
console.log('\n📝 Vérification des tests d\'exemple...');
const exampleTests = [
  'tests/unit/components/Portfolio.test.tsx',
  'tests/integration/api/portfolio.test.ts',
  'tests/e2e/auth.spec.ts'
];

let exampleCount = 0;
exampleTests.forEach(testFile => {
  if (fs.existsSync(testFile)) {
    exampleCount++;
    console.log(`✅ ${testFile} existe`);
  } else {
    console.log(`⚠️  ${testFile} manquant`);
  }
});

// 6. Vérifier les scripts npm
console.log('\n🚀 Vérification des scripts de test...');
const requiredScripts = [
  'test',
  'test:unit',
  'test:integration',
  'test:e2e',
  'test:coverage'
];

const scripts = packageJson.scripts || {};
const missingScripts = requiredScripts.filter(script => !scripts[script]);

if (missingScripts.length > 0) {
  console.log('⚠️  Scripts manquants:', missingScripts.join(', '));
  console.log('💡 Ajoutez-les dans package.json');
} else {
  console.log('✅ Tous les scripts de test sont configurés');
}

// 7. Test de base
console.log('\n🧪 Test de configuration...');
try {
  // Test Jest
  console.log('Testing Jest configuration...');
  execSync('npx jest --version', { stdio: 'pipe' });
  console.log('✅ Jest configuré correctement');
  
  // Test Playwright
  console.log('Testing Playwright configuration...');
  execSync('npx playwright --version', { stdio: 'pipe' });
  console.log('✅ Playwright configuré correctement');
  
} catch (error) {
  console.log('⚠️  Problème de configuration détecté');
  console.log('💡 Vérifiez les fichiers de configuration');
}

// 8. Résumé et prochaines étapes
console.log('\n📊 RÉSUMÉ DE LA CONFIGURATION:');
console.log(`  📦 Dépendances: ${missingDeps.length === 0 ? '✅' : '⚠️'} ${missingDeps.length === 0 ? 'OK' : missingDeps.length + ' manquantes'}`);
console.log(`  📁 Structure: ✅ OK`);
console.log(`  ⚙️ Configuration: ${configFiles.every(f => fs.existsSync(f)) ? '✅' : '⚠️'} ${configFiles.every(f => fs.existsSync(f)) ? 'OK' : 'Fichiers manquants'}`);
console.log(`  📝 Tests d'exemple: ${exampleCount}/${exampleTests.length} présents`);
console.log(`  🚀 Scripts: ${missingScripts.length === 0 ? '✅' : '⚠️'} ${missingScripts.length === 0 ? 'OK' : missingScripts.length + ' manquants'}`);

console.log('\n🎯 PROCHAINES ÉTAPES:');
console.log('  1. Exécuter les tests unitaires: npm run test:unit');
console.log('  2. Exécuter les tests E2E: npm run test:e2e');
console.log('  3. Vérifier la couverture: npm run test:coverage');
console.log('  4. Consulter le guide: GUIDE_TESTS.md');

console.log('\n💡 CONSEILS:');
console.log('  - Commencez par écrire des tests pour les fonctions critiques');
console.log('  - Maintenez une couverture de code > 70%');
console.log('  - Exécutez les tests avant chaque commit');
console.log('  - Consultez GUIDE_TESTS.md pour les bonnes pratiques');

console.log('\n🎉 Configuration des tests terminée !');
console.log('📚 Consultez GUIDE_TESTS.md pour commencer à écrire des tests.');

// 9. Créer un fichier de statut
const statusFile = {
  setupDate: new Date().toISOString(),
  version: '1.0.0',
  dependencies: {
    jest: devDeps.jest || 'not installed',
    playwright: devDeps['@playwright/test'] || 'not installed',
    testingLibrary: devDeps['@testing-library/react'] || 'not installed'
  },
  structure: 'complete',
  examples: `${exampleCount}/${exampleTests.length}`,
  status: missingDeps.length === 0 && missingScripts.length === 0 ? 'ready' : 'needs-attention'
};

fs.writeFileSync('tests/setup-status.json', JSON.stringify(statusFile, null, 2));
console.log('\n📄 Statut sauvegardé dans tests/setup-status.json');
