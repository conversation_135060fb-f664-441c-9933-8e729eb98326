
// Test d'authentification rapide
import { signIn } from 'next-auth/react';

async function testAuth() {
  try {
    const result = await signIn('credentials', {
      email: '<EMAIL>',
      password: 'password123',
      redirect: false,
    });
    
    console.log('Résultat de connexion:', result);
    
    if (result?.ok) {
      console.log('✅ Authentification réussie');
      window.location.href = '/dashboard';
    } else {
      console.log('❌ Échec de l'authentification:', result?.error);
    }
  } catch (error) {
    console.error('❌ Erreur:', error);
  }
}

// Exécuter le test
testAuth();
