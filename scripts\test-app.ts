import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testDatabase() {
  console.log('🧪 Testing database connection...')
  
  try {
    // Test connection
    await prisma.$queryRaw`SELECT 1`
    console.log('✅ Database connection successful')
    
    // Test data retrieval
    const userCount = await prisma.user.count()
    const serviceCount = await prisma.service.count()
    const requestCount = await prisma.request.count()
    const matchCount = await prisma.match.count()
    
    console.log(`📊 Data summary:`)
    console.log(`   - Users: ${userCount}`)
    console.log(`   - Services: ${serviceCount}`)
    console.log(`   - Requests: ${requestCount}`)
    console.log(`   - Matches: ${matchCount}`)
    
    // Test specific queries
    const services = await prisma.service.findMany({
      take: 3,
      include: {
        user: {
          select: {
            name: true,
            rating: true
          }
        }
      }
    })
    
    console.log('🔍 Sample services:')
    services.forEach(service => {
      console.log(`   - ${service.title} by ${service.user.name} (${service.user.rating}⭐)`)
    })
    
    return true
  } catch (error) {
    console.error('❌ Database test failed:', error)
    return false
  }
}

async function testAPI() {
  console.log('🌐 Testing API endpoints...')
  
  const baseUrl = 'http://localhost:3000'
  const endpoints = [
    '/api/services',
    '/api/requests',
    '/api/auth/session'
  ]
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${baseUrl}${endpoint}`)
      if (response.ok) {
        console.log(`✅ ${endpoint} - OK`)
      } else {
        console.log(`❌ ${endpoint} - ${response.status}`)
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - Error: ${error}`)
    }
  }
}

async function main() {
  console.log('🚀 Starting QribLik application tests...\n')
  
  // Test database
  const dbSuccess = await testDatabase()
  console.log('')
  
  // Test API (only if server is running)
  try {
    await testAPI()
  } catch (error) {
    console.log('⚠️  API tests skipped (server not running)')
  }
  
  console.log('\n📋 Test Summary:')
  console.log(`   Database: ${dbSuccess ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`   API: ⚠️  Manual test required`)
  
  console.log('\n🎯 Next steps:')
  console.log('   1. Start the development server: npm run dev')
  console.log('   2. Visit http://localhost:3000/test for full tests')
  console.log('   3. Test the application manually:')
  console.log('      - Homepage: http://localhost:3000')
  console.log('      - Services: http://localhost:3000/services')
  console.log('      - Requests: http://localhost:3000/requests')
  console.log('      - Dashboard: http://localhost:3000/dashboard')
  console.log('      - Auth: http://localhost:3000/auth/signin')
  
  await prisma.$disconnect()
}

main().catch(console.error)
