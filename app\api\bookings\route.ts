import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const createBookingSchema = z.object({
  matchId: z.string().min(1, 'ID du match requis'),
  scheduledDate: z.string().min(1, 'Date requise'),
  scheduledTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format d\'heure invalide (HH:MM)'),
  duration: z.number().min(15, 'Durée minimale de 15 minutes'),
  notes: z.string().optional(),
});

const updateBookingSchema = z.object({
  scheduledDate: z.string().optional(),
  scheduledTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format d\'heure invalide (HH:MM)').optional(),
  duration: z.number().min(15, 'Durée minimale de 15 minutes').optional(),
  status: z.enum(['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).optional(),
  notes: z.string().optional(),
});

// GET /api/bookings - Récupérer les réservations
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const upcoming = searchParams.get('upcoming') === 'true';
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    const where: any = {
      match: {
        OR: [
          { clientId: session.user.id },
          { prestataireId: session.user.id },
        ],
      },
    };

    if (status) {
      where.status = status;
    }

    if (upcoming) {
      where.scheduledDate = {
        gte: new Date(),
      };
    }

    const bookings = await prisma.booking.findMany({
      where,
      include: {
        match: {
          include: {
            client: {
              select: {
                id: true,
                name: true,
                image: true,
                phone: true,
              },
            },
            prestataire: {
              select: {
                id: true,
                name: true,
                image: true,
                phone: true,
                rating: true,
                reviewCount: true,
              },
            },
            request: {
              select: {
                id: true,
                title: true,
                category: true,
                locationAddress: true,
                locationCity: true,
              },
            },
            service: {
              select: {
                id: true,
                title: true,
                category: true,
              },
            },
          },
        },
      },
      orderBy: [
        { scheduledDate: 'asc' },
        { scheduledTime: 'asc' },
      ],
      take: limit,
      skip: offset,
    });

    return NextResponse.json({
      success: true,
      data: bookings,
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des réservations:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}

// POST /api/bookings - Créer une nouvelle réservation
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = createBookingSchema.parse(body);

    // Vérifier que le match existe et que l'utilisateur y participe
    const match = await prisma.match.findUnique({
      where: { id: validatedData.matchId },
      select: {
        id: true,
        clientId: true,
        prestataireId: true,
        status: true,
      },
    });

    if (!match) {
      return NextResponse.json(
        { success: false, error: 'Match non trouvé' },
        { status: 404 }
      );
    }

    if (match.clientId !== session.user.id && match.prestataireId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: 'Non autorisé' },
        { status: 403 }
      );
    }

    if (match.status !== 'ACCEPTED') {
      return NextResponse.json(
        { success: false, error: 'Le match doit être accepté avant de pouvoir réserver' },
        { status: 400 }
      );
    }

    // Vérifier qu'il n'y a pas déjà une réservation pour ce match
    const existingBooking = await prisma.booking.findUnique({
      where: { matchId: validatedData.matchId },
    });

    if (existingBooking) {
      return NextResponse.json(
        { success: false, error: 'Une réservation existe déjà pour ce match' },
        { status: 400 }
      );
    }

    // Vérifier la disponibilité du prestataire
    const scheduledDate = new Date(validatedData.scheduledDate);
    const dayOfWeek = scheduledDate.getDay();

    const availabilitySlots = await prisma.availabilitySlot.findMany({
      where: {
        userId: match.prestataireId,
        dayOfWeek,
        isAvailable: true,
        slotType: 'WORK',
      },
    });

    const isAvailable = availabilitySlots.some(slot => {
      return validatedData.scheduledTime >= slot.startTime && 
             validatedData.scheduledTime < slot.endTime;
    });

    if (!isAvailable) {
      return NextResponse.json(
        { success: false, error: 'Le prestataire n\'est pas disponible à cette heure' },
        { status: 400 }
      );
    }

    const booking = await prisma.booking.create({
      data: {
        matchId: validatedData.matchId,
        scheduledDate: new Date(validatedData.scheduledDate),
        scheduledTime: validatedData.scheduledTime,
        duration: validatedData.duration,
        notes: validatedData.notes,
      },
      include: {
        match: {
          include: {
            client: {
              select: {
                id: true,
                name: true,
                image: true,
                phone: true,
              },
            },
            prestataire: {
              select: {
                id: true,
                name: true,
                image: true,
                phone: true,
                rating: true,
                reviewCount: true,
              },
            },
            request: {
              select: {
                id: true,
                title: true,
                category: true,
                locationAddress: true,
                locationCity: true,
              },
            },
          },
        },
      },
    });

    // Mettre à jour le statut du match
    await prisma.match.update({
      where: { id: validatedData.matchId },
      data: { status: 'IN_PROGRESS' },
    });

    // Créer des notifications pour les deux parties
    await prisma.notification.createMany({
      data: [
        {
          title: 'Réservation confirmée',
          message: `Votre réservation pour le ${scheduledDate.toLocaleDateString()} à ${validatedData.scheduledTime} a été confirmée.`,
          notificationType: 'BOOKING_CREATED',
          userId: match.clientId,
          relatedId: booking.id,
          relatedType: 'BOOKING',
        },
        {
          title: 'Nouvelle réservation',
          message: `Une nouvelle réservation a été créée pour le ${scheduledDate.toLocaleDateString()} à ${validatedData.scheduledTime}.`,
          notificationType: 'BOOKING_CREATED',
          userId: match.prestataireId,
          relatedId: booking.id,
          relatedType: 'BOOKING',
        },
      ],
    });

    return NextResponse.json({
      success: true,
      data: booking,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Données invalides', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Erreur lors de la création de la réservation:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
