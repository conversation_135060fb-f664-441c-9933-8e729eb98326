import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const updatePortfolioSchema = z.object({
  title: z.string().min(1, 'Le titre est requis').optional(),
  description: z.string().optional(),
  category: z.string().min(1, 'La catégorie est requise').optional(),
  images: z.array(z.string()).optional(),
  videos: z.array(z.string()).optional(),
  beforeImages: z.array(z.string()).optional(),
  afterImages: z.array(z.string()).optional(),
  projectDate: z.string().optional(),
  duration: z.string().optional(),
  clientTestimonial: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().optional(),
  isFeatured: z.boolean().optional(),
  locationAddress: z.string().optional(),
  locationCity: z.string().optional(),
  locationRegion: z.string().optional(),
});

// GET /api/portfolio/[id] - Récupérer un élément de portfolio
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const portfolioItem = await prisma.portfolioItem.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            rating: true,
            reviewCount: true,
            locationCity: true,
            locationRegion: true,
            phone: true,
            bio: true,
          },
        },
      },
    });

    if (!portfolioItem) {
      return NextResponse.json(
        { success: false, error: 'Élément de portfolio non trouvé' },
        { status: 404 }
      );
    }

    // Incrémenter le compteur de vues
    await prisma.portfolioItem.update({
      where: { id: params.id },
      data: { viewCount: { increment: 1 } },
    });

    return NextResponse.json({
      success: true,
      data: {
        ...portfolioItem,
        images: portfolioItem.images ? portfolioItem.images.split(',') : [],
        videos: portfolioItem.videos ? portfolioItem.videos.split(',') : [],
        beforeImages: portfolioItem.beforeImages ? portfolioItem.beforeImages.split(',') : [],
        afterImages: portfolioItem.afterImages ? portfolioItem.afterImages.split(',') : [],
        tags: portfolioItem.tags ? portfolioItem.tags.split(',') : [],
      },
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du portfolio:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}

// PUT /api/portfolio/[id] - Mettre à jour un élément de portfolio
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const portfolioItem = await prisma.portfolioItem.findUnique({
      where: { id: params.id },
      select: { userId: true },
    });

    if (!portfolioItem) {
      return NextResponse.json(
        { success: false, error: 'Élément de portfolio non trouvé' },
        { status: 404 }
      );
    }

    if (portfolioItem.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: 'Non autorisé' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = updatePortfolioSchema.parse(body);

    const updateData: any = {};
    
    if (validatedData.title !== undefined) updateData.title = validatedData.title;
    if (validatedData.description !== undefined) updateData.description = validatedData.description;
    if (validatedData.category !== undefined) updateData.category = validatedData.category;
    if (validatedData.images !== undefined) updateData.images = validatedData.images.join(',');
    if (validatedData.videos !== undefined) updateData.videos = validatedData.videos.join(',');
    if (validatedData.beforeImages !== undefined) updateData.beforeImages = validatedData.beforeImages.join(',');
    if (validatedData.afterImages !== undefined) updateData.afterImages = validatedData.afterImages.join(',');
    if (validatedData.projectDate !== undefined) updateData.projectDate = validatedData.projectDate ? new Date(validatedData.projectDate) : null;
    if (validatedData.duration !== undefined) updateData.duration = validatedData.duration;
    if (validatedData.clientTestimonial !== undefined) updateData.clientTestimonial = validatedData.clientTestimonial;
    if (validatedData.tags !== undefined) updateData.tags = validatedData.tags.join(',');
    if (validatedData.isPublic !== undefined) updateData.isPublic = validatedData.isPublic;
    if (validatedData.isFeatured !== undefined) updateData.isFeatured = validatedData.isFeatured;
    if (validatedData.locationAddress !== undefined) updateData.locationAddress = validatedData.locationAddress;
    if (validatedData.locationCity !== undefined) updateData.locationCity = validatedData.locationCity;
    if (validatedData.locationRegion !== undefined) updateData.locationRegion = validatedData.locationRegion;

    const updatedItem = await prisma.portfolioItem.update({
      where: { id: params.id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            rating: true,
            reviewCount: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        ...updatedItem,
        images: updatedItem.images ? updatedItem.images.split(',') : [],
        videos: updatedItem.videos ? updatedItem.videos.split(',') : [],
        beforeImages: updatedItem.beforeImages ? updatedItem.beforeImages.split(',') : [],
        afterImages: updatedItem.afterImages ? updatedItem.afterImages.split(',') : [],
        tags: updatedItem.tags ? updatedItem.tags.split(',') : [],
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Données invalides', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Erreur lors de la mise à jour du portfolio:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}

// DELETE /api/portfolio/[id] - Supprimer un élément de portfolio
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const portfolioItem = await prisma.portfolioItem.findUnique({
      where: { id: params.id },
      select: { userId: true },
    });

    if (!portfolioItem) {
      return NextResponse.json(
        { success: false, error: 'Élément de portfolio non trouvé' },
        { status: 404 }
      );
    }

    if (portfolioItem.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: 'Non autorisé' },
        { status: 403 }
      );
    }

    await prisma.portfolioItem.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: 'Élément de portfolio supprimé avec succès',
    });
  } catch (error) {
    console.error('Erreur lors de la suppression du portfolio:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
