'use client'

import { useState, useEffect } from 'react'
import { Head<PERSON> } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Search, MapPin, Clock, User, Filter, Plus, AlertCircle } from 'lucide-react'
import Link from 'next/link'
import { motion } from 'framer-motion'

interface Request {
  id: string
  title: string
  description: string
  category: string
  subCategory?: string
  tags: string
  budget?: number
  budgetType: string
  urgency: string
  deadline?: string
  images: string
  status: string
  viewCount: number
  proposalCount: number
  locationCity?: string
  locationRegion?: string
  client: {
    id: string
    name: string
    image?: string
    rating?: number
    reviewCount: number
    locationCity?: string
    locationRegion?: string
  }
  createdAt: string
}

const categories = [
  { value: 'all', label: 'Toutes les catégories' },
  { value: 'BRICOLAGE_REPARATIONS', label: 'Bricolage & Réparations' },
  { value: 'MENAGE_NETTOYAGE', label: 'Ménage & Nettoyage' },
  { value: 'JARDINAGE_ESPACES_VERTS', label: 'Jardinage & Espaces verts' },
  { value: 'DEMENAGEMENT_TRANSPORT', label: 'Déménagement & Transport' },
  { value: 'COURS_PARTICULIERS', label: 'Cours particuliers' },
  { value: 'SERVICES_PERSONNE', label: 'Services à la personne' },
  { value: 'EVENEMENTS_ANIMATION', label: 'Événements & Animation' },
  { value: 'BEAUTE_BIEN_ETRE', label: 'Beauté & Bien-être' },
  { value: 'INFORMATIQUE_TECH', label: 'Informatique & Tech' },
  { value: 'AUTOMOBILE', label: 'Automobile' },
  { value: 'IMMOBILIER', label: 'Immobilier' },
  { value: 'AUTRES', label: 'Autres' }
]

const cities = [
  { value: 'all', label: 'Toutes les villes' },
  { value: 'Casablanca', label: 'Casablanca' },
  { value: 'Rabat', label: 'Rabat' },
  { value: 'Marrakech', label: 'Marrakech' },
  { value: 'Fès', label: 'Fès' },
  { value: 'Agadir', label: 'Agadir' },
  { value: 'Tanger', label: 'Tanger' },
  { value: 'Meknès', label: 'Meknès' },
  { value: 'Oujda', label: 'Oujda' },
  { value: 'Kénitra', label: 'Kénitra' },
  { value: 'Tétouan', label: 'Tétouan' }
]

const urgencyLevels = [
  { value: 'LOW', label: 'Faible', color: 'bg-green-100 text-green-800' },
  { value: 'NORMAL', label: 'Normal', color: 'bg-blue-100 text-blue-800' },
  { value: 'HIGH', label: 'Élevé', color: 'bg-orange-100 text-orange-800' },
  { value: 'EMERGENCY', label: 'Urgent', color: 'bg-red-100 text-red-800' }
]

export default function RequestsPage() {
  const [requests, setRequests] = useState<Request[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedCity, setSelectedCity] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  const fetchRequests = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12'
      })
      
      if (searchQuery) params.append('search', searchQuery)
      if (selectedCategory && selectedCategory !== 'all') params.append('category', selectedCategory)
      if (selectedCity && selectedCity !== 'all') params.append('city', selectedCity)

      const response = await fetch(`/api/requests?${params}`)
      const data = await response.json()
      
      setRequests(data.requests || [])
      setTotalPages(data.pagination?.pages || 1)
    } catch (error) {
      console.error('Error fetching requests:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchRequests()
  }, [currentPage, searchQuery, selectedCategory, selectedCity])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchRequests()
  }

  const getCategoryLabel = (category: string) => {
    return categories.find(c => c.value === category)?.label || category
  }

  const getBudgetDisplay = (budget?: number, budgetType?: string) => {
    if (!budget) return 'Budget non spécifié'
    return `${budget} MAD ${budgetType === 'HOURLY' ? '/heure' : budgetType === 'DAILY' ? '/jour' : ''}`
  }

  const getUrgencyInfo = (urgency: string) => {
    return urgencyLevels.find(u => u.value === urgency) || urgencyLevels[1]
  }

  const getImageUrl = (images: string) => {
    const imageList = images.split(',').filter(Boolean)
    return imageList[0] || '/placeholder-request.jpg'
  }

  const getTags = (tags: string) => {
    return tags.split(',').filter(Boolean).slice(0, 3)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="min-h-screen bg-muted/30">
      <Header />
      
      <main className="container py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-4">Demandes de services</h1>
          <p className="text-xl text-muted-foreground">
            Trouvez des clients qui ont besoin de vos services
          </p>
        </div>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Recherche et filtres
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Rechercher une demande..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Catégorie" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes les catégories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedCity} onValueChange={setSelectedCity}>
                  <SelectTrigger>
                    <SelectValue placeholder="Ville" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes les villes</SelectItem>
                    {cities.map((city) => (
                      <SelectItem key={city.value} value={city.value}>
                        {city.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button type="submit" className="w-full">
                  Rechercher
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Requests Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-20 bg-muted rounded mb-4"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-muted rounded"></div>
                    <div className="h-3 bg-muted rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : requests.length === 0 ? (
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-muted-foreground mb-4">
                <Search className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Aucune demande trouvée</h3>
                <p>Essayez de modifier vos critères de recherche</p>
              </div>
              <Button asChild>
                <Link href="/requests/new">
                  <Plus className="mr-2 h-4 w-4" />
                  Créer une demande
                </Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {requests.map((request, index) => {
                const urgencyInfo = getUrgencyInfo(request.urgency)
                return (
                  <motion.div
                    key={request.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer group">
                      <div className="aspect-video overflow-hidden rounded-t-lg">
                        <img
                          src={getImageUrl(request.images)}
                          alt={request.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                      
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between mb-2">
                          <Badge variant="secondary">
                            {getCategoryLabel(request.category)}
                          </Badge>
                          <Badge className={urgencyInfo.color}>
                            {urgencyInfo.label}
                          </Badge>
                        </div>
                        
                        <CardTitle className="text-lg line-clamp-2">
                          {request.title}
                        </CardTitle>
                        
                        <CardDescription className="line-clamp-2">
                          {request.description}
                        </CardDescription>
                      </CardHeader>

                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          {/* Budget */}
                          <div className="text-lg font-semibold text-primary">
                            {getBudgetDisplay(request.budget, request.budgetType)}
                          </div>

                          {/* Location */}
                          {request.locationCity && (
                            <div className="flex items-center text-sm text-muted-foreground">
                              <MapPin className="h-4 w-4 mr-1" />
                              {request.locationCity}
                              {request.locationRegion && `, ${request.locationRegion}`}
                            </div>
                          )}

                          {/* Client Info */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                <User className="h-4 w-4 text-primary" />
                              </div>
                              <div>
                                <p className="text-sm font-medium">{request.client.name}</p>
                                <p className="text-xs text-muted-foreground">
                                  {formatDate(request.createdAt)}
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* Tags */}
                          <div className="flex flex-wrap gap-1">
                            {getTags(request.tags).map((tag, i) => (
                              <Badge key={i} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>

                          {/* Stats */}
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>{request.viewCount} vues</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <AlertCircle className="h-3 w-3" />
                              <span>{request.proposalCount} propositions</span>
                            </div>
                          </div>

                          {/* Deadline */}
                          {request.deadline && (
                            <div className="text-xs text-muted-foreground">
                              <Clock className="h-3 w-3 inline mr-1" />
                              Échéance : {formatDate(request.deadline)}
                            </div>
                          )}

                          {/* Action Button */}
                          <Button asChild className="w-full">
                            <Link href={`/requests/${request.id}`}>
                              Voir la demande
                            </Link>
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )
              })}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Précédent
                </Button>
                
                <div className="flex items-center space-x-1">
                  {[...Array(Math.min(5, totalPages))].map((_, i) => {
                    const page = i + 1
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                      >
                        {page}
                      </Button>
                    )
                  })}
                </div>

                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Suivant
                </Button>
              </div>
            )}
          </>
        )}
      </main>

      <Footer />
    </div>
  )
}
