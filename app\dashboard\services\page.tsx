'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Eye, 
  Trash2, 
  MoreHorizontal,
  MapPin,
  Clock,
  DollarSign,
  TrendingUp,
  Users
} from 'lucide-react'
import Link from 'next/link'
import { motion } from 'framer-motion'

interface Service {
  id: string
  title: string
  description: string
  category: string
  price?: number
  priceType: string
  status: string
  viewCount: number
  contactCount: number
  locationCity?: string
  images: string
  createdAt: string
}

const categories = [
  { value: 'all', label: 'Toutes les catégories' },
  { value: 'BRICOLAGE_REPARATIONS', label: 'Bricolage & Réparations' },
  { value: 'MENAGE_NETTOYAGE', label: 'Ménage & Nettoyage' },
  { value: 'JARDINAGE_ESPACES_VERTS', label: 'Jardinage & Espaces verts' },
  { value: 'DEMENAGEMENT_TRANSPORT', label: 'Déménagement & Transport' },
  { value: 'COURS_PARTICULIERS', label: 'Cours particuliers' },
  { value: 'SERVICES_PERSONNE', label: 'Services à la personne' },
  { value: 'EVENEMENTS_ANIMATION', label: 'Événements & Animation' },
  { value: 'BEAUTE_BIEN_ETRE', label: 'Beauté & Bien-être' },
  { value: 'INFORMATIQUE_TECH', label: 'Informatique & Tech' },
  { value: 'AUTOMOBILE', label: 'Automobile' },
  { value: 'IMMOBILIER', label: 'Immobilier' },
  { value: 'AUTRES', label: 'Autres' }
]

const statusOptions = [
  { value: 'all', label: 'Tous les statuts' },
  { value: 'ACTIVE', label: 'Actif', color: 'bg-green-100 text-green-800' },
  { value: 'PAUSED', label: 'En pause', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'DRAFT', label: 'Brouillon', color: 'bg-gray-100 text-gray-800' },
  { value: 'ARCHIVED', label: 'Archivé', color: 'bg-red-100 text-red-800' }
]

export default function DashboardServicesPage() {
  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true)
        const params = new URLSearchParams()
        
        if (searchQuery) params.append('search', searchQuery)
        if (selectedCategory && selectedCategory !== 'all') params.append('category', selectedCategory)
        if (selectedStatus && selectedStatus !== 'all') params.append('status', selectedStatus)

        const response = await fetch(`/api/dashboard/services?${params}`)
        const data = await response.json()
        
        setServices(data.services || [])
      } catch (error) {
        console.error('Error fetching services:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchServices()
  }, [searchQuery, selectedCategory, selectedStatus])

  const getCategoryLabel = (category: string) => {
    return categories.find(c => c.value === category)?.label || category
  }

  const getStatusInfo = (status: string) => {
    return statusOptions.find(s => s.value === status) || statusOptions[0]
  }

  const getPriceDisplay = (price?: number, priceType?: string) => {
    if (!price) return 'Sur devis'
    return `${price} MAD ${priceType === 'HOURLY' ? '/heure' : priceType === 'DAILY' ? '/jour' : ''}`
  }

  const getImageUrl = (images: string) => {
    const imageList = images.split(',').filter(Boolean)
    return imageList[0] || '/placeholder-service.jpg'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const handleStatusChange = async (serviceId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/dashboard/services/${serviceId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        setServices(services.map(service => 
          service.id === serviceId 
            ? { ...service, status: newStatus }
            : service
        ))
      }
    } catch (error) {
      console.error('Error updating service status:', error)
    }
  }

  return (
    <div className="min-h-screen bg-muted/30">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Mes services</h1>
              <p className="text-muted-foreground">
                Gérez vos services et suivez leurs performances
              </p>
            </div>
            <Button asChild>
              <Link href="/dashboard/services/new">
                <Plus className="mr-2 h-4 w-4" />
                Nouveau service
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <div className="container py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total services</p>
                  <p className="text-2xl font-bold">12</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Vues totales</p>
                  <p className="text-2xl font-bold">1,234</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Eye className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Contacts</p>
                  <p className="text-2xl font-bold">89</p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Users className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Revenus</p>
                  <p className="text-2xl font-bold">2,450 MAD</p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filtres et recherche
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Rechercher un service..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Catégorie" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Statut" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button variant="outline" className="w-full">
                <TrendingUp className="mr-2 h-4 w-4" />
                Statistiques
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Services Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="h-48 bg-muted rounded-t-lg"></div>
                <CardHeader>
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-3 bg-muted rounded"></div>
                    <div className="h-3 bg-muted rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : services.length === 0 ? (
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-muted-foreground mb-4">
                <Users className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Aucun service trouvé</h3>
                <p>Commencez par créer votre premier service</p>
              </div>
              <Button asChild>
                <Link href="/dashboard/services/new">
                  <Plus className="mr-2 h-4 w-4" />
                  Créer un service
                </Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service, index) => {
              const statusInfo = getStatusInfo(service.status)
              return (
                <motion.div
                  key={service.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow group">
                    <div className="aspect-video overflow-hidden rounded-t-lg relative">
                      <img
                        src={getImageUrl(service.images)}
                        alt={service.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-3 right-3">
                        <Badge className={statusInfo.color}>
                          {statusInfo.label}
                        </Badge>
                      </div>
                    </div>
                    
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between mb-2">
                        <Badge variant="secondary">
                          {getCategoryLabel(service.category)}
                        </Badge>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <CardTitle className="text-lg line-clamp-2">
                        {service.title}
                      </CardTitle>
                      
                      <CardDescription className="line-clamp-2">
                        {service.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <div className="space-y-3">
                        {/* Price */}
                        <div className="text-xl font-bold text-primary">
                          {getPriceDisplay(service.price, service.priceType)}
                        </div>

                        {/* Location */}
                        {service.locationCity && (
                          <div className="flex items-center text-sm text-muted-foreground">
                            <MapPin className="h-4 w-4 mr-1" />
                            {service.locationCity}
                          </div>
                        )}

                        {/* Stats */}
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Eye className="h-3 w-3" />
                            <span>{service.viewCount} vues</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Users className="h-3 w-3" />
                            <span>{service.contactCount} contacts</span>
                          </div>
                        </div>

                        {/* Date */}
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Clock className="h-3 w-3 mr-1" />
                          Créé le {formatDate(service.createdAt)}
                        </div>

                        {/* Actions */}
                        <div className="flex space-x-2 pt-2">
                          <Button asChild size="sm" variant="outline" className="flex-1">
                            <Link href={`/services/${service.id}`}>
                              <Eye className="mr-2 h-3 w-3" />
                              Voir
                            </Link>
                          </Button>
                          <Button asChild size="sm" className="flex-1">
                            <Link href={`/dashboard/services/${service.id}/edit`}>
                              <Edit className="mr-2 h-3 w-3" />
                              Modifier
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}
