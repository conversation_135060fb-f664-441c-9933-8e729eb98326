'use client'

import { useEffect, useRef } from 'react'
import { io, Socket } from 'socket.io-client'

export const useSocket = () => {
  const socketRef = useRef<Socket | null>(null)

  useEffect(() => {
    const socketInitializer = async () => {
      await fetch('/api/socket/io')
      
      socketRef.current = io(process.env.NODE_ENV === 'production' 
        ? process.env.NEXT_PUBLIC_SITE_URL || '' 
        : 'http://localhost:3000', {
        path: '/api/socket/io',
        addTrailingSlash: false,
      })

      socketRef.current.on('connect', () => {
        console.log('Connecté au serveur Socket.IO')
      })

      socketRef.current.on('disconnect', () => {
        console.log('Déconnecté du serveur Socket.IO')
      })
    }

    socketInitializer()

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect()
      }
    }
  }, [])

  return socketRef.current
}
