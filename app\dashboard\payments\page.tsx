'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CreditCard, 
  ArrowUpRight, 
  ArrowDownLeft, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  ExternalLink,
  Wallet,
  TrendingUp
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { fr } from 'date-fns/locale'

interface Payment {
  id: string
  amount: number
  platformFee: number
  currency: string
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  paymentMethod: string
  description: string
  createdAt: string
  completedAt?: string
  client: {
    id: string
    name: string
    image?: string
  }
  prestataire: {
    id: string
    name: string
    image?: string
  }
  match: {
    request: {
      id: string
      title: string
    }
    service?: {
      id: string
      title: string
    }
  }
}

interface PaymentsResponse {
  payments: Payment[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

interface ConnectAccountStatus {
  hasConnectedAccount: boolean
  accountStatus?: string
  canReceivePayments?: boolean
}

export default function PaymentsPage() {
  const { data: session } = useSession()
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('all')
  const [connectStatus, setConnectStatus] = useState<ConnectAccountStatus | null>(null)
  const [connectLoading, setConnectLoading] = useState(false)

  useEffect(() => {
    if (session?.user?.id) {
      fetchPayments()
      fetchConnectStatus()
    }
  }, [session, activeTab])

  const fetchPayments = async () => {
    try {
      const params = new URLSearchParams({
        page: '1',
        limit: '20'
      })

      if (activeTab !== 'all') {
        params.append('type', activeTab)
      }

      const response = await fetch(`/api/payments?${params}`)
      if (response.ok) {
        const data: PaymentsResponse = await response.json()
        setPayments(data.payments)
      }
    } catch (error) {
      console.error('Erreur lors du chargement des paiements:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchConnectStatus = async () => {
    try {
      const response = await fetch('/api/payments/connect-account')
      if (response.ok) {
        const data: ConnectAccountStatus = await response.json()
        setConnectStatus(data)
      }
    } catch (error) {
      console.error('Erreur lors du chargement du statut du compte:', error)
    }
  }

  const handleConnectAccount = async () => {
    setConnectLoading(true)
    try {
      const response = await fetch('/api/payments/connect-account', {
        method: 'POST'
      })

      if (response.ok) {
        const data = await response.json()
        window.location.href = data.onboardingUrl
      }
    } catch (error) {
      console.error('Erreur lors de la connexion du compte:', error)
    } finally {
      setConnectLoading(false)
    }
  }

  const getStatusIcon = (status: Payment['status']) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'CANCELLED':
        return <XCircle className="h-4 w-4 text-gray-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: Payment['status']) => {
    const variants = {
      COMPLETED: 'default',
      PENDING: 'secondary',
      FAILED: 'destructive',
      CANCELLED: 'outline'
    } as const

    const labels = {
      COMPLETED: 'Terminé',
      PENDING: 'En attente',
      FAILED: 'Échoué',
      CANCELLED: 'Annulé'
    }

    return (
      <Badge variant={variants[status]}>
        {labels[status]}
      </Badge>
    )
  }

  const calculateStats = () => {
    const sent = payments.filter(p => p.client.id === session?.user?.id)
    const received = payments.filter(p => p.prestataire.id === session?.user?.id)
    const completed = payments.filter(p => p.status === 'COMPLETED')

    return {
      totalSent: sent.reduce((sum, p) => sum + p.amount, 0),
      totalReceived: received.reduce((sum, p) => sum + (p.amount - p.platformFee), 0),
      totalTransactions: payments.length,
      completedTransactions: completed.length
    }
  }

  const stats = calculateStats()

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Paiements</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Paiements</h1>
        {connectStatus && !connectStatus.canReceivePayments && (
          <Button onClick={handleConnectAccount} disabled={connectLoading}>
            <Wallet className="mr-2 h-4 w-4" />
            {connectLoading ? 'Connexion...' : 'Configurer les paiements'}
          </Button>
        )}
      </div>

      {/* Alerte pour la configuration des paiements */}
      {connectStatus && !connectStatus.hasConnectedAccount && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Pour recevoir des paiements, vous devez configurer votre compte Stripe.
            <Button 
              variant="link" 
              className="p-0 ml-2 h-auto"
              onClick={handleConnectAccount}
              disabled={connectLoading}
            >
              Configurer maintenant
              <ExternalLink className="ml-1 h-3 w-3" />
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <ArrowUpRight className="h-4 w-4 text-red-500" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600">Total envoyé</p>
                <p className="text-2xl font-bold">{stats.totalSent.toFixed(2)} MAD</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <ArrowDownLeft className="h-4 w-4 text-green-500" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600">Total reçu</p>
                <p className="text-2xl font-bold">{stats.totalReceived.toFixed(2)} MAD</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-blue-500" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600">Transactions</p>
                <p className="text-2xl font-bold">{stats.totalTransactions}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600">Terminées</p>
                <p className="text-2xl font-bold">{stats.completedTransactions}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Liste des paiements */}
      <Card>
        <CardHeader>
          <CardTitle>Historique des paiements</CardTitle>
          <CardDescription>
            Gérez vos transactions et suivez l'état de vos paiements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">Tous</TabsTrigger>
              <TabsTrigger value="sent">Envoyés</TabsTrigger>
              <TabsTrigger value="received">Reçus</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="space-y-4 mt-6">
              {payments.length === 0 ? (
                <div className="text-center py-8">
                  <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-semibold text-gray-900">Aucun paiement</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Vous n'avez pas encore de transactions.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {payments.map((payment) => {
                    const isReceived = payment.prestataire.id === session?.user?.id
                    const otherUser = isReceived ? payment.client : payment.prestataire
                    const amount = isReceived ? payment.amount - payment.platformFee : payment.amount

                    return (
                      <Card key={payment.id}>
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center space-x-2">
                                {isReceived ? (
                                  <ArrowDownLeft className="h-5 w-5 text-green-500" />
                                ) : (
                                  <ArrowUpRight className="h-5 w-5 text-red-500" />
                                )}
                                {getStatusIcon(payment.status)}
                              </div>
                              
                              <div className="space-y-1">
                                <p className="font-medium">{payment.match.request.title}</p>
                                <p className="text-sm text-gray-500">
                                  {isReceived ? 'De' : 'À'} {otherUser.name}
                                </p>
                                <p className="text-xs text-gray-400">
                                  {formatDistanceToNow(new Date(payment.createdAt), {
                                    addSuffix: true,
                                    locale: fr
                                  })}
                                </p>
                              </div>
                            </div>

                            <div className="text-right space-y-2">
                              <div className="flex items-center space-x-2">
                                <span className={`font-semibold ${isReceived ? 'text-green-600' : 'text-red-600'}`}>
                                  {isReceived ? '+' : '-'}{amount.toFixed(2)} MAD
                                </span>
                                {getStatusBadge(payment.status)}
                              </div>
                              {payment.platformFee > 0 && isReceived && (
                                <p className="text-xs text-gray-500">
                                  Frais: {payment.platformFee.toFixed(2)} MAD
                                </p>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
