'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Settings, 
  Globe, 
  Mail, 
  CreditCard, 
  Shield, 
  Bell,
  Save,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface PlatformSettings {
  general: {
    platformName: string
    platformDescription: string
    supportEmail: string
    contactPhone: string
    defaultLanguage: string
    timezone: string
    maintenanceMode: boolean
  }
  payments: {
    platformFeePercentage: number
    minServicePrice: number
    maxServicePrice: number
    paymentMethods: string[]
    autoPayoutEnabled: boolean
    payoutDelay: number
  }
  moderation: {
    autoApproveServices: boolean
    requirePhoneVerification: boolean
    requireIdVerification: boolean
    maxImagesPerService: number
    bannedWords: string[]
  }
  notifications: {
    emailNotifications: boolean
    smsNotifications: boolean
    pushNotifications: boolean
    marketingEmails: boolean
    adminAlerts: boolean
  }
}

export default function AdminSettingsPage() {
  const [settings, setSettings] = useState<PlatformSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle')

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      // Simuler les données pour la démo
      setTimeout(() => {
        setSettings({
          general: {
            platformName: 'QirbLik',
            platformDescription: 'Plateforme de services entre particuliers au Maroc',
            supportEmail: '<EMAIL>',
            contactPhone: '+212 5 22 00 00 00',
            defaultLanguage: 'fr',
            timezone: 'Africa/Casablanca',
            maintenanceMode: false
          },
          payments: {
            platformFeePercentage: 5,
            minServicePrice: 50,
            maxServicePrice: 10000,
            paymentMethods: ['stripe', 'cmi', 'bank_transfer'],
            autoPayoutEnabled: true,
            payoutDelay: 7
          },
          moderation: {
            autoApproveServices: false,
            requirePhoneVerification: true,
            requireIdVerification: false,
            maxImagesPerService: 5,
            bannedWords: ['spam', 'arnaque', 'gratuit']
          },
          notifications: {
            emailNotifications: true,
            smsNotifications: true,
            pushNotifications: true,
            marketingEmails: false,
            adminAlerts: true
          }
        })
        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Erreur lors du chargement des paramètres:', error)
      setLoading(false)
    }
  }

  const handleSave = async () => {
    if (!settings) return

    setSaving(true)
    try {
      // Simuler la sauvegarde
      await new Promise(resolve => setTimeout(resolve, 1500))
      setSaveStatus('success')
      setTimeout(() => setSaveStatus('idle'), 3000)
    } catch (error) {
      setSaveStatus('error')
      setTimeout(() => setSaveStatus('idle'), 3000)
    } finally {
      setSaving(false)
    }
  }

  const updateSettings = (section: keyof PlatformSettings, field: string, value: any) => {
    if (!settings) return
    
    setSettings(prev => ({
      ...prev!,
      [section]: {
        ...prev![section],
        [field]: value
      }
    }))
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Paramètres de la plateforme</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!settings) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Erreur lors du chargement des paramètres</p>
        <Button onClick={fetchSettings} className="mt-4">
          Réessayer
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Paramètres de la plateforme</h1>
          <p className="text-gray-600">Configurez les paramètres globaux de QirbLik</p>
        </div>
        <Button onClick={handleSave} disabled={saving}>
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Sauvegarde...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Sauvegarder
            </>
          )}
        </Button>
      </div>

      {/* Statut de sauvegarde */}
      {saveStatus === 'success' && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Les paramètres ont été sauvegardés avec succès.
          </AlertDescription>
        </Alert>
      )}

      {saveStatus === 'error' && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Erreur lors de la sauvegarde des paramètres. Veuillez réessayer.
          </AlertDescription>
        </Alert>
      )}

      {/* Onglets de configuration */}
      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">
            <Settings className="mr-2 h-4 w-4" />
            Général
          </TabsTrigger>
          <TabsTrigger value="payments">
            <CreditCard className="mr-2 h-4 w-4" />
            Paiements
          </TabsTrigger>
          <TabsTrigger value="moderation">
            <Shield className="mr-2 h-4 w-4" />
            Modération
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="mr-2 h-4 w-4" />
            Notifications
          </TabsTrigger>
        </TabsList>

        {/* Paramètres généraux */}
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres généraux</CardTitle>
              <CardDescription>
                Configuration de base de la plateforme
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="platformName">Nom de la plateforme</Label>
                  <Input
                    id="platformName"
                    value={settings.general.platformName}
                    onChange={(e) => updateSettings('general', 'platformName', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="supportEmail">Email de support</Label>
                  <Input
                    id="supportEmail"
                    type="email"
                    value={settings.general.supportEmail}
                    onChange={(e) => updateSettings('general', 'supportEmail', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactPhone">Téléphone de contact</Label>
                  <Input
                    id="contactPhone"
                    value={settings.general.contactPhone}
                    onChange={(e) => updateSettings('general', 'contactPhone', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="defaultLanguage">Langue par défaut</Label>
                  <Select 
                    value={settings.general.defaultLanguage}
                    onValueChange={(value) => updateSettings('general', 'defaultLanguage', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fr">Français</SelectItem>
                      <SelectItem value="ar">العربية</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="platformDescription">Description de la plateforme</Label>
                <Textarea
                  id="platformDescription"
                  value={settings.general.platformDescription}
                  onChange={(e) => updateSettings('general', 'platformDescription', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-1">
                  <Label>Mode maintenance</Label>
                  <p className="text-sm text-gray-500">
                    Activer pour mettre la plateforme en maintenance
                  </p>
                </div>
                <Switch
                  checked={settings.general.maintenanceMode}
                  onCheckedChange={(checked) => updateSettings('general', 'maintenanceMode', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Paramètres de paiement */}
        <TabsContent value="payments">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres de paiement</CardTitle>
              <CardDescription>
                Configuration des paiements et commissions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="platformFee">Commission plateforme (%)</Label>
                  <Input
                    id="platformFee"
                    type="number"
                    min="0"
                    max="20"
                    step="0.1"
                    value={settings.payments.platformFeePercentage}
                    onChange={(e) => updateSettings('payments', 'platformFeePercentage', parseFloat(e.target.value))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="minPrice">Prix minimum (MAD)</Label>
                  <Input
                    id="minPrice"
                    type="number"
                    min="0"
                    value={settings.payments.minServicePrice}
                    onChange={(e) => updateSettings('payments', 'minServicePrice', parseInt(e.target.value))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxPrice">Prix maximum (MAD)</Label>
                  <Input
                    id="maxPrice"
                    type="number"
                    min="0"
                    value={settings.payments.maxServicePrice}
                    onChange={(e) => updateSettings('payments', 'maxServicePrice', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="payoutDelay">Délai de versement (jours)</Label>
                <Input
                  id="payoutDelay"
                  type="number"
                  min="1"
                  max="30"
                  value={settings.payments.payoutDelay}
                  onChange={(e) => updateSettings('payments', 'payoutDelay', parseInt(e.target.value))}
                  className="w-32"
                />
                <p className="text-sm text-gray-500">
                  Délai avant versement automatique aux prestataires
                </p>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-1">
                  <Label>Versement automatique</Label>
                  <p className="text-sm text-gray-500">
                    Activer les versements automatiques aux prestataires
                  </p>
                </div>
                <Switch
                  checked={settings.payments.autoPayoutEnabled}
                  onCheckedChange={(checked) => updateSettings('payments', 'autoPayoutEnabled', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Paramètres de modération */}
        <TabsContent value="moderation">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres de modération</CardTitle>
              <CardDescription>
                Configuration de la modération et validation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label>Approbation automatique des services</Label>
                    <p className="text-sm text-gray-500">
                      Les nouveaux services sont publiés sans validation manuelle
                    </p>
                  </div>
                  <Switch
                    checked={settings.moderation.autoApproveServices}
                    onCheckedChange={(checked) => updateSettings('moderation', 'autoApproveServices', checked)}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label>Vérification téléphonique obligatoire</Label>
                    <p className="text-sm text-gray-500">
                      Exiger la vérification du numéro de téléphone
                    </p>
                  </div>
                  <Switch
                    checked={settings.moderation.requirePhoneVerification}
                    onCheckedChange={(checked) => updateSettings('moderation', 'requirePhoneVerification', checked)}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label>Vérification d&apos;identité obligatoire</Label>
                    <p className="text-sm text-gray-500">
                      Exiger la vérification de la pièce d&apos;identité
                    </p>
                  </div>
                  <Switch
                    checked={settings.moderation.requireIdVerification}
                    onCheckedChange={(checked) => updateSettings('moderation', 'requireIdVerification', checked)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxImages">Nombre maximum d&apos;images par service</Label>
                <Input
                  id="maxImages"
                  type="number"
                  min="1"
                  max="10"
                  value={settings.moderation.maxImagesPerService}
                  onChange={(e) => updateSettings('moderation', 'maxImagesPerService', parseInt(e.target.value))}
                  className="w-32"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bannedWords">Mots interdits (séparés par des virgules)</Label>
                <Textarea
                  id="bannedWords"
                  value={settings.moderation.bannedWords.join(', ')}
                  onChange={(e) => updateSettings('moderation', 'bannedWords', e.target.value.split(', ').filter(w => w.trim()))}
                  rows={3}
                  placeholder="spam, arnaque, gratuit..."
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Paramètres de notifications */}
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres de notifications</CardTitle>
              <CardDescription>
                Configuration des notifications système
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label>Notifications par email</Label>
                    <p className="text-sm text-gray-500">
                      Envoyer des notifications par email aux utilisateurs
                    </p>
                  </div>
                  <Switch
                    checked={settings.notifications.emailNotifications}
                    onCheckedChange={(checked) => updateSettings('notifications', 'emailNotifications', checked)}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label>Notifications SMS</Label>
                    <p className="text-sm text-gray-500">
                      Envoyer des notifications par SMS
                    </p>
                  </div>
                  <Switch
                    checked={settings.notifications.smsNotifications}
                    onCheckedChange={(checked) => updateSettings('notifications', 'smsNotifications', checked)}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label>Notifications push</Label>
                    <p className="text-sm text-gray-500">
                      Envoyer des notifications push sur mobile
                    </p>
                  </div>
                  <Switch
                    checked={settings.notifications.pushNotifications}
                    onCheckedChange={(checked) => updateSettings('notifications', 'pushNotifications', checked)}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label>Emails marketing</Label>
                    <p className="text-sm text-gray-500">
                      Envoyer des emails promotionnels et newsletters
                    </p>
                  </div>
                  <Switch
                    checked={settings.notifications.marketingEmails}
                    onCheckedChange={(checked) => updateSettings('notifications', 'marketingEmails', checked)}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label>Alertes administrateur</Label>
                    <p className="text-sm text-gray-500">
                      Recevoir des alertes pour les actions importantes
                    </p>
                  </div>
                  <Switch
                    checked={settings.notifications.adminAlerts}
                    onCheckedChange={(checked) => updateSettings('notifications', 'adminAlerts', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
