import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import prisma from '@/lib/prisma'
import { z } from 'zod'

const createRequestSchema = z.object({
  title: z.string().min(1, 'Le titre est requis'),
  description: z.string().min(10, 'La description doit contenir au moins 10 caractères'),
  category: z.string().min(1, 'La catégorie est requise'),
  subCategory: z.string().optional(),
  tags: z.array(z.string()),
  budget: z.number().positive().optional(),
  budgetType: z.string().min(1, 'Le type de budget est requis'),
  urgency: z.string().default('NORMAL'),
  deadline: z.string().datetime().optional(),
  images: z.array(z.string()),
  attachments: z.array(z.string()),
  isPublic: z.boolean().default(true),
  expiresAt: z.string().datetime().optional(),
  locationLat: z.number().optional(),
  locationLng: z.number().optional(),
  locationAddress: z.string().optional(),
  locationCity: z.string().optional(),
  locationRegion: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const category = searchParams.get('category')
    const city = searchParams.get('city')
    const search = searchParams.get('search')
    const status = searchParams.get('status')

    const where: any = {
      isPublic: true
    }

    if (status) {
      where.status = status
    }

    if (category) {
      where.category = category
    }

    if (city) {
      where.locationCity = {
        contains: city,
        mode: 'insensitive'
      }
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { tags: { has: search } }
      ]
    }

    const requests = await prisma.request.findMany({
      where,
      include: {
        client: {
          select: {
            id: true,
            name: true,
            image: true,
            rating: true,
            reviewCount: true,
            locationCity: true,
            locationRegion: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    })

    const total = await prisma.request.count({ where })

    return NextResponse.json({
      requests,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching requests:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la récupération des demandes' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = createRequestSchema.parse(body)

    // Convertir les dates string en Date
    const processedData = {
      ...validatedData,
      deadline: validatedData.deadline ? new Date(validatedData.deadline) : undefined,
      expiresAt: validatedData.expiresAt ? new Date(validatedData.expiresAt) : undefined,
    }

    const request = await prisma.request.create({
      data: {
        ...processedData,
        tags: processedData.tags.join(','),
        images: processedData.images.join(','),
        attachments: processedData.attachments.join(','),
        clientId: session.user.id
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            image: true,
            rating: true,
            reviewCount: true
          }
        }
      }
    })

    return NextResponse.json(request, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Données invalides', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating request:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la création de la demande' },
      { status: 500 }
    )
  }
}
