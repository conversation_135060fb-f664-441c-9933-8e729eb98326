'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  Ban, 
  CheckCircle, 
  Mail,
  Phone,
  MapPin,
  Calendar
} from 'lucide-react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { formatDistanceToNow } from 'date-fns'
import { fr } from 'date-fns/locale'

interface User {
  id: string
  name: string
  email: string
  phone?: string
  userType: 'CLIENT' | 'PRESTATAIRE' | 'BOTH'
  isVerified: boolean
  isActive: boolean
  rating?: number
  reviewCount: number
  locationCity?: string
  locationRegion?: string
  createdAt: string
  lastSeen: string
  subscriptionStatus: string
}

export default function AdminUsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')

  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    try {
      // Simuler les données pour la démo
      setTimeout(() => {
        setUsers([
          {
            id: '1',
            name: 'Ahmed Benali',
            email: '<EMAIL>',
            phone: '+212 6 12 34 56 78',
            userType: 'PRESTATAIRE',
            isVerified: true,
            isActive: true,
            rating: 4.8,
            reviewCount: 23,
            locationCity: 'Casablanca',
            locationRegion: 'Grand Casablanca',
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(),
            lastSeen: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
            subscriptionStatus: 'ACTIVE'
          },
          {
            id: '2',
            name: 'Fatima Zahra',
            email: '<EMAIL>',
            phone: '+212 6 87 65 43 21',
            userType: 'CLIENT',
            isVerified: true,
            isActive: true,
            reviewCount: 5,
            locationCity: 'Rabat',
            locationRegion: 'Rabat-Salé-Kénitra',
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(),
            lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
            subscriptionStatus: 'FREE'
          },
          {
            id: '3',
            name: 'Youssef Alami',
            email: '<EMAIL>',
            userType: 'BOTH',
            isVerified: false,
            isActive: true,
            rating: 4.2,
            reviewCount: 8,
            locationCity: 'Marrakech',
            locationRegion: 'Marrakech-Safi',
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),
            lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
            subscriptionStatus: 'FREE'
          },
          {
            id: '4',
            name: 'Khadija Mansouri',
            email: '<EMAIL>',
            phone: '+212 6 55 44 33 22',
            userType: 'PRESTATAIRE',
            isVerified: true,
            isActive: false,
            rating: 4.9,
            reviewCount: 45,
            locationCity: 'Fès',
            locationRegion: 'Fès-Meknès',
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60).toISOString(),
            lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(),
            subscriptionStatus: 'ACTIVE'
          }
        ])
        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Erreur lors du chargement des utilisateurs:', error)
      setLoading(false)
    }
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = filterType === 'all' || user.userType === filterType
    
    const matchesStatus = filterStatus === 'all' || 
                         (filterStatus === 'active' && user.isActive) ||
                         (filterStatus === 'inactive' && !user.isActive) ||
                         (filterStatus === 'verified' && user.isVerified) ||
                         (filterStatus === 'unverified' && !user.isVerified)
    
    return matchesSearch && matchesType && matchesStatus
  })

  const getUserTypeBadge = (userType: User['userType']) => {
    const variants = {
      CLIENT: 'bg-blue-100 text-blue-800',
      PRESTATAIRE: 'bg-green-100 text-green-800',
      BOTH: 'bg-purple-100 text-purple-800'
    }
    
    const labels = {
      CLIENT: 'Client',
      PRESTATAIRE: 'Prestataire',
      BOTH: 'Les deux'
    }

    return (
      <Badge className={variants[userType]}>
        {labels[userType]}
      </Badge>
    )
  }

  const getStatusBadge = (user: User) => {
    if (!user.isActive) {
      return <Badge variant="destructive">Suspendu</Badge>
    }
    if (!user.isVerified) {
      return <Badge variant="secondary">Non vérifié</Badge>
    }
    return <Badge variant="default">Actif</Badge>
  }

  const handleUserAction = async (userId: string, action: 'view' | 'suspend' | 'activate' | 'verify') => {
    console.log(`Action ${action} pour l'utilisateur ${userId}`)
    // Implémenter les actions ici
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Gestion des utilisateurs</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des utilisateurs</h1>
          <p className="text-gray-600">Gérez les comptes utilisateurs de la plateforme</p>
        </div>
        <Button onClick={fetchUsers}>
          Actualiser
        </Button>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{users.length}</p>
              <p className="text-sm text-gray-600">Total utilisateurs</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {users.filter(u => u.isActive).length}
              </p>
              <p className="text-sm text-gray-600">Actifs</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {users.filter(u => u.isVerified).length}
              </p>
              <p className="text-sm text-gray-600">Vérifiés</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">
                {users.filter(u => u.userType === 'PRESTATAIRE' || u.userType === 'BOTH').length}
              </p>
              <p className="text-sm text-gray-600">Prestataires</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtres et recherche */}
      <Card>
        <CardHeader>
          <CardTitle>Filtres</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher par nom ou email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Type d'utilisateur" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les types</SelectItem>
                <SelectItem value="CLIENT">Clients</SelectItem>
                <SelectItem value="PRESTATAIRE">Prestataires</SelectItem>
                <SelectItem value="BOTH">Les deux</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les statuts</SelectItem>
                <SelectItem value="active">Actifs</SelectItem>
                <SelectItem value="inactive">Suspendus</SelectItem>
                <SelectItem value="verified">Vérifiés</SelectItem>
                <SelectItem value="unverified">Non vérifiés</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Liste des utilisateurs */}
      <Card>
        <CardHeader>
          <CardTitle>Utilisateurs ({filteredUsers.length})</CardTitle>
          <CardDescription>
            Liste de tous les utilisateurs inscrits sur la plateforme
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Utilisateur</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Localisation</TableHead>
                  <TableHead>Note</TableHead>
                  <TableHead>Inscription</TableHead>
                  <TableHead>Dernière activité</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium">{user.name}</p>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <Mail className="h-3 w-3" />
                          <span>{user.email}</span>
                        </div>
                        {user.phone && (
                          <div className="flex items-center space-x-2 text-sm text-gray-500">
                            <Phone className="h-3 w-3" />
                            <span>{user.phone}</span>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getUserTypeBadge(user.userType)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(user)}
                    </TableCell>
                    <TableCell>
                      {user.locationCity && (
                        <div className="flex items-center space-x-1 text-sm">
                          <MapPin className="h-3 w-3 text-gray-400" />
                          <span>{user.locationCity}</span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {user.rating ? (
                        <div className="text-sm">
                          <span className="font-medium">{user.rating.toFixed(1)}</span>
                          <span className="text-gray-500"> ({user.reviewCount})</span>
                        </div>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1 text-sm text-gray-500">
                        <Calendar className="h-3 w-3" />
                        <span>
                          {formatDistanceToNow(new Date(user.createdAt), {
                            addSuffix: true,
                            locale: fr
                          })}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-500">
                        {formatDistanceToNow(new Date(user.lastSeen), {
                          addSuffix: true,
                          locale: fr
                        })}
                      </span>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleUserAction(user.id, 'view')}>
                            <Eye className="mr-2 h-4 w-4" />
                            Voir le profil
                          </DropdownMenuItem>
                          {!user.isVerified && (
                            <DropdownMenuItem onClick={() => handleUserAction(user.id, 'verify')}>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Vérifier
                            </DropdownMenuItem>
                          )}
                          {user.isActive ? (
                            <DropdownMenuItem 
                              onClick={() => handleUserAction(user.id, 'suspend')}
                              className="text-red-600"
                            >
                              <Ban className="mr-2 h-4 w-4" />
                              Suspendre
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem onClick={() => handleUserAction(user.id, 'activate')}>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Réactiver
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
