<testsuites id="" name="" tests="115" failures="18" skipped="97" errors="0" time="17.852019">
<testsuite name="auth.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="chromium" tests="9" failures="9" skipped="0" time="26.542" errors="0">
<testcase name="Authentication Flow › should display login page when accessing protected route" classname="auth.spec.ts" time="2.88">
<failure message="auth.spec.ts:10:7 should display login page when accessing protected route" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:10:7 › Authentication Flow › should display login page when accessing protected route 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
    Call log:
      - navigating to "http://localhost:3000/", waiting until "networkidle"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Ensure server is ready before each test
    > 6 |     await page.goto('/', { waitUntil: 'networkidle', timeout: 30000 });
        |                ^
      7 |     await page.waitForTimeout(1000); // Additional wait for stability
      8 |   });
      9 |
        at E:\NodeProjects\QirbLik\tests\e2e\auth.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-s-197ac-n-accessing-protected-route-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-s-197ac-n-accessing-protected-route-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|auth-Authentication-Flow-s-197ac-n-accessing-protected-route-chromium\test-failed-1.png]]

[[ATTACHMENT|auth-Authentication-Flow-s-197ac-n-accessing-protected-route-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should login with valid credentials" classname="auth.spec.ts" time="2.876">
<failure message="auth.spec.ts:24:7 should login with valid credentials" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:24:7 › Authentication Flow › should login with valid credentials ───────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
    Call log:
      - navigating to "http://localhost:3000/", waiting until "networkidle"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Ensure server is ready before each test
    > 6 |     await page.goto('/', { waitUntil: 'networkidle', timeout: 30000 });
        |                ^
      7 |     await page.waitForTimeout(1000); // Additional wait for stability
      8 |   });
      9 |
        at E:\NodeProjects\QirbLik\tests\e2e\auth.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-login-with-valid-credentials-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-login-with-valid-credentials-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|auth-Authentication-Flow-should-login-with-valid-credentials-chromium\test-failed-1.png]]

[[ATTACHMENT|auth-Authentication-Flow-should-login-with-valid-credentials-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should show error with invalid credentials" classname="auth.spec.ts" time="3.004">
<failure message="auth.spec.ts:56:7 should show error with invalid credentials" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:56:7 › Authentication Flow › should show error with invalid credentials 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
    Call log:
      - navigating to "http://localhost:3000/", waiting until "networkidle"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Ensure server is ready before each test
    > 6 |     await page.goto('/', { waitUntil: 'networkidle', timeout: 30000 });
        |                ^
      7 |     await page.waitForTimeout(1000); // Additional wait for stability
      8 |   });
      9 |
        at E:\NodeProjects\QirbLik\tests\e2e\auth.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-s-22fb7-or-with-invalid-credentials-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-s-22fb7-or-with-invalid-credentials-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|auth-Authentication-Flow-s-22fb7-or-with-invalid-credentials-chromium\test-failed-1.png]]

[[ATTACHMENT|auth-Authentication-Flow-s-22fb7-or-with-invalid-credentials-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should register new user" classname="auth.spec.ts" time="2.946">
<failure message="auth.spec.ts:79:7 should register new user" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:79:7 › Authentication Flow › should register new user ──────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
    Call log:
      - navigating to "http://localhost:3000/", waiting until "networkidle"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Ensure server is ready before each test
    > 6 |     await page.goto('/', { waitUntil: 'networkidle', timeout: 30000 });
        |                ^
      7 |     await page.waitForTimeout(1000); // Additional wait for stability
      8 |   });
      9 |
        at E:\NodeProjects\QirbLik\tests\e2e\auth.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-register-new-user-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-register-new-user-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|auth-Authentication-Flow-should-register-new-user-chromium\test-failed-1.png]]

[[ATTACHMENT|auth-Authentication-Flow-should-register-new-user-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should logout successfully" classname="auth.spec.ts" time="2.99">
<failure message="auth.spec.ts:108:7 should logout successfully" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:108:7 › Authentication Flow › should logout successfully ───────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
    Call log:
      - navigating to "http://localhost:3000/", waiting until "networkidle"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Ensure server is ready before each test
    > 6 |     await page.goto('/', { waitUntil: 'networkidle', timeout: 30000 });
        |                ^
      7 |     await page.waitForTimeout(1000); // Additional wait for stability
      8 |   });
      9 |
        at E:\NodeProjects\QirbLik\tests\e2e\auth.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-logout-successfully-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-logout-successfully-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|auth-Authentication-Flow-should-logout-successfully-chromium\test-failed-1.png]]

[[ATTACHMENT|auth-Authentication-Flow-should-logout-successfully-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should handle Google OAuth flow" classname="auth.spec.ts" time="2.927">
<failure message="auth.spec.ts:134:7 should handle Google OAuth flow" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:134:7 › Authentication Flow › should handle Google OAuth flow ──────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
    Call log:
      - navigating to "http://localhost:3000/", waiting until "networkidle"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Ensure server is ready before each test
    > 6 |     await page.goto('/', { waitUntil: 'networkidle', timeout: 30000 });
        |                ^
      7 |     await page.waitForTimeout(1000); // Additional wait for stability
      8 |   });
      9 |
        at E:\NodeProjects\QirbLik\tests\e2e\auth.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-handle-Google-OAuth-flow-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-handle-Google-OAuth-flow-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|auth-Authentication-Flow-should-handle-Google-OAuth-flow-chromium\test-failed-1.png]]

[[ATTACHMENT|auth-Authentication-Flow-should-handle-Google-OAuth-flow-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should validate form fields" classname="auth.spec.ts" time="3.152">
<failure message="auth.spec.ts:150:7 should validate form fields" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:150:7 › Authentication Flow › should validate form fields ──────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
    Call log:
      - navigating to "http://localhost:3000/", waiting until "networkidle"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Ensure server is ready before each test
    > 6 |     await page.goto('/', { waitUntil: 'networkidle', timeout: 30000 });
        |                ^
      7 |     await page.waitForTimeout(1000); // Additional wait for stability
      8 |   });
      9 |
        at E:\NodeProjects\QirbLik\tests\e2e\auth.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-validate-form-fields-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-validate-form-fields-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|auth-Authentication-Flow-should-validate-form-fields-chromium\test-failed-1.png]]

[[ATTACHMENT|auth-Authentication-Flow-should-validate-form-fields-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should remember user session" classname="auth.spec.ts" time="2.941">
<failure message="auth.spec.ts:173:7 should remember user session" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:173:7 › Authentication Flow › should remember user session ─────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
    Call log:
      - navigating to "http://localhost:3000/", waiting until "networkidle"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Ensure server is ready before each test
    > 6 |     await page.goto('/', { waitUntil: 'networkidle', timeout: 30000 });
        |                ^
      7 |     await page.waitForTimeout(1000); // Additional wait for stability
      8 |   });
      9 |
        at E:\NodeProjects\QirbLik\tests\e2e\auth.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-remember-user-session-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-remember-user-session-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|auth-Authentication-Flow-should-remember-user-session-chromium\test-failed-1.png]]

[[ATTACHMENT|auth-Authentication-Flow-should-remember-user-session-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should handle password reset flow" classname="auth.spec.ts" time="2.826">
<failure message="auth.spec.ts:197:7 should handle password reset flow" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:197:7 › Authentication Flow › should handle password reset flow ────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
    Call log:
      - navigating to "http://localhost:3000/", waiting until "networkidle"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Ensure server is ready before each test
    > 6 |     await page.goto('/', { waitUntil: 'networkidle', timeout: 30000 });
        |                ^
      7 |     await page.waitForTimeout(1000); // Additional wait for stability
      8 |   });
      9 |
        at E:\NodeProjects\QirbLik\tests\e2e\auth.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-handle-password-reset-flow-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\auth-Authentication-Flow-should-handle-password-reset-flow-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|auth-Authentication-Flow-should-handle-password-reset-flow-chromium\test-failed-1.png]]

[[ATTACHMENT|auth-Authentication-Flow-should-handle-password-reset-flow-chromium\video.webm]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="connection-test.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="chromium" tests="2" failures="2" skipped="0" time="5.995" errors="0">
<testcase name="Connection Test › should connect to localhost:3000" classname="connection-test.spec.ts" time="2.995">
<failure message="connection-test.spec.ts:4:7 should connect to localhost:3000" type="FAILURE">
<![CDATA[  [chromium] › connection-test.spec.ts:4:7 › Connection Test › should connect to localhost:3000 ────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
    Call log:
      - navigating to "http://localhost:3000/", waiting until "networkidle"


       6 |     
       7 |     try {
    >  8 |       await page.goto('http://localhost:3000', { 
         |                  ^
       9 |         waitUntil: 'networkidle',
      10 |         timeout: 10000 
      11 |       });
        at E:\NodeProjects\QirbLik\tests\e2e\connection-test.spec.ts:8:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\connection-test-Connection-47cf0-d-connect-to-localhost-3000-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\connection-test-Connection-47cf0-d-connect-to-localhost-3000-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[Attempting to connect to http://localhost:3000

[[ATTACHMENT|connection-test-Connection-47cf0-d-connect-to-localhost-3000-chromium\test-failed-1.png]]

[[ATTACHMENT|connection-test-Connection-47cf0-d-connect-to-localhost-3000-chromium\video.webm]]
]]>
</system-out>
<system-err>
<![CDATA[Connection failed: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/
Call log:
[2m  - navigating to "http://localhost:3000/", waiting until "networkidle"[22m

    at [90mE:\NodeProjects\QirbLik\[39mtests\e2e\connection-test.spec.ts:8:18 {
  name: [32m'Error'[39m,
  [[32mSymbol(step)[39m]: {
    location: {
      file: [32m'E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts'[39m,
      line: [33m8[39m,
      column: [33m18[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'Navigate to "/"'[39m,
    apiName: [32m'page.goto'[39m,
    params: {
      url: [32m'http://localhost:3000'[39m,
      waitUntil: [32m'networkidle'[39m,
      timeout: [33m10000[39m
    },
    group: [90mundefined[39m,
    stepId: [32m'pw:api@35'[39m,
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@35'[39m,
      _title: [32m'Navigate to "/"'[39m,
      _parentStep: [90mundefined[39m,
      skip: [36m[Function (anonymous)][39m
    },
    recoverFromStepError: [36m[AsyncFunction: recoverFromStepError][39m,
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1758204288880[39m,
    error: {
      message: [32m'Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - navigating to "http://localhost:3000/", waiting until "networkidle"\x1B[22m\n'[39m,
      stack: [32m'Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - navigating to "http://localhost:3000/", waiting until "networkidle"\x1B[22m\n'[39m +
        [32m'\n'[39m +
        [32m'    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts:8:18'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
<testcase name="Connection Test › should access dashboard and get redirected to signin" classname="connection-test.spec.ts" time="3">
<failure message="connection-test.spec.ts:24:7 should access dashboard and get redirected to signin" type="FAILURE">
<![CDATA[  [chromium] › connection-test.spec.ts:24:7 › Connection Test › should access dashboard and get redirected to signin 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/dashboard
    Call log:
      - navigating to "http://localhost:3000/dashboard", waiting until "networkidle"


      27 |     try {
      28 |       // Try to access dashboard without authentication
    > 29 |       await page.goto('http://localhost:3000/dashboard', { 
         |                  ^
      30 |         waitUntil: 'networkidle',
      31 |         timeout: 10000 
      32 |       });
        at E:\NodeProjects\QirbLik\tests\e2e\connection-test.spec.ts:29:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\connection-test-Connection-66f23-nd-get-redirected-to-signin-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\connection-test-Connection-66f23-nd-get-redirected-to-signin-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[Testing dashboard redirect...

[[ATTACHMENT|connection-test-Connection-66f23-nd-get-redirected-to-signin-chromium\test-failed-1.png]]

[[ATTACHMENT|connection-test-Connection-66f23-nd-get-redirected-to-signin-chromium\video.webm]]
]]>
</system-out>
<system-err>
<![CDATA[Dashboard redirect test failed: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/dashboard
Call log:
[2m  - navigating to "http://localhost:3000/dashboard", waiting until "networkidle"[22m

    at [90mE:\NodeProjects\QirbLik\[39mtests\e2e\connection-test.spec.ts:29:18 {
  name: [32m'Error'[39m,
  [[32mSymbol(step)[39m]: {
    location: {
      file: [32m'E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts'[39m,
      line: [33m29[39m,
      column: [33m18[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'Navigate to "/dashboard"'[39m,
    apiName: [32m'page.goto'[39m,
    params: {
      url: [32m'http://localhost:3000/dashboard'[39m,
      waitUntil: [32m'networkidle'[39m,
      timeout: [33m10000[39m
    },
    group: [90mundefined[39m,
    stepId: [32m'pw:api@35'[39m,
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@35'[39m,
      _title: [32m'Navigate to "/dashboard"'[39m,
      _parentStep: [90mundefined[39m,
      skip: [36m[Function (anonymous)][39m
    },
    recoverFromStepError: [36m[AsyncFunction: recoverFromStepError][39m,
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1758204288958[39m,
    error: {
      message: [32m'Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/dashboard\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - navigating to "http://localhost:3000/dashboard", waiting until "networkidle"\x1B[22m\n'[39m,
      stack: [32m'Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/dashboard\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - navigating to "http://localhost:3000/dashboard", waiting until "networkidle"\x1B[22m\n'[39m +
        [32m'\n'[39m +
        [32m'    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts:29:18'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
</testsuite>
<testsuite name="portfolio.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="chromium" tests="12" failures="7" skipped="5" time="22.178" errors="0">
<testcase name="Portfolio Management › should display portfolio page with existing items" classname="portfolio.spec.ts" time="2.957">
<failure message="portfolio.spec.ts:13:7 should display portfolio page with existing items" type="FAILURE">
<![CDATA[  [chromium] › portfolio.spec.ts:13:7 › Portfolio Management › should display portfolio page with existing items 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin
    Call log:
      - navigating to "http://localhost:3000/auth/signin", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Login as prestataire before each test
    > 6 |     await page.goto('/auth/signin');
        |                ^
      7 |     await page.getByLabel('Email').fill('<EMAIL>');
      8 |     await page.getByLabel('Mot de passe').fill('password123');
      9 |     await page.getByRole('button', { name: 'Se connecter' }).click();
        at E:\NodeProjects\QirbLik\tests\e2e\portfolio.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\portfolio-Portfolio-Manage-895e3-io-page-with-existing-items-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\portfolio-Portfolio-Manage-895e3-io-page-with-existing-items-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|portfolio-Portfolio-Manage-895e3-io-page-with-existing-items-chromium\test-failed-1.png]]

[[ATTACHMENT|portfolio-Portfolio-Manage-895e3-io-page-with-existing-items-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Portfolio Management › should create new portfolio item" classname="portfolio.spec.ts" time="2.988">
<failure message="portfolio.spec.ts:25:7 should create new portfolio item" type="FAILURE">
<![CDATA[  [chromium] › portfolio.spec.ts:25:7 › Portfolio Management › should create new portfolio item ────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin
    Call log:
      - navigating to "http://localhost:3000/auth/signin", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Login as prestataire before each test
    > 6 |     await page.goto('/auth/signin');
        |                ^
      7 |     await page.getByLabel('Email').fill('<EMAIL>');
      8 |     await page.getByLabel('Mot de passe').fill('password123');
      9 |     await page.getByRole('button', { name: 'Se connecter' }).click();
        at E:\NodeProjects\QirbLik\tests\e2e\portfolio.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\portfolio-Portfolio-Manage-d33e4-d-create-new-portfolio-item-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\portfolio-Portfolio-Manage-d33e4-d-create-new-portfolio-item-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|portfolio-Portfolio-Manage-d33e4-d-create-new-portfolio-item-chromium\test-failed-1.png]]

[[ATTACHMENT|portfolio-Portfolio-Manage-d33e4-d-create-new-portfolio-item-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Portfolio Management › should edit existing portfolio item" classname="portfolio.spec.ts" time="2.783">
<failure message="portfolio.spec.ts:68:7 should edit existing portfolio item" type="FAILURE">
<![CDATA[  [chromium] › portfolio.spec.ts:68:7 › Portfolio Management › should edit existing portfolio item ─

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin
    Call log:
      - navigating to "http://localhost:3000/auth/signin", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Login as prestataire before each test
    > 6 |     await page.goto('/auth/signin');
        |                ^
      7 |     await page.getByLabel('Email').fill('<EMAIL>');
      8 |     await page.getByLabel('Mot de passe').fill('password123');
      9 |     await page.getByRole('button', { name: 'Se connecter' }).click();
        at E:\NodeProjects\QirbLik\tests\e2e\portfolio.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\portfolio-Portfolio-Manage-6e658-dit-existing-portfolio-item-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\portfolio-Portfolio-Manage-6e658-dit-existing-portfolio-item-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|portfolio-Portfolio-Manage-6e658-dit-existing-portfolio-item-chromium\test-failed-1.png]]

[[ATTACHMENT|portfolio-Portfolio-Manage-6e658-dit-existing-portfolio-item-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Portfolio Management › should delete portfolio item" classname="portfolio.spec.ts" time="2.976">
<failure message="portfolio.spec.ts:99:7 should delete portfolio item" type="FAILURE">
<![CDATA[  [chromium] › portfolio.spec.ts:99:7 › Portfolio Management › should delete portfolio item ────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin
    Call log:
      - navigating to "http://localhost:3000/auth/signin", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Login as prestataire before each test
    > 6 |     await page.goto('/auth/signin');
        |                ^
      7 |     await page.getByLabel('Email').fill('<EMAIL>');
      8 |     await page.getByLabel('Mot de passe').fill('password123');
      9 |     await page.getByRole('button', { name: 'Se connecter' }).click();
        at E:\NodeProjects\QirbLik\tests\e2e\portfolio.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\portfolio-Portfolio-Management-should-delete-portfolio-item-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\portfolio-Portfolio-Management-should-delete-portfolio-item-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|portfolio-Portfolio-Management-should-delete-portfolio-item-chromium\test-failed-1.png]]

[[ATTACHMENT|portfolio-Portfolio-Management-should-delete-portfolio-item-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Portfolio Management › should filter portfolio by category" classname="portfolio.spec.ts" time="2.793">
<failure message="portfolio.spec.ts:125:7 should filter portfolio by category" type="FAILURE">
<![CDATA[  [chromium] › portfolio.spec.ts:125:7 › Portfolio Management › should filter portfolio by category 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin
    Call log:
      - navigating to "http://localhost:3000/auth/signin", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Login as prestataire before each test
    > 6 |     await page.goto('/auth/signin');
        |                ^
      7 |     await page.getByLabel('Email').fill('<EMAIL>');
      8 |     await page.getByLabel('Mot de passe').fill('password123');
      9 |     await page.getByRole('button', { name: 'Se connecter' }).click();
        at E:\NodeProjects\QirbLik\tests\e2e\portfolio.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\portfolio-Portfolio-Manage-c6c60-ilter-portfolio-by-category-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\portfolio-Portfolio-Manage-c6c60-ilter-portfolio-by-category-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|portfolio-Portfolio-Manage-c6c60-ilter-portfolio-by-category-chromium\test-failed-1.png]]

[[ATTACHMENT|portfolio-Portfolio-Manage-c6c60-ilter-portfolio-by-category-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Portfolio Management › should toggle item visibility" classname="portfolio.spec.ts" time="1.865">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should share portfolio item" classname="portfolio.spec.ts" time="0.006">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should handle empty portfolio state" classname="portfolio.spec.ts" time="1.551">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should validate form inputs" classname="portfolio.spec.ts" time="1.509">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should handle image upload" classname="portfolio.spec.ts" time="1.58">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should display portfolio statistics" classname="portfolio.spec.ts" time="0.582">
<failure message="portfolio.spec.ts:235:7 should display portfolio statistics" type="FAILURE">
<![CDATA[  [chromium] › portfolio.spec.ts:235:7 › Portfolio Management › should display portfolio statistics 

    Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin
    Call log:
      - navigating to "http://localhost:3000/auth/signin", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Login as prestataire before each test
    > 6 |     await page.goto('/auth/signin');
        |                ^
      7 |     await page.getByLabel('Email').fill('<EMAIL>');
      8 |     await page.getByLabel('Mot de passe').fill('password123');
      9 |     await page.getByRole('button', { name: 'Se connecter' }).click();
        at E:\NodeProjects\QirbLik\tests\e2e\portfolio.spec.ts:6:16

    Error: browserContext.close: Test ended.
    Browser logs:

    <launching> C:\Users\<USER>\AppData\Local\ms-playwright\chromium_headless_shell-1187\chrome-win\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AvoidUnnecessaryBeforeUnloadCheckSync,DestroyProfileOnBrowserClose,DialMediaRouteProvider,GlobalMediaControls,HttpsUpgrades,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --user-data-dir=C:\Users\<USER>\AppData\Local\Temp\playwright_chromiumdev_profile-XXXXXXilOGbR --remote-debugging-pipe --no-startup-window
    <launched> pid=38092
]]>
</failure>
</testcase>
<testcase name="Portfolio Management › should work on mobile viewport" classname="portfolio.spec.ts" time="0.588">
<failure message="portfolio.spec.ts:248:7 should work on mobile viewport" type="FAILURE">
<![CDATA[  [chromium] › portfolio.spec.ts:248:7 › Portfolio Management › should work on mobile viewport ─────

    Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin
    Call log:
      - navigating to "http://localhost:3000/auth/signin", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Login as prestataire before each test
    > 6 |     await page.goto('/auth/signin');
        |                ^
      7 |     await page.getByLabel('Email').fill('<EMAIL>');
      8 |     await page.getByLabel('Mot de passe').fill('password123');
      9 |     await page.getByRole('button', { name: 'Se connecter' }).click();
        at E:\NodeProjects\QirbLik\tests\e2e\portfolio.spec.ts:6:16

    Error: browserContext.close: Test ended.
    Browser logs:

    <launching> C:\Users\<USER>\AppData\Local\ms-playwright\chromium_headless_shell-1187\chrome-win\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AvoidUnnecessaryBeforeUnloadCheckSync,DestroyProfileOnBrowserClose,DialMediaRouteProvider,GlobalMediaControls,HttpsUpgrades,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --user-data-dir=C:\Users\<USER>\AppData\Local\Temp\playwright_chromiumdev_profile-XXXXXXcFn1tO --remote-debugging-pipe --no-startup-window
    <launched> pid=40760
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="firefox" tests="9" failures="0" skipped="9" time="0.007" errors="0">
<testcase name="Authentication Flow › should display login page when accessing protected route" classname="auth.spec.ts" time="0.007">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should login with valid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error with invalid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should register new user" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should logout successfully" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle Google OAuth flow" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should validate form fields" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should remember user session" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle password reset flow" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="connection-test.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="firefox" tests="2" failures="0" skipped="2" time="0" errors="0">
<testcase name="Connection Test › should connect to localhost:3000" classname="connection-test.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Test › should access dashboard and get redirected to signin" classname="connection-test.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="portfolio.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="firefox" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Portfolio Management › should display portfolio page with existing items" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should create new portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should edit existing portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should delete portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should filter portfolio by category" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should toggle item visibility" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should share portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should handle empty portfolio state" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should validate form inputs" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should handle image upload" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should display portfolio statistics" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should work on mobile viewport" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="webkit" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Authentication Flow › should display login page when accessing protected route" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should login with valid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error with invalid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should register new user" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should logout successfully" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle Google OAuth flow" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should validate form fields" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should remember user session" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle password reset flow" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="connection-test.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="webkit" tests="2" failures="0" skipped="2" time="0" errors="0">
<testcase name="Connection Test › should connect to localhost:3000" classname="connection-test.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Test › should access dashboard and get redirected to signin" classname="connection-test.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="portfolio.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="webkit" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Portfolio Management › should display portfolio page with existing items" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should create new portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should edit existing portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should delete portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should filter portfolio by category" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should toggle item visibility" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should share portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should handle empty portfolio state" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should validate form inputs" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should handle image upload" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should display portfolio statistics" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should work on mobile viewport" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="Mobile Chrome" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Authentication Flow › should display login page when accessing protected route" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should login with valid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error with invalid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should register new user" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should logout successfully" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle Google OAuth flow" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should validate form fields" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should remember user session" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle password reset flow" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="connection-test.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="Mobile Chrome" tests="2" failures="0" skipped="2" time="0" errors="0">
<testcase name="Connection Test › should connect to localhost:3000" classname="connection-test.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Test › should access dashboard and get redirected to signin" classname="connection-test.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="portfolio.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="Mobile Chrome" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Portfolio Management › should display portfolio page with existing items" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should create new portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should edit existing portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should delete portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should filter portfolio by category" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should toggle item visibility" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should share portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should handle empty portfolio state" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should validate form inputs" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should handle image upload" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should display portfolio statistics" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should work on mobile viewport" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="Mobile Safari" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Authentication Flow › should display login page when accessing protected route" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should login with valid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error with invalid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should register new user" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should logout successfully" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle Google OAuth flow" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should validate form fields" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should remember user session" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle password reset flow" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="connection-test.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="Mobile Safari" tests="2" failures="0" skipped="2" time="0" errors="0">
<testcase name="Connection Test › should connect to localhost:3000" classname="connection-test.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Test › should access dashboard and get redirected to signin" classname="connection-test.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="portfolio.spec.ts" timestamp="2025-09-18T14:04:36.873Z" hostname="Mobile Safari" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Portfolio Management › should display portfolio page with existing items" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should create new portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should edit existing portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should delete portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should filter portfolio by category" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should toggle item visibility" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should share portfolio item" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should handle empty portfolio state" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should validate form inputs" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should handle image upload" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should display portfolio statistics" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Portfolio Management › should work on mobile viewport" classname="portfolio.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>