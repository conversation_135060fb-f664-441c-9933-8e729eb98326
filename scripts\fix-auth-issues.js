const fs = require('fs');
const path = require('path');

console.log('🔧 Diagnostic et Correction des Problèmes d\'Authentification\n');

// 1. Vérifier les variables d'environnement
console.log('🔍 1. Vérification des variables d\'environnement...');

const envPath = '.env.local';
let envIssues = [];

if (!fs.existsSync(envPath)) {
  envIssues.push('Fichier .env.local manquant');
} else {
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  const requiredVars = [
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
    'DATABASE_URL'
  ];
  
  requiredVars.forEach(varName => {
    if (!envContent.includes(varName)) {
      envIssues.push(`Variable ${varName} manquante`);
    } else if (envContent.includes(`${varName}=`)) {
      const line = envContent.split('\n').find(l => l.startsWith(varName));
      if (line && line.split('=')[1].trim() === '') {
        envIssues.push(`Variable ${varName} vide`);
      }
    }
  });
}

if (envIssues.length > 0) {
  console.log('❌ Problèmes détectés:');
  envIssues.forEach(issue => console.log(`  - ${issue}`));
  
  console.log('\n🔧 Correction automatique...');
  
  // Créer ou corriger le fichier .env.local
  let envContent = fs.existsSync(envPath) ? fs.readFileSync(envPath, 'utf8') : '';
  
  // Générer NEXTAUTH_SECRET si manquant
  if (!envContent.includes('NEXTAUTH_SECRET=') || envContent.includes('NEXTAUTH_SECRET=\n')) {
    const crypto = require('crypto');
    const secret = crypto.randomBytes(32).toString('base64');
    
    if (envContent.includes('NEXTAUTH_SECRET=')) {
      envContent = envContent.replace(/NEXTAUTH_SECRET=.*\n/, `NEXTAUTH_SECRET="${secret}"\n`);
    } else {
      envContent += `\nNEXTAUTH_SECRET="${secret}"\n`;
    }
    console.log('  ✅ NEXTAUTH_SECRET généré');
  }
  
  // Ajouter NEXTAUTH_URL si manquant
  if (!envContent.includes('NEXTAUTH_URL=')) {
    envContent += `NEXTAUTH_URL="http://localhost:3000"\n`;
    console.log('  ✅ NEXTAUTH_URL ajouté');
  }
  
  // Ajouter DATABASE_URL si manquant
  if (!envContent.includes('DATABASE_URL=')) {
    envContent += `DATABASE_URL="file:./dev.db"\n`;
    console.log('  ✅ DATABASE_URL ajouté');
  }
  
  fs.writeFileSync(envPath, envContent);
  console.log('✅ Fichier .env.local corrigé');
} else {
  console.log('✅ Variables d\'environnement OK');
}

// 2. Vérifier la configuration NextAuth
console.log('\n🔍 2. Vérification de la configuration NextAuth...');

const authConfigPath = 'lib/auth.ts';
if (fs.existsSync(authConfigPath)) {
  const authContent = fs.readFileSync(authConfigPath, 'utf8');
  
  const checks = [
    { pattern: 'async redirect', name: 'Callback redirect' },
    { pattern: 'pages:', name: 'Pages personnalisées' },
    { pattern: 'session:', name: 'Configuration session' },
    { pattern: 'callbacks:', name: 'Callbacks' }
  ];
  
  checks.forEach(check => {
    if (authContent.includes(check.pattern)) {
      console.log(`  ✅ ${check.name} configuré`);
    } else {
      console.log(`  ⚠️  ${check.name} manquant`);
    }
  });
} else {
  console.log('❌ Fichier lib/auth.ts manquant');
}

// 3. Vérifier le middleware
console.log('\n🔍 3. Vérification du middleware...');

const middlewarePath = 'middleware.ts';
if (fs.existsSync(middlewarePath)) {
  console.log('✅ Middleware présent');
  
  const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
  if (middlewareContent.includes('protectedRoutes')) {
    console.log('  ✅ Routes protégées configurées');
  } else {
    console.log('  ⚠️  Routes protégées non configurées');
  }
} else {
  console.log('⚠️  Middleware manquant - Redirection automatique non configurée');
}

// 4. Vérifier la base de données
console.log('\n🔍 4. Vérification de la base de données...');

const dbPath = 'prisma/dev.db';
if (fs.existsSync(dbPath)) {
  console.log('✅ Base de données présente');
} else {
  console.log('⚠️  Base de données manquante');
  console.log('💡 Exécutez: npx prisma db push');
}

// 5. Vérifier les pages d'authentification
console.log('\n🔍 5. Vérification des pages d\'authentification...');

const authPages = [
  'app/auth/signin/page.tsx',
  'app/auth/signup/page.tsx'
];

authPages.forEach(pagePath => {
  if (fs.existsSync(pagePath)) {
    console.log(`  ✅ ${pagePath} présent`);
    
    const pageContent = fs.readFileSync(pagePath, 'utf8');
    if (pageContent.includes('window.location.href')) {
      console.log(`    ✅ Redirection forcée configurée`);
    } else if (pageContent.includes('router.push')) {
      console.log(`    ⚠️  Utilise router.push (peut causer des problèmes)`);
    }
  } else {
    console.log(`  ❌ ${pagePath} manquant`);
  }
});

// 6. Générer un script de test d'authentification
console.log('\n🧪 6. Génération du script de test...');

const testScript = `
// Test d'authentification rapide
import { signIn } from 'next-auth/react';

async function testAuth() {
  try {
    const result = await signIn('credentials', {
      email: '<EMAIL>',
      password: 'password123',
      redirect: false,
    });
    
    console.log('Résultat de connexion:', result);
    
    if (result?.ok) {
      console.log('✅ Authentification réussie');
      window.location.href = '/dashboard';
    } else {
      console.log('❌ Échec de l\'authentification:', result?.error);
    }
  } catch (error) {
    console.error('❌ Erreur:', error);
  }
}

// Exécuter le test
testAuth();
`;

fs.writeFileSync('scripts/test-auth.js', testScript);
console.log('✅ Script de test créé: scripts/test-auth.js');

// 7. Résumé et recommandations
console.log('\n📊 RÉSUMÉ DES CORRECTIONS:');
console.log(`  🔧 Variables d'environnement: ${envIssues.length === 0 ? '✅ OK' : '🔧 Corrigées'}`);
console.log(`  ⚙️  Configuration NextAuth: ${fs.existsSync(authConfigPath) ? '✅ OK' : '❌ À vérifier'}`);
console.log(`  🛡️  Middleware: ${fs.existsSync(middlewarePath) ? '✅ OK' : '⚠️  Recommandé'}`);
console.log(`  🗄️  Base de données: ${fs.existsSync(dbPath) ? '✅ OK' : '⚠️  À créer'}`);

console.log('\n🎯 ACTIONS RECOMMANDÉES:');
console.log('  1. Créer les utilisateurs de test: npx tsx scripts/create-test-users.ts');
console.log('  2. Redémarrer le serveur: npm run dev');
console.log('  3. Tester la connexion manuellement');
console.log('  4. Exécuter les tests E2E: npm run test:e2e');

console.log('\n💡 CONSEILS DE DÉBOGAGE:');
console.log('  - Vérifiez les logs du navigateur (F12)');
console.log('  - Vérifiez les logs du serveur Next.js');
console.log('  - Testez avec différents navigateurs');
console.log('  - Videz le cache du navigateur');

console.log('\n🔧 Diagnostic terminé !');
