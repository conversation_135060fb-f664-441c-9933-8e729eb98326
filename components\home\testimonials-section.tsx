'use client'

import { Star, Quote } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { motion } from 'framer-motion'

const testimonials = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    location: 'Casablanca',
    avatar: 'https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=150',
    rating: 5,
    comment: 'J\'ai trouvé un excellent plombier grâce à QribLik. Intervention rapide et tarif honnête. Je recommande !',
    service: 'Plomberie'
  },
  {
    id: 2,
    name: '<PERSON>',
    location: 'Rabat',
    avatar: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150',
    rating: 5,
    comment: 'Plateforme très pratique pour proposer mes services de jardinage. J\'ai maintenant une clientèle régulière.',
    service: 'J<PERSON>ina<PERSON>'
  },
  {
    id: 3,
    name: '<PERSON><PERSON><PERSON>',
    location: 'Marrakech',
    avatar: 'https://images.pexels.com/photos/762020/pexels-photo-762020.jpeg?auto=compress&cs=tinysrgb&w=150',
    rating: 5,
    comment: 'Mes enfants ont trouvé d\'excellents cours particuliers en maths. Les résultats sont au rendez-vous !',
    service: 'Cours particuliers'
  }
]

export function TestimonialsSection() {
  return (
    <section className="py-16 lg:py-24 bg-muted/30">
      <div className="container">
        <motion.div 
          className="text-center space-y-4 mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl font-bold">
            Ce que disent nos utilisateurs
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Découvrez les témoignages de notre communauté
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              className="bg-card p-6 rounded-xl border border-border shadow-sm"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2, duration: 0.6 }}
              viewport={{ once: true }}
            >
              <div className="space-y-4">
                {/* Quote Icon */}
                <Quote className="h-8 w-8 text-primary/30" />
                
                {/* Rating */}
                <div className="flex items-center space-x-1">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>

                {/* Comment */}
                <p className="text-muted-foreground leading-relaxed">
                  "{testimonial.comment}"
                </p>

                {/* Service Tag */}
                <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                  {testimonial.service}
                </div>

                {/* Author */}
                <div className="flex items-center space-x-3 pt-4 border-t border-border">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                    <AvatarFallback>
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-semibold text-sm">
                      {testimonial.name}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {testimonial.location}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}