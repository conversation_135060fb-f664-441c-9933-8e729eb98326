export const portfolioFixtures = [
  {
    id: '1',
    title: 'Rénovation Cuisine Moderne',
    description: 'Transformation complète d\'une cuisine traditionnelle en espace moderne avec îlot central.',
    category: 'BRICOLAGE_REPARATIONS',
    images: ['https://images.unsplash.com/photo-1556909114-f6e7ad7d3136'],
    videos: [],
    beforeImages: ['https://images.unsplash.com/photo-1556909114-f6e7ad7d3136'],
    afterImages: ['https://images.unsplash.com/photo-1556909114-f6e7ad7d3136'],
    projectDate: '2024-08-15T00:00:00.000Z',
    duration: '2 semaines',
    clientTestimonial: 'Travail exceptionnel ! Ma cuisine est maintenant le cœur de ma maison.',
    tags: ['rénovation', 'cuisine', 'moderne', 'îlot'],
    isPublic: true,
    isFeatured: true,
    viewCount: 45,
    likeCount: 12,
    shareCount: 3,
    locationCity: 'Casablanca',
    locationRegion: 'Casablanca-Settat',
    locationAddress: 'Quartier Maarif',
    locationLat: 33.5731,
    locationLng: -7.5898,
    userId: '1',
    createdAt: '2024-08-15T00:00:00.000Z',
    updatedAt: '2024-08-15T00:00:00.000Z',
    user: {
      id: '1',
      name: 'Ahmed Bennani',
      image: null,
      rating: 4.8,
      reviewCount: 24,
      locationCity: 'Casablanca',
      locationRegion: 'Casablanca-Settat',
    },
  },
  {
    id: '2',
    title: 'Nettoyage Complet Villa',
    description: 'Service de nettoyage approfondi d\'une villa de 300m² avec jardins.',
    category: 'MENAGE_NETTOYAGE',
    images: ['https://images.unsplash.com/photo-1558618666-fcd25c85cd64'],
    videos: [],
    beforeImages: [],
    afterImages: [],
    projectDate: '2024-09-01T00:00:00.000Z',
    duration: '1 jour',
    clientTestimonial: 'Service impeccable, très professionnel et ponctuel.',
    tags: ['nettoyage', 'villa', 'professionnel'],
    isPublic: true,
    isFeatured: false,
    viewCount: 23,
    likeCount: 8,
    shareCount: 1,
    locationCity: 'Rabat',
    locationRegion: 'Rabat-Salé-Kénitra',
    locationAddress: null,
    locationLat: null,
    locationLng: null,
    userId: '2',
    createdAt: '2024-09-01T00:00:00.000Z',
    updatedAt: '2024-09-01T00:00:00.000Z',
    user: {
      id: '2',
      name: 'Fatima Alaoui',
      image: null,
      rating: 4.6,
      reviewCount: 18,
      locationCity: 'Rabat',
      locationRegion: 'Rabat-Salé-Kénitra',
    },
  },
  {
    id: '3',
    title: 'Aménagement Jardin Zen',
    description: 'Création d\'un espace zen avec fontaine, bambous et chemin de pierres.',
    category: 'JARDINAGE_ESPACES_VERTS',
    images: ['https://images.unsplash.com/photo-1416879595882-3373a0480b5b'],
    videos: [],
    beforeImages: ['https://images.unsplash.com/photo-1416879595882-3373a0480b5b'],
    afterImages: ['https://images.unsplash.com/photo-1416879595882-3373a0480b5b'],
    projectDate: '2024-07-20T00:00:00.000Z',
    duration: '1 semaine',
    clientTestimonial: 'Mon jardin est devenu un véritable havre de paix.',
    tags: ['jardin', 'zen', 'aménagement', 'paysage'],
    isPublic: true,
    isFeatured: false,
    viewCount: 67,
    likeCount: 18,
    shareCount: 5,
    locationCity: 'Marrakech',
    locationRegion: 'Marrakech-Safi',
    locationAddress: null,
    locationLat: null,
    locationLng: null,
    userId: '3',
    createdAt: '2024-07-20T00:00:00.000Z',
    updatedAt: '2024-07-20T00:00:00.000Z',
    user: {
      id: '3',
      name: 'Youssef Tazi',
      image: null,
      rating: 4.9,
      reviewCount: 31,
      locationCity: 'Marrakech',
      locationRegion: 'Marrakech-Safi',
    },
  },
]

export const userFixtures = {
  prestataire: {
    id: '1',
    name: 'Ahmed Bennani',
    email: '<EMAIL>',
    userType: 'PRESTATAIRE',
    locationCity: 'Casablanca',
    locationRegion: 'Casablanca-Settat',
    rating: 4.8,
    reviewCount: 24,
  },
  client: {
    id: '2',
    name: 'Fatima Client',
    email: '<EMAIL>',
    userType: 'CLIENT',
    locationCity: 'Rabat',
    locationRegion: 'Rabat-Salé-Kénitra',
    rating: null,
    reviewCount: 0,
  },
  both: {
    id: '3',
    name: 'Youssef Both',
    email: '<EMAIL>',
    userType: 'BOTH',
    locationCity: 'Marrakech',
    locationRegion: 'Marrakech-Safi',
    rating: 4.5,
    reviewCount: 12,
  },
}

export const availabilityFixtures = [
  {
    id: '1',
    userId: '1',
    dayOfWeek: 1, // Lundi
    startTime: '08:00',
    endTime: '12:00',
    isAvailable: true,
    slotType: 'WORK',
    createdAt: '2024-09-01T00:00:00.000Z',
    updatedAt: '2024-09-01T00:00:00.000Z',
  },
  {
    id: '2',
    userId: '1',
    dayOfWeek: 1, // Lundi
    startTime: '14:00',
    endTime: '18:00',
    isAvailable: true,
    slotType: 'WORK',
    createdAt: '2024-09-01T00:00:00.000Z',
    updatedAt: '2024-09-01T00:00:00.000Z',
  },
  {
    id: '3',
    userId: '1',
    dayOfWeek: 2, // Mardi
    startTime: '08:00',
    endTime: '12:00',
    isAvailable: true,
    slotType: 'WORK',
    createdAt: '2024-09-01T00:00:00.000Z',
    updatedAt: '2024-09-01T00:00:00.000Z',
  },
]

export const notificationFixtures = [
  {
    id: '1',
    title: 'Nouveau match trouvé !',
    message: 'Un client recherche vos services de bricolage à Casablanca.',
    notificationType: 'NEW_MATCH',
    userId: '1',
    relatedId: 'match-1',
    relatedType: 'MATCH',
    actionUrl: '/dashboard/matches/match-1',
    isRead: false,
    isEmailSent: false,
    isPushSent: false,
    createdAt: '2024-09-17T10:00:00.000Z',
    updatedAt: '2024-09-17T10:00:00.000Z',
  },
  {
    id: '2',
    title: 'Message reçu',
    message: 'Ahmed vous a envoyé un message concernant votre devis.',
    notificationType: 'MESSAGE_RECEIVED',
    userId: '1',
    relatedId: 'message-1',
    relatedType: 'MESSAGE',
    actionUrl: '/dashboard/messages',
    isRead: false,
    isEmailSent: true,
    isPushSent: true,
    createdAt: '2024-09-17T09:30:00.000Z',
    updatedAt: '2024-09-17T09:30:00.000Z',
  },
  {
    id: '3',
    title: 'Paiement reçu',
    message: 'Vous avez reçu un paiement de 450 MAD pour votre dernière intervention.',
    notificationType: 'PAYMENT_RECEIVED',
    userId: '1',
    relatedId: 'payment-1',
    relatedType: 'PAYMENT',
    actionUrl: '/dashboard/payments',
    isRead: true,
    isEmailSent: true,
    isPushSent: true,
    createdAt: '2024-09-17T08:00:00.000Z',
    updatedAt: '2024-09-17T08:00:00.000Z',
  },
]
