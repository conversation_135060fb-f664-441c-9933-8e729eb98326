'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  User, 
  Camera, 
  MapPin, 
  Phone, 
  Mail, 
  Calendar,
  Star,
  Award,
  Shield,
  Edit,
  Save,
  X,
  Plus,
  Trash2
} from 'lucide-react'
import { motion } from 'framer-motion'

interface UserProfile {
  id: string
  name: string
  email: string
  phone?: string
  image?: string
  bio?: string
  locationCity?: string
  locationRegion?: string
  dateOfBirth?: string
  profession?: string
  rating: number
  reviewCount: number
  completionRate: number
  responseTime: number
  joinedAt: string
  isVerified: boolean
  skills: string[]
  languages: string[]
}

const cities = [
  'Casablanca', 'Rabat', 'Marrakech', 'Fès', 'Agadir', 
  'Tanger', 'Meknès', 'Oujda', 'Kénitra', 'Tétouan'
]

const regions = [
  'Casablanca-Settat', 'Rabat-Salé-Kénitra', 'Marrakech-Safi', 
  'Fès-Meknès', 'Souss-Massa', 'Tanger-Tétouan-Al Hoceïma',
  'Oriental', 'Béni Mellal-Khénifra', 'Drâa-Tafilalet', 
  'Laâyoune-Sakia El Hamra', 'Dakhla-Oued Ed-Dahab', 'Guelmim-Oued Noun'
]

export default function DashboardProfilePage() {
  const [isEditing, setIsEditing] = useState(false)
  const [profile, setProfile] = useState<UserProfile>({
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+212 6 12 34 56 78',
    image: '/placeholder-avatar.jpg',
    bio: 'Prestataire de services expérimenté avec plus de 5 ans d\'expérience dans le bricolage et la réparation. Passionné par l\'aide aux autres et la résolution de problèmes.',
    locationCity: 'Casablanca',
    locationRegion: 'Casablanca-Settat',
    dateOfBirth: '1985-06-15',
    profession: 'Artisan',
    rating: 4.8,
    reviewCount: 127,
    completionRate: 96,
    responseTime: 15,
    joinedAt: '2023-01-15',
    isVerified: true,
    skills: ['Plomberie', 'Électricité', 'Peinture', 'Jardinage'],
    languages: ['Français', 'Arabe', 'Anglais']
  })

  const [editedProfile, setEditedProfile] = useState(profile)
  const [newSkill, setNewSkill] = useState('')
  const [newLanguage, setNewLanguage] = useState('')

  const handleSave = () => {
    setProfile(editedProfile)
    setIsEditing(false)
    // Ici, vous ajouteriez l'appel API pour sauvegarder
  }

  const handleCancel = () => {
    setEditedProfile(profile)
    setIsEditing(false)
  }

  const addSkill = () => {
    if (newSkill.trim() && !editedProfile.skills.includes(newSkill.trim())) {
      setEditedProfile({
        ...editedProfile,
        skills: [...editedProfile.skills, newSkill.trim()]
      })
      setNewSkill('')
    }
  }

  const removeSkill = (skill: string) => {
    setEditedProfile({
      ...editedProfile,
      skills: editedProfile.skills.filter(s => s !== skill)
    })
  }

  const addLanguage = () => {
    if (newLanguage.trim() && !editedProfile.languages.includes(newLanguage.trim())) {
      setEditedProfile({
        ...editedProfile,
        languages: [...editedProfile.languages, newLanguage.trim()]
      })
      setNewLanguage('')
    }
  }

  const removeLanguage = (language: string) => {
    setEditedProfile({
      ...editedProfile,
      languages: editedProfile.languages.filter(l => l !== language)
    })
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="min-h-screen bg-muted/30">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Mon profil</h1>
              <p className="text-muted-foreground">
                Gérez vos informations personnelles et professionnelles
              </p>
            </div>
            <div className="flex space-x-3">
              {isEditing ? (
                <>
                  <Button variant="outline" onClick={handleCancel}>
                    <X className="mr-2 h-4 w-4" />
                    Annuler
                  </Button>
                  <Button onClick={handleSave}>
                    <Save className="mr-2 h-4 w-4" />
                    Sauvegarder
                  </Button>
                </>
              ) : (
                <Button onClick={() => setIsEditing(true)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Modifier le profil
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="container py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Overview */}
          <div className="lg:col-span-1 space-y-6">
            {/* Avatar & Basic Info */}
            <Card>
              <CardContent className="p-6 text-center">
                <div className="relative inline-block mb-4">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src={profile.image} />
                    <AvatarFallback className="text-lg">
                      {profile.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  {isEditing && (
                    <Button
                      size="icon"
                      className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full"
                    >
                      <Camera className="h-4 w-4" />
                    </Button>
                  )}
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-center space-x-2">
                    <h2 className="text-xl font-bold">{profile.name}</h2>
                    {profile.isVerified && (
                      <Badge className="bg-blue-100 text-blue-800">
                        <Shield className="h-3 w-3 mr-1" />
                        Vérifié
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-muted-foreground">{profile.profession}</p>
                  
                  <div className="flex items-center justify-center space-x-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{profile.rating}</span>
                    <span className="text-muted-foreground">({profile.reviewCount} avis)</span>
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="font-medium text-2xl text-green-600">{profile.completionRate}%</div>
                    <div className="text-muted-foreground">Taux de réussite</div>
                  </div>
                  <div>
                    <div className="font-medium text-2xl text-blue-600">{profile.responseTime}min</div>
                    <div className="text-muted-foreground">Temps de réponse</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="h-5 w-5 mr-2" />
                  Statistiques
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Membre depuis</span>
                  <span className="font-medium">{formatDate(profile.joinedAt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Services actifs</span>
                  <span className="font-medium">12</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Projets terminés</span>
                  <span className="font-medium">89</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Revenus totaux</span>
                  <span className="font-medium">15,750 MAD</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Profile Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Informations personnelles
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nom complet</Label>
                    {isEditing ? (
                      <Input
                        id="name"
                        value={editedProfile.name}
                        onChange={(e) => setEditedProfile({...editedProfile, name: e.target.value})}
                      />
                    ) : (
                      <p className="text-sm p-2 bg-muted rounded">{profile.name}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    {isEditing ? (
                      <Input
                        id="email"
                        type="email"
                        value={editedProfile.email}
                        onChange={(e) => setEditedProfile({...editedProfile, email: e.target.value})}
                      />
                    ) : (
                      <p className="text-sm p-2 bg-muted rounded flex items-center">
                        <Mail className="h-4 w-4 mr-2" />
                        {profile.email}
                      </p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="phone">Téléphone</Label>
                    {isEditing ? (
                      <Input
                        id="phone"
                        value={editedProfile.phone || ''}
                        onChange={(e) => setEditedProfile({...editedProfile, phone: e.target.value})}
                      />
                    ) : (
                      <p className="text-sm p-2 bg-muted rounded flex items-center">
                        <Phone className="h-4 w-4 mr-2" />
                        {profile.phone || 'Non renseigné'}
                      </p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="profession">Profession</Label>
                    {isEditing ? (
                      <Input
                        id="profession"
                        value={editedProfile.profession || ''}
                        onChange={(e) => setEditedProfile({...editedProfile, profession: e.target.value})}
                      />
                    ) : (
                      <p className="text-sm p-2 bg-muted rounded">{profile.profession || 'Non renseigné'}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bio">Biographie</Label>
                  {isEditing ? (
                    <Textarea
                      id="bio"
                      rows={4}
                      value={editedProfile.bio || ''}
                      onChange={(e) => setEditedProfile({...editedProfile, bio: e.target.value})}
                      placeholder="Décrivez-vous et vos compétences..."
                    />
                  ) : (
                    <p className="text-sm p-3 bg-muted rounded leading-relaxed">
                      {profile.bio || 'Aucune biographie renseignée'}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Location */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  Localisation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">Ville</Label>
                    {isEditing ? (
                      <Select 
                        value={editedProfile.locationCity || ''} 
                        onValueChange={(value) => setEditedProfile({...editedProfile, locationCity: value})}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionnez une ville" />
                        </SelectTrigger>
                        <SelectContent>
                          {cities.map((city) => (
                            <SelectItem key={city} value={city}>{city}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-sm p-2 bg-muted rounded">{profile.locationCity || 'Non renseigné'}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="region">Région</Label>
                    {isEditing ? (
                      <Select 
                        value={editedProfile.locationRegion || ''} 
                        onValueChange={(value) => setEditedProfile({...editedProfile, locationRegion: value})}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionnez une région" />
                        </SelectTrigger>
                        <SelectContent>
                          {regions.map((region) => (
                            <SelectItem key={region} value={region}>{region}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-sm p-2 bg-muted rounded">{profile.locationRegion || 'Non renseigné'}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Skills & Languages */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Skills */}
              <Card>
                <CardHeader>
                  <CardTitle>Compétences</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    {(isEditing ? editedProfile.skills : profile.skills).map((skill) => (
                      <Badge key={skill} variant="secondary" className="flex items-center gap-1">
                        {skill}
                        {isEditing && (
                          <button
                            onClick={() => removeSkill(skill)}
                            className="ml-1 hover:text-red-500"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        )}
                      </Badge>
                    ))}
                  </div>
                  
                  {isEditing && (
                    <div className="flex gap-2">
                      <Input
                        placeholder="Nouvelle compétence"
                        value={newSkill}
                        onChange={(e) => setNewSkill(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && addSkill()}
                      />
                      <Button size="sm" onClick={addSkill}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Languages */}
              <Card>
                <CardHeader>
                  <CardTitle>Langues</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    {(isEditing ? editedProfile.languages : profile.languages).map((language) => (
                      <Badge key={language} variant="outline" className="flex items-center gap-1">
                        {language}
                        {isEditing && (
                          <button
                            onClick={() => removeLanguage(language)}
                            className="ml-1 hover:text-red-500"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        )}
                      </Badge>
                    ))}
                  </div>
                  
                  {isEditing && (
                    <div className="flex gap-2">
                      <Input
                        placeholder="Nouvelle langue"
                        value={newLanguage}
                        onChange={(e) => setNewLanguage(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && addLanguage()}
                      />
                      <Button size="sm" onClick={addLanguage}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
