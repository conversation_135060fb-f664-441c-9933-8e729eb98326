import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createConnectedAccount, createAccountLink } from '@/lib/stripe'

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user) {
      return NextResponse.json({ error: 'Utilisateur non trouvé' }, { status: 404 })
    }

    let accountId = user.stripeAccountId

    // Créer un compte connecté s'il n'existe pas
    if (!accountId) {
      const account = await createConnectedAccount(
        user.email,
        'MA',
        {
          userId: user.id,
          userName: user.name || '',
        }
      )

      accountId = account.id

      // Mettre à jour l'utilisateur avec l'ID du compte Stripe
      await prisma.user.update({
        where: { id: user.id },
        data: {
          stripeAccountId: accountId,
          stripeAccountStatus: 'PENDING'
        }
      })
    }

    // Créer le lien d'onboarding
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'
    const accountLink = await createAccountLink(
      accountId,
      `${baseUrl}/dashboard/payments/connect?refresh=true`,
      `${baseUrl}/dashboard/payments/connect?success=true`
    )

    return NextResponse.json({
      accountId,
      onboardingUrl: accountLink.url
    })

  } catch (error) {
    console.error('Erreur lors de la création du compte connecté:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        stripeAccountId: true,
        stripeAccountStatus: true,
        canReceivePayments: true
      }
    })

    if (!user) {
      return NextResponse.json({ error: 'Utilisateur non trouvé' }, { status: 404 })
    }

    return NextResponse.json({
      hasConnectedAccount: !!user.stripeAccountId,
      accountStatus: user.stripeAccountStatus,
      canReceivePayments: user.canReceivePayments
    })

  } catch (error) {
    console.error('Erreur lors de la récupération du statut du compte:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
