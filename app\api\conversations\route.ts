import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createConversationSchema = z.object({
  matchId: z.string().optional(),
  requestId: z.string().optional(),
  participantIds: z.array(z.string()).min(1, 'Au moins un participant requis'),
})

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    // Récupérer les conversations de l'utilisateur
    const conversations = await prisma.conversation.findMany({
      where: {
        participants: {
          some: {
            userId: session.user.id
          }
        },
        isActive: true
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
                userType: true
              }
            }
          }
        },
        match: {
          include: {
            request: {
              select: {
                id: true,
                title: true
              }
            },
            service: {
              select: {
                id: true,
                title: true
              }
            }
          }
        },
        request: {
          select: {
            id: true,
            title: true
          }
        },
        messages: {
          where: {
            isDeleted: false
          },
          orderBy: { createdAt: 'desc' },
          take: 1,
          include: {
            sender: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      orderBy: { lastMessageAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    })

    // Compter les messages non lus pour chaque conversation
    const conversationsWithUnreadCount = await Promise.all(
      conversations.map(async (conversation) => {
        const unreadCount = await prisma.message.count({
          where: {
            conversationId: conversation.id,
            senderId: { not: session.user.id },
            readAt: null,
            isDeleted: false
          }
        })

        return {
          ...conversation,
          unreadCount,
          otherParticipants: conversation.participants.filter(
            p => p.userId !== session.user.id
          )
        }
      })
    )

    return NextResponse.json({
      conversations: conversationsWithUnreadCount,
      pagination: {
        page,
        limit,
        hasMore: conversations.length === limit
      }
    })

  } catch (error) {
    console.error('Erreur lors de la récupération des conversations:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createConversationSchema.parse(body)

    // Vérifier si une conversation existe déjà pour ce match/request
    let existingConversation = null
    if (validatedData.matchId) {
      existingConversation = await prisma.conversation.findFirst({
        where: { matchId: validatedData.matchId }
      })
    } else if (validatedData.requestId) {
      existingConversation = await prisma.conversation.findFirst({
        where: { requestId: validatedData.requestId }
      })
    }

    if (existingConversation) {
      return NextResponse.json({ 
        conversation: existingConversation,
        message: 'Conversation existante trouvée'
      })
    }

    // Créer une nouvelle conversation
    const conversation = await prisma.conversation.create({
      data: {
        matchId: validatedData.matchId,
        requestId: validatedData.requestId,
        isActive: true
      }
    })

    // Ajouter les participants (incluant l'utilisateur actuel)
    const participantIds = Array.from(new Set([...validatedData.participantIds, session.user.id]))
    
    await Promise.all(
      participantIds.map(userId =>
        prisma.conversationParticipant.create({
          data: {
            conversationId: conversation.id,
            userId
          }
        })
      )
    )

    // Récupérer la conversation complète
    const fullConversation = await prisma.conversation.findUnique({
      where: { id: conversation.id },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
                userType: true
              }
            }
          }
        },
        match: {
          include: {
            request: {
              select: {
                id: true,
                title: true
              }
            },
            service: {
              select: {
                id: true,
                title: true
              }
            }
          }
        },
        request: {
          select: {
            id: true,
            title: true
          }
        }
      }
    })

    return NextResponse.json({ conversation: fullConversation })

  } catch (error) {
    console.error('Erreur lors de la création de la conversation:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Données invalides', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
