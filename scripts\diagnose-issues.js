const fs = require('fs');
const path = require('path');

console.log('🔍 DIAGNOSTIC COMPLET - QribLik\n');

// 1. Vérifier les dépendances critiques
console.log('📦 1. Vérification des dépendances...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

const criticalDeps = [
  'sonner', 'next-themes', 'date-fns', 'lucide-react', 
  'next-auth', '@prisma/client', 'zod', 'framer-motion',
  'react', 'next', 'typescript'
];

let allDepsOk = true;
criticalDeps.forEach(dep => {
  if (dependencies[dep]) {
    console.log(`  ✅ ${dep}`);
  } else {
    console.log(`  ❌ ${dep} - MANQUANT`);
    allDepsOk = false;
  }
});

// 2. Vérifier les composants UI
console.log('\n🎨 2. Vérification des composants UI...');
const uiComponents = [
  'button.tsx', 'card.tsx', 'badge.tsx', 'tabs.tsx', 
  'dialog.tsx', 'input.tsx', 'label.tsx', 'textarea.tsx',
  'select.tsx', 'switch.tsx', 'popover.tsx', 'scroll-area.tsx', 'sonner.tsx'
];

let allComponentsOk = true;
uiComponents.forEach(component => {
  const componentPath = path.join('components', 'ui', component);
  if (fs.existsSync(componentPath)) {
    console.log(`  ✅ ${component}`);
  } else {
    console.log(`  ❌ ${component} - MANQUANT`);
    allComponentsOk = false;
  }
});

// 3. Vérifier les nouvelles pages
console.log('\n📄 3. Vérification des nouvelles pages...');
const newPages = [
  'app/dashboard/portfolio/page.tsx',
  'app/dashboard/agenda/page.tsx', 
  'app/dashboard/map/page.tsx'
];

let allPagesOk = true;
newPages.forEach(page => {
  if (fs.existsSync(page)) {
    console.log(`  ✅ ${page}`);
  } else {
    console.log(`  ❌ ${page} - MANQUANT`);
    allPagesOk = false;
  }
});

// 4. Vérifier les APIs
console.log('\n🌐 4. Vérification des APIs...');
const newApis = [
  'app/api/portfolio/route.ts',
  'app/api/availability/route.ts',
  'app/api/bookings/route.ts',
  'app/api/notifications/route.ts'
];

let allApisOk = true;
newApis.forEach(api => {
  if (fs.existsSync(api)) {
    console.log(`  ✅ ${api}`);
  } else {
    console.log(`  ❌ ${api} - MANQUANT`);
    allApisOk = false;
  }
});

// 5. Vérifier les nouveaux composants
console.log('\n🧩 5. Vérification des nouveaux composants...');
const newComponents = [
  'components/social/social-share.tsx',
  'components/maps/demand-heatmap.tsx',
  'components/notifications/notification-center.tsx'
];

let allNewComponentsOk = true;
newComponents.forEach(component => {
  if (fs.existsSync(component)) {
    console.log(`  ✅ ${component}`);
  } else {
    console.log(`  ❌ ${component} - MANQUANT`);
    allNewComponentsOk = false;
  }
});

// 6. Vérifier la configuration
console.log('\n⚙️ 6. Vérification de la configuration...');
const configFiles = [
  'app/providers.tsx',
  'lib/auth.ts',
  'lib/prisma.ts',
  'prisma/schema.prisma'
];

let allConfigOk = true;
configFiles.forEach(config => {
  if (fs.existsSync(config)) {
    console.log(`  ✅ ${config}`);
  } else {
    console.log(`  ❌ ${config} - MANQUANT`);
    allConfigOk = false;
  }
});

// 7. Vérifier les imports problématiques
console.log('\n🔍 7. Vérification des imports...');
const filesToCheck = [
  'app/dashboard/portfolio/page.tsx',
  'app/dashboard/agenda/page.tsx'
];

filesToCheck.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    
    // Vérifier les imports problématiques
    const problematicImports = [
      "import { toast } from 'sonner'",
      "import { useSession } from 'next-auth/react'",
      "from 'lucide-react'",
      "from '@/components/ui/"
    ];
    
    console.log(`  📄 ${file}:`);
    problematicImports.forEach(imp => {
      if (content.includes(imp)) {
        console.log(`    ✅ ${imp}`);
      } else {
        console.log(`    ⚠️  ${imp} - Non trouvé`);
      }
    });
  }
});

// Résumé final
console.log('\n📊 RÉSUMÉ DU DIAGNOSTIC:');
console.log(`  📦 Dépendances: ${allDepsOk ? '✅ OK' : '❌ Problèmes détectés'}`);
console.log(`  🎨 Composants UI: ${allComponentsOk ? '✅ OK' : '❌ Problèmes détectés'}`);
console.log(`  📄 Nouvelles pages: ${allPagesOk ? '✅ OK' : '❌ Problèmes détectés'}`);
console.log(`  🌐 Nouvelles APIs: ${allApisOk ? '✅ OK' : '❌ Problèmes détectés'}`);
console.log(`  🧩 Nouveaux composants: ${allNewComponentsOk ? '✅ OK' : '❌ Problèmes détectés'}`);
console.log(`  ⚙️ Configuration: ${allConfigOk ? '✅ OK' : '❌ Problèmes détectés'}`);

const allOk = allDepsOk && allComponentsOk && allPagesOk && allApisOk && allNewComponentsOk && allConfigOk;

console.log('\n🎯 ACTIONS RECOMMANDÉES:');
if (!allDepsOk) {
  console.log('  1. Installer les dépendances manquantes: npm install');
}
if (!allOk) {
  console.log('  2. Vérifier les erreurs de compilation dans le terminal');
  console.log('  3. Redémarrer le serveur: npm run dev');
}
console.log('  4. Tester les pages une par une');
console.log('  5. Vérifier les logs du navigateur (F12)');

if (allOk) {
  console.log('\n🎉 DIAGNOSTIC: Tous les fichiers sont présents !');
  console.log('   Le problème pourrait être lié à:');
  console.log('   - Configuration NextAuth (NEXTAUTH_SECRET)');
  console.log('   - Cache de Next.js (.next/cache)');
  console.log('   - Erreurs TypeScript');
} else {
  console.log('\n⚠️  DIAGNOSTIC: Problèmes détectés - Voir les détails ci-dessus');
}
