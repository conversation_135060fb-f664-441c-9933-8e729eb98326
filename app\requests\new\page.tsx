'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, ArrowLeft, Plus, X, Calendar } from 'lucide-react'
import Link from 'next/link'

const categories = [
  { value: 'BRICOLAGE_REPARATIONS', label: 'Bricolage & Réparations' },
  { value: 'MENAGE_NETTOYAGE', label: 'Ménage & Nettoyage' },
  { value: 'JARDINAGE_ESPACES_VERTS', label: 'Jardinage & Espaces verts' },
  { value: 'DEMENAGEMENT_TRANSPORT', label: 'Déménagement & Transport' },
  { value: 'COURS_PARTICULIERS', label: 'Cours particuliers' },
  { value: 'SERVICES_PERSONNE', label: 'Services à la personne' },
  { value: 'EVENEMENTS_ANIMATION', label: 'Événements & Animation' },
  { value: 'BEAUTE_BIEN_ETRE', label: 'Beauté & Bien-être' },
  { value: 'INFORMATIQUE_TECH', label: 'Informatique & Tech' },
  { value: 'AUTOMOBILE', label: 'Automobile' },
  { value: 'IMMOBILIER', label: 'Immobilier' },
  { value: 'AUTRES', label: 'Autres' }
]

const budgetTypes = [
  { value: 'FIXED', label: 'Budget fixe' },
  { value: 'HOURLY', label: 'Budget horaire' },
  { value: 'DAILY', label: 'Budget journalier' },
  { value: 'NEGOTIABLE', label: 'Négociable' }
]

const urgencyLevels = [
  { value: 'LOW', label: 'Faible - Pas pressé' },
  { value: 'NORMAL', label: 'Normal - Dans la semaine' },
  { value: 'HIGH', label: 'Élevé - Dans les 2-3 jours' },
  { value: 'EMERGENCY', label: 'Urgent - Aujourd\'hui/demain' }
]

const cities = [
  'Casablanca', 'Rabat', 'Marrakech', 'Fès', 'Agadir', 'Tanger', 'Meknès', 'Oujda', 'Kénitra', 'Tétouan'
]

export default function NewRequestPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState('')

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    subCategory: '',
    budget: '',
    budgetType: 'FIXED',
    urgency: 'NORMAL',
    deadline: '',
    images: '',
    attachments: '',
    isPublic: true,
    locationCity: '',
    locationRegion: '',
    requirements: ''
  })

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-muted/30">
        <Header />
        <main className="container py-8">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return (
      <div className="min-h-screen bg-muted/30">
        <Header />
        <main className="container py-8">
          <Card className="text-center py-12">
            <CardContent>
              <h2 className="text-2xl font-bold mb-4">Connexion requise</h2>
              <p className="text-muted-foreground mb-6">
                Vous devez être connecté pour créer une demande.
              </p>
              <Button asChild>
                <Link href="/auth/signin">Se connecter</Link>
              </Button>
            </CardContent>
          </Card>
        </main>
        <Footer />
      </div>
    )
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags(prev => [...prev, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(prev => prev.filter(tag => tag !== tagToRemove))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          budget: formData.budget ? parseFloat(formData.budget) : undefined,
          deadline: formData.deadline ? new Date(formData.deadline).toISOString() : undefined,
          tags,
          images: formData.images ? formData.images.split(',').map(img => img.trim()) : [],
          attachments: formData.attachments ? formData.attachments.split(',').map(att => att.trim()) : []
        }),
      })

      if (response.ok) {
        const request = await response.json()
        router.push(`/requests/${request.id}`)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Erreur lors de la création de la demande')
      }
    } catch (error) {
      setError('Une erreur est survenue. Veuillez réessayer.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-muted/30">
      <Header />
      
      <main className="container py-8">
        {/* Back Button */}
        <Button variant="ghost" asChild className="mb-6">
          <Link href="/requests">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour aux demandes
          </Link>
        </Button>

        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Créer une nouvelle demande</CardTitle>
              <CardDescription>
                Décrivez votre besoin et trouvez le prestataire idéal
              </CardDescription>
            </CardHeader>
            <CardContent>
              {error && (
                <Alert variant="destructive" className="mb-6">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Informations de base</h3>
                  
                  <div className="space-y-2">
                    <Label htmlFor="title">Titre de la demande *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      placeholder="Ex: Recherche plombier pour réparation urgente"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description détaillée *</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Décrivez votre besoin en détail : problème, contexte, attentes..."
                      rows={5}
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="category">Catégorie *</Label>
                      <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionnez une catégorie" />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category.value} value={category.value}>
                              {category.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="subCategory">Sous-catégorie</Label>
                      <Input
                        id="subCategory"
                        value={formData.subCategory}
                        onChange={(e) => handleInputChange('subCategory', e.target.value)}
                        placeholder="Ex: Plomberie, Électricité..."
                      />
                    </div>
                  </div>
                </div>

                {/* Budget & Urgency */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Budget et urgence</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="budget">Budget (MAD)</Label>
                      <Input
                        id="budget"
                        type="number"
                        value={formData.budget}
                        onChange={(e) => handleInputChange('budget', e.target.value)}
                        placeholder="500"
                        min="0"
                        step="0.01"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="budgetType">Type de budget</Label>
                      <Select value={formData.budgetType} onValueChange={(value) => handleInputChange('budgetType', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {budgetTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="urgency">Niveau d'urgence</Label>
                      <Select value={formData.urgency} onValueChange={(value) => handleInputChange('urgency', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {urgencyLevels.map((level) => (
                            <SelectItem key={level.value} value={level.value}>
                              {level.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="deadline">Date limite (optionnel)</Label>
                      <Input
                        id="deadline"
                        type="datetime-local"
                        value={formData.deadline}
                        onChange={(e) => handleInputChange('deadline', e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* Tags */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Tags</h3>
                  <p className="text-sm text-muted-foreground">
                    Ajoutez des mots-clés pour aider les prestataires à trouver votre demande
                  </p>
                  
                  <div className="flex space-x-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Ajouter un tag"
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    />
                    <Button type="button" onClick={addTag} variant="outline">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  {tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {tags.map((tag, index) => (
                        <div key={index} className="flex items-center space-x-1 bg-muted px-2 py-1 rounded">
                          <span className="text-sm">{tag}</span>
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="text-muted-foreground hover:text-foreground"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Location */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Localisation</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="locationCity">Ville</Label>
                      <Select value={formData.locationCity} onValueChange={(value) => handleInputChange('locationCity', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionnez une ville" />
                        </SelectTrigger>
                        <SelectContent>
                          {cities.map((city) => (
                            <SelectItem key={city} value={city}>
                              {city}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="locationRegion">Région</Label>
                      <Input
                        id="locationRegion"
                        value={formData.locationRegion}
                        onChange={(e) => handleInputChange('locationRegion', e.target.value)}
                        placeholder="Ex: Casablanca-Settat"
                      />
                    </div>
                  </div>
                </div>

                {/* Additional Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Informations supplémentaires</h3>
                  
                  <div className="space-y-2">
                    <Label htmlFor="requirements">Exigences spécifiques</Label>
                    <Textarea
                      id="requirements"
                      value={formData.requirements}
                      onChange={(e) => handleInputChange('requirements', e.target.value)}
                      placeholder="Ex: Certification requise, expérience minimum, matériel à fournir..."
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="images">URLs des images (séparées par des virgules)</Label>
                    <Input
                      id="images"
                      value={formData.images}
                      onChange={(e) => handleInputChange('images', e.target.value)}
                      placeholder="https://example.com/image1.jpg,https://example.com/image2.jpg"
                    />
                    <p className="text-xs text-muted-foreground">
                      Ajoutez des photos pour illustrer votre demande
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="attachments">Fichiers joints (URLs séparées par des virgules)</Label>
                    <Input
                      id="attachments"
                      value={formData.attachments}
                      onChange={(e) => handleInputChange('attachments', e.target.value)}
                      placeholder="https://example.com/document.pdf"
                    />
                    <p className="text-xs text-muted-foreground">
                      Plans, devis, documents techniques...
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isPublic"
                      checked={formData.isPublic}
                      onCheckedChange={(checked) => handleInputChange('isPublic', checked as boolean)}
                    />
                    <Label htmlFor="isPublic">Demande publique</Label>
                    <p className="text-xs text-muted-foreground ml-2">
                      (Visible par tous les prestataires)
                    </p>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="flex justify-end space-x-4 pt-6 border-t">
                  <Button type="button" variant="outline" asChild>
                    <Link href="/requests">Annuler</Link>
                  </Button>
                  <Button type="submit" disabled={loading}>
                    {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Publier la demande
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </main>

      <Footer />
    </div>
  )
}
