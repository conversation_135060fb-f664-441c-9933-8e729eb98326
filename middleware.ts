import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Routes qui nécessitent une authentification
const protectedRoutes = [
  '/dashboard',
  '/api/portfolio',
  '/api/availability',
  '/api/bookings',
  '/api/notifications',
]

// Routes d'authentification (redirection si déjà connecté)
const authRoutes = [
  '/auth/signin',
  '/auth/signup',
]

export default function middleware(req: NextRequest) {
  try {
    const { pathname } = req.nextUrl
    
    // Skip middleware for API routes, static files, and auth callbacks
    if (pathname.startsWith('/api/auth') || 
        pathname.startsWith('/_next') || 
        pathname.startsWith('/favicon.ico') ||
        pathname.startsWith('/public')) {
      return NextResponse.next()
    }
    
    // Vérifier si la route nécessite une authentification
    const isProtectedRoute = protectedRoutes.some(route => 
      pathname.startsWith(route)
    )
    
    // Pour les routes protégées, rediriger vers signin (sans vérifier l'auth pour éviter les erreurs)
    if (isProtectedRoute) {
      // Vérifier s'il y a un cookie de session NextAuth
      const sessionToken = req.cookies.get('next-auth.session-token') || req.cookies.get('__Secure-next-auth.session-token')
      
      if (!sessionToken) {
        const signInUrl = new URL('/auth/signin', req.url)
        signInUrl.searchParams.set('callbackUrl', pathname)
        return NextResponse.redirect(signInUrl)
      }
    }
    
    return NextResponse.next()
  } catch (error) {
    console.error('Middleware error:', error)
    // En cas d'erreur, laisser passer la requête
    return NextResponse.next()
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)',
  ],
}
