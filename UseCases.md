# QribLik - Cas d'Usage Détaillés 

*Plateforme marketplace de services entre particuliers pour le Maroc*

---

## **1. Utilisateur (Client)**

### Interface & Expérience Utilisateur
- **US-C001** : En tant qu'utilisateur, je veux avoir une application simple, claire et attractive (design sexy, couleurs modernes) afin de prendre plaisir à l'utiliser.
  - *Critères d'acceptation* : Interface moderne avec couleurs du Maroc, animations fluides, navigation intuitive
  - *Priorité* : Haute
  - *Status* : À améliorer (base existante)

### Recherche & Navigation
- **US-C002** : En tant qu'utilisateur, je veux trouver rapidement un artisan par catégorie (bricolage, femme de ménage, maçonnerie, transport, déménagement, etc.) afin de gagner du temps.
  - *Critères d'acceptation* : Catégories claires, recherche par mots-clés, filtres avancés
  - *Priorité* : Haute
  - *Status* : Implémenté

- **US-C003** : En tant qu'utilisateur, je veux accéder à une couverture nationale (partout au Maroc) afin de trouver un service où que je sois.
  - *Critères d'acceptation* : Géolocalisation, recherche par ville/région, couverture toutes les villes du Maroc
  - *Priorité* : Haute
  - *Status* : Partiellement implémenté

- **US-C004** : En tant qu'utilisateur, je veux naviguer facilement grâce à un moteur de recherche efficace et des filtres afin de trouver exactement ce dont j'ai besoin.
  - *Critères d'acceptation* : Recherche intelligente, filtres par prix/distance/note, tri des résultats
  - *Priorité* : Haute
  - *Status* : À améliorer

### Nouvelles Fonctionnalités Clients
- **US-C005** : En tant qu'utilisateur, je veux voir les avis et portfolios des artisans pour choisir le meilleur prestataire.
- **US-C006** : En tant qu'utilisateur, je veux recevoir des notifications en temps réel sur l'avancement de ma demande.
- **US-C007** : En tant qu'utilisateur, je veux pouvoir comparer facilement plusieurs devis.

---

## **2. Artisan (Prestataire)**

### Gestion des Missions
- **US-A001** : En tant qu'artisan, je veux recevoir facilement des missions et pouvoir accepter ou rejeter en un clic, afin de gérer mon temps.
  - *Critères d'acceptation* : Notifications push, interface simple accept/reject, détails de la mission
  - *Priorité* : Haute
  - *Status* : Implémenté

- **US-A002** : En tant qu'artisan, je veux avoir la possibilité de prendre une deuxième tâche si je termine rapidement la première, afin d'augmenter mes revenus.
  - *Critères d'acceptation* : Système de missions multiples, gestion des créneaux, optimisation des trajets
  - *Priorité* : Moyenne
  - *Status* : À implémenter

### Planification & Organisation
- **US-A003** : En tant qu'artisan, je veux planifier mon agenda directement dans l'application afin d'organiser mes interventions.
  - *Critères d'acceptation* : Calendrier intégré, gestion des créneaux, synchronisation avec missions
  - *Priorité* : Haute
  - *Status* : À implémenter

### Portfolio & Visibilité
- **US-A004** : En tant qu'artisan, je veux publier des photos et vidéos de mes réalisations afin de valoriser mon savoir-faire.
  - *Critères d'acceptation* : Upload multiple, galerie organisée, avant/après, descriptions
  - *Priorité* : Haute
  - *Status* : À implémenter

- **US-A005** : En tant qu'artisan, je veux que mes photos/vidéos puissent être partagées facilement sur TikTok, Facebook ou Instagram, afin d'avoir plus de visibilité.
  - *Critères d'acceptation* : Boutons de partage intégrés, formats optimisés, watermark QribLik
  - *Priorité* : Moyenne
  - *Status* : À implémenter

### Monétisation & Premium
- **US-A006** : En tant qu'artisan, je veux que l'application m'encourage à utiliser le paiement intégré en me donnant plus de visibilité et des opportunités premium, afin de développer mon activité.
  - *Critères d'acceptation* : Badge premium, meilleur référencement, accès prioritaire aux missions
  - *Priorité* : Haute
  - *Status* : Partiellement implémenté

- **US-A007** : En tant qu'artisan, je veux avoir le choix entre un abonnement ou le paiement intégré pour accéder à plus de services afin d'adapter l'application à mes besoins.
  - *Critères d'acceptation* : Plans d'abonnement flexibles, comparaison des options, essai gratuit
  - *Priorité* : Moyenne
  - *Status* : À implémenter

### Géolocalisation & Optimisation
- **US-A008** : En tant qu'artisan, je veux voir une carte avec les zones où il y a beaucoup de demandes afin de cibler mes déplacements.
  - *Critères d'acceptation* : Heatmap des demandes, statistiques par zone, suggestions d'optimisation
  - *Priorité* : Moyenne
  - *Status* : À implémenter

### Nouvelles Fonctionnalités Artisans
- **US-A009** : En tant qu'artisan, je veux recevoir des recommandations de missions basées sur mon profil et ma localisation.
- **US-A010** : En tant qu'artisan, je veux pouvoir créer des packages de services pour augmenter mon chiffre d'affaires.
- **US-A011** : En tant qu'artisan, je veux accéder à des formations et certifications pour améliorer mes compétences.

---

## **3. Administrateur**

### Gestion Existante (Déjà Implémentée)
- **US-AD001** : Validation des artisans et vérification des profils
- **US-AD002** : Gestion des signalements et résolution des conflits
- **US-AD003** : Gestion des abonnements et paiements
- **US-AD004** : Statistiques d'utilisation et analytics
- **US-AD005** : Notifications globales et communication

### Nouvelles Fonctionnalités Admin
- **US-AD006** : En tant qu'admin, je veux pouvoir modérer le contenu des portfolios automatiquement.
- **US-AD007** : En tant qu'admin, je veux analyser les tendances de demandes par région pour optimiser la plateforme.
- **US-AD008** : En tant qu'admin, je veux gérer les campagnes de promotion et les offres spéciales.

---

## **4. PDG / Fondateur**

### Stratégie & Croissance
- **US-PDG001** : En tant que PDG, je veux que l'application soit attractive pour les utilisateurs (UX/UI moderne et sexy) afin de maximiser l'adoption.
  - *Critères d'acceptation* : Design moderne, UX fluide, taux de conversion élevé
  - *Priorité* : Haute
  - *Status* : À améliorer

- **US-PDG002** : En tant que PDG, je veux que les artisans qui paient ou passent par l'application bénéficient de plus de visibilité afin de créer une incitation forte à utiliser notre système.
  - *Critères d'acceptation* : Algorithme de visibilité, badges premium, statistiques de performance
  - *Priorité* : Haute
  - *Status* : Partiellement implémenté

### Marketing & Viralité
- **US-PDG003** : En tant que PDG, je veux pouvoir générer de la publicité gratuite pour la plateforme grâce aux partages des artisans sur les réseaux sociaux afin d'augmenter la notoriété sans gros budget marketing.
  - *Critères d'acceptation* : Partages automatiques, contenu viral, tracking des conversions
  - *Priorité* : Moyenne
  - *Status* : À implémenter

### Business Model
- **US-PDG004** : En tant que PDG, je veux tester différents modèles de revenus (paiement intégré, abonnements, premium) afin d'assurer la rentabilité.
  - *Critères d'acceptation* : A/B testing, analytics financiers, tableaux de bord KPI
  - *Priorité* : Haute
  - *Status* : Partiellement implémenté

### Nouvelles Fonctionnalités Business
- **US-PDG005** : En tant que PDG, je veux analyser le comportement des utilisateurs pour optimiser la conversion.
- **US-PDG006** : En tant que PDG, je veux pouvoir lancer des campagnes marketing ciblées par région/catégorie.
- **US-PDG007** : En tant que PDG, je veux mesurer la satisfaction client et le NPS de la plateforme.

---

## **Priorisation des Développements**

### **Phase 1 - Priorité Haute (1-2 mois)**
1. **Portfolio Artisans** (US-A004) - Galerie photos/vidéos
2. **Agenda Intégré** (US-A003) - Planification des interventions
3. **Design Moderne** (US-C001, US-PDG001) - Refonte UI/UX
4. **Géolocalisation Avancée** (US-C003) - Couverture nationale

### **Phase 2 - Priorité Moyenne (2-4 mois)**
1. **Partage Réseaux Sociaux** (US-A005) - Viralité
2. **Carte des Demandes** (US-A008) - Heatmap
3. **Missions Multiples** (US-A002) - Optimisation revenus
4. **Système Premium** (US-A006, US-A007) - Monétisation

### **Phase 3 - Fonctionnalités Avancées (4-6 mois)**
1. **Analytics Avancées** (US-PDG004, US-PDG005)
2. **IA & Recommandations** (US-A009)
3. **Formations & Certifications** (US-A011)
4. **Marketing Automation** (US-PDG006)

---

## **Métriques de Succès**

### KPIs Utilisateurs
- **Taux d'adoption** : +50% nouveaux utilisateurs/mois
- **Engagement** : 3+ sessions/semaine par utilisateur actif
- **Conversion** : 15% des visiteurs créent un compte

### KPIs Artisans
- **Activation** : 80% des artisans inscrits complètent leur profil
- **Rétention** : 70% des artisans actifs après 30 jours
- **Revenus** : Augmentation de 25% des revenus artisans via premium

### KPIs Business
- **GMV** : 100k MAD de transactions/mois
- **Commission** : 5% de commission moyenne
- **Croissance** : 20% de croissance mensuelle

---

*Dernière mise à jour : Septembre 2024*
*Version : 2.0 - Détaillée et Priorisée*