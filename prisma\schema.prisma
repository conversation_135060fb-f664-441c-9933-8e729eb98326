// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                String    @id @default(cuid())
  name              String?
  email             String    @unique
  emailVerified     DateTime?
  image             String?
  password          String?   // For credentials login
  phone             String?
  bio               String?
  userType          String    @default("CLIENT") // CLIENT, PRESTATAIRE, BOTH
  isVerified        Boolean   @default(false)
  serviceRadius     Int       @default(10) // km
  rating            Float?
  reviewCount       Int       @default(0)
  responseTime      Int?      // minutes
  completionRate    Float     @default(0)
  subscriptionStatus String   @default("FREE") // FREE, ACTIVE, PAST_DUE, CANCELLED, EXPIRED
  isActive          Boolean   @default(true)
  lastSeen          DateTime  @default(now())
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Stripe payment fields
  stripeAccountId     String?
  stripeAccountStatus String?   @default("PENDING") // PENDING, ACTIVE, RESTRICTED
  canReceivePayments  Boolean   @default(false)

  // Relations
  accounts          Account[]
  sessions          Session[]
  services          Service[]
  requests          Request[]
  matchesAsClient   Match[]   @relation("ClientMatches")
  matchesAsProvider Match[]   @relation("ProviderMatches")
  reviewsGiven      Review[]  @relation("ReviewsGiven")
  reviewsReceived   Review[]  @relation("ReviewsReceived")
  conversations     ConversationParticipant[]
  messages          Message[]
  notifications     Notification[]
  paymentsAsSender  Payment[] @relation("PaymentsSent")
  paymentsAsReceiver Payment[] @relation("PaymentsReceived")
  portfolioItems    PortfolioItem[]
  availabilitySlots AvailabilitySlot[]

  // Location
  locationLat       Float?
  locationLng       Float?
  locationAddress   String?
  locationCity      String?
  locationRegion    String?

  @@map("users")
}

model Service {
  id            String        @id @default(cuid())
  title         String
  description   String
  shortDesc     String?
  category      String        // BRICOLAGE_REPARATIONS, MENAGE_NETTOYAGE, etc.
  subCategory   String?
  tags          String        // Comma-separated tags
  price         Float?
  priceType     String        // FIXED, HOURLY, DAILY, QUOTE
  images        String        // Comma-separated image URLs
  video         String?
  isRemote      Boolean       @default(false)
  availability  String?       // JSON string
  status        String        @default("PENDING_APPROVAL") // ACTIVE, PAUSED, INACTIVE, PENDING_APPROVAL
  isPromoted    Boolean       @default(false)
  promoteEnds   DateTime?
  viewCount     Int           @default(0)
  contactCount  Int           @default(0)
  requirements  String?
  duration      String?
  deliverables  String        // Comma-separated deliverables
  userId        String
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  matches       Match[]

  // Location
  locationLat   Float?
  locationLng   Float?
  locationAddress String?
  locationCity  String?
  locationRegion String?

  @@map("services")
}

model Request {
  id              String        @id @default(cuid())
  title           String
  description     String
  category        String        // BRICOLAGE_REPARATIONS, MENAGE_NETTOYAGE, etc.
  subCategory     String?
  tags            String        // Comma-separated tags
  budget          Float?
  budgetType      String        // FIXED, RANGE, HOURLY
  urgency         String        @default("NORMAL") // LOW, NORMAL, HIGH, EMERGENCY
  preferredTiming String?       // JSON string
  deadline        DateTime?
  images          String        // Comma-separated image URLs
  attachments     String        // Comma-separated attachment URLs
  status          String        @default("OPEN") // OPEN, MATCHED, IN_PROGRESS, COMPLETED, CANCELLED, EXPIRED
  isPublic        Boolean       @default(true)
  expiresAt       DateTime?
  viewCount       Int           @default(0)
  proposalCount   Int           @default(0)
  clientId        String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  client          User          @relation(fields: [clientId], references: [id], onDelete: Cascade)
  matches         Match[]
  conversations   Conversation[]

  // Location
  locationLat     Float?
  locationLng     Float?
  locationAddress String?
  locationCity    String?
  locationRegion  String?

  @@map("requests")
}

model Match {
  id              String      @id @default(cuid())
  requestId       String
  serviceId       String?
  prestataireId   String
  clientId        String
  message         String?
  proposedPrice   Float?
  proposedDelay   String?
  conditions      String?
  status          String      @default("PENDING") // PENDING, ACCEPTED, REJECTED, IN_PROGRESS, COMPLETED, CANCELLED
  clientResponse  String?
  proposedAt      DateTime    @default(now())
  respondedAt     DateTime?
  acceptedAt      DateTime?
  completedAt     DateTime?
  autoGenerated   Boolean     @default(false)
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  request         Request     @relation(fields: [requestId], references: [id], onDelete: Cascade)
  service         Service?    @relation(fields: [serviceId], references: [id], onDelete: SetNull)
  prestataire     User        @relation("ProviderMatches", fields: [prestataireId], references: [id], onDelete: Cascade)
  client          User        @relation("ClientMatches", fields: [clientId], references: [id], onDelete: Cascade)
  conversations   Conversation[]
  reviews         Review[]
  payments        Payment[]
  booking         Booking?

  @@map("matches")
}

model Conversation {
  id            String   @id @default(cuid())
  matchId       String?
  requestId     String?
  lastMessage   String?
  lastMessageAt DateTime?
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  match         Match?   @relation(fields: [matchId], references: [id], onDelete: SetNull)
  request       Request? @relation(fields: [requestId], references: [id], onDelete: SetNull)
  participants  ConversationParticipant[]
  messages      Message[]

  @@map("conversations")
}

model ConversationParticipant {
  id             String       @id @default(cuid())
  conversationId String
  userId         String
  joinedAt       DateTime     @default(now())
  lastReadAt     DateTime?

  // Relations
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([conversationId, userId])
  @@map("conversation_participants")
}

model Message {
  id             String      @id @default(cuid())
  content        String
  messageType    String      @default("TEXT") // TEXT, IMAGE, FILE, LOCATION, SYSTEM
  attachments    String      // Comma-separated attachment URLs
  metadata       String?     // JSON string
  conversationId String
  senderId       String
  readBy         String      // Comma-separated user IDs
  readAt         DateTime?
  isEdited       Boolean     @default(false)
  isDeleted      Boolean     @default(false)
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  sender         User         @relation(fields: [senderId], references: [id], onDelete: Cascade)

  @@map("messages")
}

model Review {
  id                  String   @id @default(cuid())
  matchId             String
  reviewerId          String
  revieweeId          String
  rating              Int      // 1-5
  comment             String?
  qualityRating       Int?
  communicationRating Int?
  timelinessRating    Int?
  valueRating         Int?
  images              String   // Comma-separated image URLs
  isPublic            Boolean  @default(true)
  isVerified          Boolean  @default(false)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations
  match               Match    @relation(fields: [matchId], references: [id], onDelete: Cascade)
  reviewer            User     @relation("ReviewsGiven", fields: [reviewerId], references: [id], onDelete: Cascade)
  reviewee            User     @relation("ReviewsReceived", fields: [revieweeId], references: [id], onDelete: Cascade)

  @@map("reviews")
}

model Notification {
  id             String           @id @default(cuid())
  title          String
  message        String
  notificationType String         // NEW_MATCH, MESSAGE_RECEIVED, etc.
  userId         String
  relatedId      String?
  relatedType    String?
  actionUrl      String?
  isRead         Boolean          @default(false)
  isEmailSent    Boolean          @default(false)
  isPushSent     Boolean          @default(false)
  createdAt      DateTime         @default(now())

  // Relations
  user           User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model Payment {
  id                    String   @id @default(cuid())
  matchId               String
  clientId              String
  prestataireId         String
  amount                Float
  platformFee           Float
  currency              String   @default("mad")
  status                String   @default("PENDING") // PENDING, COMPLETED, FAILED, CANCELLED
  paymentMethod         String   @default("STRIPE") // STRIPE, CMI, BANK_TRANSFER, CASH
  stripePaymentIntentId String?
  stripeChargeId        String?
  description           String?
  failureReason         String?
  createdAt             DateTime @default(now())
  completedAt           DateTime?

  // Relations
  match                 Match    @relation(fields: [matchId], references: [id], onDelete: Cascade)
  client                User     @relation("PaymentsSent", fields: [clientId], references: [id], onDelete: Cascade)
  prestataire           User     @relation("PaymentsReceived", fields: [prestataireId], references: [id], onDelete: Cascade)

  @@map("payments")
}

model PortfolioItem {
  id              String   @id @default(cuid())
  title           String
  description     String?
  category        String   // Same categories as Service
  images          String   // Comma-separated image URLs
  videos          String?  // Comma-separated video URLs
  beforeImages    String?  // Before photos for before/after
  afterImages     String?  // After photos for before/after
  projectDate     DateTime?
  duration        String?  // How long the project took
  clientTestimonial String? // Optional client feedback
  tags            String   // Comma-separated tags
  isPublic        Boolean  @default(true)
  isFeatured      Boolean  @default(false)
  viewCount       Int      @default(0)
  likeCount       Int      @default(0)
  shareCount      Int      @default(0)
  userId          String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Location where work was done
  locationLat     Float?
  locationLng     Float?
  locationAddress String?
  locationCity    String?
  locationRegion  String?

  @@map("portfolio_items")
}

model AvailabilitySlot {
  id            String   @id @default(cuid())
  userId        String
  dayOfWeek     Int      // 0-6 (Sunday to Saturday)
  startTime     String   // HH:MM format
  endTime       String   // HH:MM format
  isAvailable   Boolean  @default(true)
  slotType      String   @default("WORK") // WORK, BREAK, UNAVAILABLE
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("availability_slots")
}

model Booking {
  id              String   @id @default(cuid())
  matchId         String   @unique
  scheduledDate   DateTime
  scheduledTime   String   // HH:MM format
  duration        Int      // Duration in minutes
  status          String   @default("SCHEDULED") // SCHEDULED, CONFIRMED, IN_PROGRESS, COMPLETED, CANCELLED
  notes           String?
  reminderSent    Boolean  @default(false)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  match           Match    @relation(fields: [matchId], references: [id], onDelete: Cascade)

  @@map("bookings")
}
