'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Search, ArrowRight, Star, Users, CheckCircle } from 'lucide-react'
import { motion } from 'framer-motion'

export function HeroSection() {
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <section className="relative bg-gradient-to-br from-primary/5 via-background to-secondary/5 py-20 lg:py-32 overflow-hidden">
      {/* Background Decorations */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-20 h-20 bg-accent/20 rounded-full blur-xl animate-bounce-soft" />
        <div className="absolute bottom-20 right-10 w-32 h-32 bg-secondary/20 rounded-full blur-xl animate-bounce-soft" style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-1/2 w-16 h-16 bg-primary/20 rounded-full blur-xl animate-bounce-soft" style={{ animationDelay: '2s' }} />
      </div>

      <div className="container relative">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div 
            className="space-y-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="space-y-4">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2, duration: 0.5 }}
              >
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/10 text-primary">
                  🇲🇦 Numéro 1 au Maroc
                </span>
              </motion.div>

              <h1 className="text-4xl md:text-6xl font-bold leading-tight">
                Trouvez le service
                <br />
                <span className="gradient-text">parfait près de chez vous</span>
              </h1>

              <p className="text-xl text-muted-foreground max-w-lg leading-relaxed">
                Bricolage, ménage, cours particuliers, jardinage... 
                Plus de 100 000 prestataires de confiance dans tout le Maroc.
              </p>
            </div>

            {/* Search Bar */}
            <motion.div 
              className="flex flex-col sm:flex-row gap-4 max-w-lg"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              <div className="relative flex-1">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
                <input
                  type="text"
                  placeholder="Quel service recherchez-vous ?"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 text-lg border border-input rounded-xl focus:outline-none focus:ring-2 focus:ring-primary shadow-lg"
                />
              </div>
              <Button size="lg" className="px-8 py-4 text-lg rounded-xl shadow-lg">
                Rechercher
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </motion.div>

            {/* Popular Searches */}
            <motion.div 
              className="flex flex-wrap gap-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
            >
              <span className="text-sm text-muted-foreground">Populaire :</span>
              {['Ménage', 'Bricolage', 'Cours d\'anglais', 'Jardinage', 'Réparation'].map((term) => (
                <button
                  key={term}
                  className="px-3 py-1 text-sm bg-muted hover:bg-muted/80 rounded-full transition-colors"
                >
                  {term}
                </button>
              ))}
            </motion.div>

            {/* Stats */}
            <motion.div 
              className="flex flex-wrap items-center gap-8 pt-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.5 }}
            >
              <div className="flex items-center space-x-2">
                <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                <span className="font-semibold">4.8/5</span>
                <span className="text-muted-foreground">sur 12k avis</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-primary" />
                <span className="font-semibold">100k+</span>
                <span className="text-muted-foreground">prestataires</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-secondary" />
                <span className="font-semibold">500k+</span>
                <span className="text-muted-foreground">services réalisés</span>
              </div>
            </motion.div>
          </motion.div>

          {/* Hero Image/Graphics */}
          <motion.div 
            className="relative"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3, duration: 0.7 }}
          >
            <div className="relative">
              {/* Main Hero Image */}
              <div className="aspect-square bg-gradient-to-br from-primary/10 to-secondary/10 rounded-3xl p-8 relative overflow-hidden">
                <img
                  src="https://images.pexels.com/photos/4492129/pexels-photo-4492129.jpeg"
                  alt="Services de proximité"
                  className="w-full h-full object-cover rounded-2xl"
                />
                
                {/* Floating Cards */}
                <div className="absolute -top-4 -right-4 bg-white p-4 rounded-xl shadow-lg animate-bounce-soft">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-secondary rounded-full flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                    <span className="font-medium text-sm">Service terminé</span>
                  </div>
                </div>
                
                <div className="absolute -bottom-4 -left-4 bg-white p-4 rounded-xl shadow-lg animate-bounce-soft" style={{ animationDelay: '1s' }}>
                  <div className="flex items-center space-x-2">
                    <div className="flex -space-x-1">
                      <div className="w-6 h-6 bg-primary rounded-full border-2 border-white" />
                      <div className="w-6 h-6 bg-accent rounded-full border-2 border-white" />
                      <div className="w-6 h-6 bg-secondary rounded-full border-2 border-white" />
                    </div>
                    <span className="font-medium text-sm">+50 près de vous</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}