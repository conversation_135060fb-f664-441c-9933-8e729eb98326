import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding nouvelles fonctionnalités...');

  // Récupérer des utilisateurs existants de type PRESTATAIRE
  const prestataires = await prisma.user.findMany({
    where: {
      OR: [
        { userType: 'PRESTATAIRE' },
        { userType: 'BOTH' }
      ]
    },
    take: 3
  });

  if (prestataires.length === 0) {
    console.log('❌ Aucun prestataire trouvé. Créez d\'abord des utilisateurs prestataires.');
    return;
  }

  console.log(`✅ Trouvé ${prestataires.length} prestataires`);

  // 1. Créer des éléments de portfolio
  console.log('📸 Création des portfolios...');
  
  const portfolioData = [
    {
      title: 'Rénovation Cuisine Moderne',
      description: 'Transformation complète d\'une cuisine traditionnelle en espace moderne avec îlot central.',
      category: 'BRICOLAGE_REPARATIONS',
      images: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136,https://images.unsplash.com/photo-1556909114-f6e7ad7d3136',
      beforeImages: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136',
      afterImages: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136',
      projectDate: new Date('2024-08-15'),
      duration: '2 semaines',
      clientTestimonial: 'Travail exceptionnel ! Ma cuisine est maintenant le cœur de ma maison.',
      tags: 'rénovation,cuisine,moderne,îlot',
      isPublic: true,
      isFeatured: true,
      locationCity: 'Casablanca',
      locationRegion: 'Casablanca-Settat',
      locationAddress: 'Quartier Maarif',
      viewCount: 45,
      likeCount: 12,
      shareCount: 3,
    },
    {
      title: 'Nettoyage Complet Villa',
      description: 'Service de nettoyage approfondi d\'une villa de 300m² avec jardins.',
      category: 'MENAGE_NETTOYAGE',
      images: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64,https://images.unsplash.com/photo-1558618666-fcd25c85cd64',
      projectDate: new Date('2024-09-01'),
      duration: '1 jour',
      clientTestimonial: 'Service impeccable, très professionnel et ponctuel.',
      tags: 'nettoyage,villa,professionnel',
      isPublic: true,
      locationCity: 'Rabat',
      locationRegion: 'Rabat-Salé-Kénitra',
      viewCount: 23,
      likeCount: 8,
      shareCount: 1,
    },
    {
      title: 'Aménagement Jardin Zen',
      description: 'Création d\'un espace zen avec fontaine, bambous et chemin de pierres.',
      category: 'JARDINAGE_ESPACES_VERTS',
      images: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b,https://images.unsplash.com/photo-1416879595882-3373a0480b5b',
      beforeImages: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b',
      afterImages: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b',
      projectDate: new Date('2024-07-20'),
      duration: '1 semaine',
      clientTestimonial: 'Mon jardin est devenu un véritable havre de paix.',
      tags: 'jardin,zen,aménagement,paysage',
      isPublic: true,
      locationCity: 'Marrakech',
      locationRegion: 'Marrakech-Safi',
      viewCount: 67,
      likeCount: 18,
      shareCount: 5,
    }
  ];

  for (let i = 0; i < portfolioData.length && i < prestataires.length; i++) {
    await prisma.portfolioItem.create({
      data: {
        ...portfolioData[i],
        userId: prestataires[i].id,
      }
    });
  }

  // 2. Créer des créneaux de disponibilité
  console.log('📅 Création des créneaux de disponibilité...');
  
  const availabilitySlots = [
    // Lundi
    { dayOfWeek: 1, startTime: '08:00', endTime: '12:00', slotType: 'WORK' },
    { dayOfWeek: 1, startTime: '14:00', endTime: '18:00', slotType: 'WORK' },
    // Mardi
    { dayOfWeek: 2, startTime: '08:00', endTime: '12:00', slotType: 'WORK' },
    { dayOfWeek: 2, startTime: '14:00', endTime: '18:00', slotType: 'WORK' },
    // Mercredi
    { dayOfWeek: 3, startTime: '08:00', endTime: '12:00', slotType: 'WORK' },
    { dayOfWeek: 3, startTime: '12:00', endTime: '13:00', slotType: 'BREAK' },
    { dayOfWeek: 3, startTime: '14:00', endTime: '18:00', slotType: 'WORK' },
    // Jeudi
    { dayOfWeek: 4, startTime: '08:00', endTime: '12:00', slotType: 'WORK' },
    { dayOfWeek: 4, startTime: '14:00', endTime: '18:00', slotType: 'WORK' },
    // Vendredi
    { dayOfWeek: 5, startTime: '08:00', endTime: '12:00', slotType: 'WORK' },
    // Samedi
    { dayOfWeek: 6, startTime: '09:00', endTime: '13:00', slotType: 'WORK' },
  ];

  for (const prestataire of prestataires) {
    for (const slot of availabilitySlots) {
      await prisma.availabilitySlot.create({
        data: {
          ...slot,
          userId: prestataire.id,
          isAvailable: true,
        }
      });
    }
  }

  // 3. Créer des notifications de test
  console.log('🔔 Création des notifications...');
  
  const notificationTypes = [
    {
      title: 'Nouveau match trouvé !',
      message: 'Un client recherche vos services de bricolage à Casablanca.',
      notificationType: 'NEW_MATCH',
    },
    {
      title: 'Message reçu',
      message: 'Ahmed vous a envoyé un message concernant votre devis.',
      notificationType: 'MESSAGE_RECEIVED',
    },
    {
      title: 'Réservation confirmée',
      message: 'Votre intervention du 20 septembre à 14h00 est confirmée.',
      notificationType: 'BOOKING_CONFIRMED',
    },
    {
      title: 'Paiement reçu',
      message: 'Vous avez reçu un paiement de 450 MAD pour votre dernière intervention.',
      notificationType: 'PAYMENT_RECEIVED',
    },
    {
      title: 'Nouvel avis',
      message: 'Fatima a laissé un avis 5 étoiles sur votre travail !',
      notificationType: 'REVIEW_RECEIVED',
    }
  ];

  for (const prestataire of prestataires) {
    for (let i = 0; i < 3; i++) {
      const notification = notificationTypes[i % notificationTypes.length];
      await prisma.notification.create({
        data: {
          ...notification,
          userId: prestataire.id,
          isRead: i === 0, // Premier notification lue, autres non lues
        }
      });
    }
  }

  // 4. Créer quelques matches avec bookings
  console.log('📋 Création des matches et bookings...');
  
  const clients = await prisma.user.findMany({
    where: {
      OR: [
        { userType: 'CLIENT' },
        { userType: 'BOTH' }
      ]
    },
    take: 2
  });

  const requests = await prisma.request.findMany({
    take: 2
  });

  if (clients.length > 0 && requests.length > 0 && prestataires.length > 0) {
    // Créer un match
    const match = await prisma.match.create({
      data: {
        requestId: requests[0].id,
        prestataireId: prestataires[0].id,
        clientId: clients[0].id,
        message: 'Je suis disponible pour ce projet et j\'ai de l\'expérience similaire.',
        proposedPrice: 800,
        proposedDelay: '3 jours',
        status: 'ACCEPTED',
        acceptedAt: new Date(),
      }
    });

    // Créer un booking pour ce match
    await prisma.booking.create({
      data: {
        matchId: match.id,
        scheduledDate: new Date('2024-09-25'),
        scheduledTime: '14:00',
        duration: 240, // 4 heures
        status: 'CONFIRMED',
        notes: 'Apporter les outils nécessaires pour la rénovation.',
      }
    });
  }

  console.log('✅ Seeding terminé avec succès !');
  console.log('');
  console.log('📊 Données créées :');
  console.log(`   - ${portfolioData.length} éléments de portfolio`);
  console.log(`   - ${availabilitySlots.length * prestataires.length} créneaux de disponibilité`);
  console.log(`   - ${notificationTypes.length * prestataires.length} notifications`);
  console.log(`   - 1 match avec booking`);
  console.log('');
  console.log('🎯 Prochaines étapes :');
  console.log('   1. Démarrer le serveur : npm run dev');
  console.log('   2. Tester les nouvelles pages :');
  console.log('      - http://localhost:3000/dashboard/portfolio');
  console.log('      - http://localhost:3000/dashboard/agenda');
  console.log('      - http://localhost:3000/dashboard/map');
}

main()
  .catch((e) => {
    console.error('❌ Erreur lors du seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
