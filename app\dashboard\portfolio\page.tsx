'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Heart, 
  Share2, 
  Camera, 
  Video, 
  MapPin,
  Calendar,
  Clock,
  Star,
  Upload,
  ExternalLink
} from 'lucide-react';
import { toast } from 'sonner';
import Image from 'next/image';

interface PortfolioItem {
  id: string;
  title: string;
  description?: string;
  category: string;
  images: string[];
  videos?: string[];
  beforeImages?: string[];
  afterImages?: string[];
  projectDate?: string;
  duration?: string;
  clientTestimonial?: string;
  tags: string[];
  isPublic: boolean;
  isFeatured: boolean;
  viewCount: number;
  likeCount: number;
  shareCount: number;
  locationAddress?: string;
  locationCity?: string;
  locationRegion?: string;
  createdAt: string;
  updatedAt: string;
}

const CATEGORIES = [
  'BRICOLAGE_REPARATIONS',
  'MENAGE_NETTOYAGE',
  'JARDINAGE_ESPACES_VERTS',
  'COURS_FORMATION',
  'TRANSPORT_DEMENAGEMENT',
  'EVENEMENTIEL',
  'BEAUTE_BIEN_ETRE',
  'INFORMATIQUE_NUMERIQUE',
  'AUTRE'
];

const CATEGORY_LABELS: Record<string, string> = {
  'BRICOLAGE_REPARATIONS': 'Bricolage & Réparations',
  'MENAGE_NETTOYAGE': 'Ménage & Nettoyage',
  'JARDINAGE_ESPACES_VERTS': 'Jardinage & Espaces Verts',
  'COURS_FORMATION': 'Cours & Formation',
  'TRANSPORT_DEMENAGEMENT': 'Transport & Déménagement',
  'EVENEMENTIEL': 'Événementiel',
  'BEAUTE_BIEN_ETRE': 'Beauté & Bien-être',
  'INFORMATIQUE_NUMERIQUE': 'Informatique & Numérique',
  'AUTRE': 'Autre'
};

export default function PortfolioPage() {
  const { data: session } = useSession();
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<PortfolioItem | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    images: [] as string[],
    videos: [] as string[],
    beforeImages: [] as string[],
    afterImages: [] as string[],
    projectDate: '',
    duration: '',
    clientTestimonial: '',
    tags: [] as string[],
    isPublic: true,
    isFeatured: false,
    locationAddress: '',
    locationCity: '',
    locationRegion: '',
  });

  useEffect(() => {
    if (session?.user?.id) {
      fetchPortfolioItems();
    }
  }, [session]);

  const fetchPortfolioItems = async () => {
    try {
      const response = await fetch(`/api/portfolio?userId=${session?.user?.id}`);
      const data = await response.json();
      
      if (data.success) {
        setPortfolioItems(data.data);
      } else {
        toast.error('Erreur lors du chargement du portfolio');
      }
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors du chargement du portfolio');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.category || formData.images.length === 0) {
      toast.error('Veuillez remplir tous les champs obligatoires');
      return;
    }

    try {
      const url = editingItem ? `/api/portfolio/${editingItem.id}` : '/api/portfolio';
      const method = editingItem ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success(editingItem ? 'Portfolio mis à jour' : 'Élément ajouté au portfolio');
        setIsCreateDialogOpen(false);
        setEditingItem(null);
        resetForm();
        fetchPortfolioItems();
      } else {
        toast.error(data.error || 'Erreur lors de la sauvegarde');
      }
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors de la sauvegarde');
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) {
      return;
    }

    try {
      const response = await fetch(`/api/portfolio/${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Élément supprimé');
        fetchPortfolioItems();
      } else {
        toast.error(data.error || 'Erreur lors de la suppression');
      }
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors de la suppression');
    }
  };

  const handleShare = async (item: PortfolioItem, platform: string) => {
    try {
      // Incrémenter le compteur de partages
      await fetch(`/api/portfolio/${item.id}/actions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'share' }),
      });

      const baseUrl = window.location.origin;
      const portfolioUrl = `${baseUrl}/portfolio/${item.id}`;
      const text = `Découvrez mon travail : ${item.title}`;
      
      let shareUrl = '';
      
      switch (platform) {
        case 'facebook':
          shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(portfolioUrl)}`;
          break;
        case 'twitter':
          shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(portfolioUrl)}`;
          break;
        case 'whatsapp':
          shareUrl = `https://wa.me/?text=${encodeURIComponent(text + ' ' + portfolioUrl)}`;
          break;
        case 'linkedin':
          shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(portfolioUrl)}`;
          break;
      }
      
      if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
        toast.success('Partage comptabilisé !');
      }
    } catch (error) {
      console.error('Erreur lors du partage:', error);
    }
  };

  const handleLike = async (item: PortfolioItem) => {
    try {
      const response = await fetch(`/api/portfolio/${item.id}/actions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'like' }),
      });

      if (response.ok) {
        toast.success('Like ajouté !');
        fetchPortfolioItems();
      }
    } catch (error) {
      console.error('Erreur lors du like:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      category: '',
      images: [],
      videos: [],
      beforeImages: [],
      afterImages: [],
      projectDate: '',
      duration: '',
      clientTestimonial: '',
      tags: [],
      isPublic: true,
      isFeatured: false,
      locationAddress: '',
      locationCity: '',
      locationRegion: '',
    });
  };

  const startEdit = (item: PortfolioItem) => {
    setEditingItem(item);
    setFormData({
      title: item.title,
      description: item.description || '',
      category: item.category,
      images: item.images,
      videos: item.videos || [],
      beforeImages: item.beforeImages || [],
      afterImages: item.afterImages || [],
      projectDate: item.projectDate ? new Date(item.projectDate).toISOString().split('T')[0] : '',
      duration: item.duration || '',
      clientTestimonial: item.clientTestimonial || '',
      tags: item.tags,
      isPublic: item.isPublic,
      isFeatured: item.isFeatured,
      locationAddress: item.locationAddress || '',
      locationCity: item.locationCity || '',
      locationRegion: item.locationRegion || '',
    });
    setIsCreateDialogOpen(true);
  };

  const filteredItems = selectedCategory === 'all' 
    ? portfolioItems 
    : portfolioItems.filter(item => item.category === selectedCategory);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mon Portfolio</h1>
          <p className="text-gray-600 mt-2">
            Présentez vos réalisations et attirez plus de clients
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => { resetForm(); setEditingItem(null); }}>
              <Plus className="w-4 h-4 mr-2" />
              Ajouter une réalisation
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingItem ? 'Modifier la réalisation' : 'Ajouter une réalisation'}
              </DialogTitle>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Titre *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="Titre de votre réalisation"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="category">Catégorie *</Label>
                  <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner une catégorie" />
                    </SelectTrigger>
                    <SelectContent>
                      {CATEGORIES.map((category) => (
                        <SelectItem key={category} value={category}>
                          {CATEGORY_LABELS[category]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Décrivez votre réalisation..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="projectDate">Date du projet</Label>
                  <Input
                    id="projectDate"
                    type="date"
                    value={formData.projectDate}
                    onChange={(e) => setFormData({ ...formData, projectDate: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="duration">Durée du projet</Label>
                  <Input
                    id="duration"
                    value={formData.duration}
                    onChange={(e) => setFormData({ ...formData, duration: e.target.value })}
                    placeholder="ex: 2 jours, 1 semaine..."
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="clientTestimonial">Témoignage client</Label>
                <Textarea
                  id="clientTestimonial"
                  value={formData.clientTestimonial}
                  onChange={(e) => setFormData({ ...formData, clientTestimonial: e.target.value })}
                  placeholder="Témoignage de satisfaction du client..."
                  rows={2}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="locationCity">Ville</Label>
                  <Input
                    id="locationCity"
                    value={formData.locationCity}
                    onChange={(e) => setFormData({ ...formData, locationCity: e.target.value })}
                    placeholder="Casablanca"
                  />
                </div>
                <div>
                  <Label htmlFor="locationRegion">Région</Label>
                  <Input
                    id="locationRegion"
                    value={formData.locationRegion}
                    onChange={(e) => setFormData({ ...formData, locationRegion: e.target.value })}
                    placeholder="Casablanca-Settat"
                  />
                </div>
                <div>
                  <Label htmlFor="locationAddress">Adresse</Label>
                  <Input
                    id="locationAddress"
                    value={formData.locationAddress}
                    onChange={(e) => setFormData({ ...formData, locationAddress: e.target.value })}
                    placeholder="Adresse complète"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isPublic"
                    checked={formData.isPublic}
                    onCheckedChange={(checked) => setFormData({ ...formData, isPublic: checked })}
                  />
                  <Label htmlFor="isPublic">Public</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isFeatured"
                    checked={formData.isFeatured}
                    onCheckedChange={(checked) => setFormData({ ...formData, isFeatured: checked })}
                  />
                  <Label htmlFor="isFeatured">Mis en avant</Label>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Annuler
                </Button>
                <Button type="submit">
                  {editingItem ? 'Mettre à jour' : 'Ajouter'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filtres */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="mb-8">
        <TabsList className="grid w-full grid-cols-5 lg:w-auto">
          <TabsTrigger value="all">Tout</TabsTrigger>
          <TabsTrigger value="BRICOLAGE_REPARATIONS">Bricolage</TabsTrigger>
          <TabsTrigger value="MENAGE_NETTOYAGE">Ménage</TabsTrigger>
          <TabsTrigger value="JARDINAGE_ESPACES_VERTS">Jardinage</TabsTrigger>
          <TabsTrigger value="AUTRE">Autre</TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Grille du portfolio */}
      {filteredItems.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <Camera className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Aucune réalisation
            </h3>
            <p className="text-gray-600 mb-4">
              Commencez à construire votre portfolio en ajoutant vos premières réalisations.
            </p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Ajouter une réalisation
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item) => (
            <Card key={item.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative">
                {item.images.length > 0 && (
                  <div className="aspect-video relative">
                    <Image
                      src={item.images[0]}
                      alt={item.title}
                      fill
                      className="object-cover"
                    />
                    {item.isFeatured && (
                      <Badge className="absolute top-2 left-2 bg-yellow-500">
                        <Star className="w-3 h-3 mr-1" />
                        Mis en avant
                      </Badge>
                    )}
                  </div>
                )}
                <div className="absolute top-2 right-2 flex space-x-1">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => startEdit(item)}
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleDelete(item.id)}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-lg">{item.title}</h3>
                  <Badge variant="outline">
                    {CATEGORY_LABELS[item.category]}
                  </Badge>
                </div>
                
                {item.description && (
                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                    {item.description}
                  </p>
                )}

                {item.locationCity && (
                  <div className="flex items-center text-sm text-gray-500 mb-3">
                    <MapPin className="w-3 h-3 mr-1" />
                    {item.locationCity}, {item.locationRegion}
                  </div>
                )}

                {item.projectDate && (
                  <div className="flex items-center text-sm text-gray-500 mb-3">
                    <Calendar className="w-3 h-3 mr-1" />
                    {new Date(item.projectDate).toLocaleDateString()}
                    {item.duration && (
                      <>
                        <Clock className="w-3 h-3 ml-2 mr-1" />
                        {item.duration}
                      </>
                    )}
                  </div>
                )}

                <div className="flex justify-between items-center text-sm text-gray-500">
                  <div className="flex space-x-3">
                    <span className="flex items-center">
                      <Eye className="w-3 h-3 mr-1" />
                      {item.viewCount}
                    </span>
                    <button
                      onClick={() => handleLike(item)}
                      className="flex items-center hover:text-red-500 transition-colors"
                    >
                      <Heart className="w-3 h-3 mr-1" />
                      {item.likeCount}
                    </button>
                    <span className="flex items-center">
                      <Share2 className="w-3 h-3 mr-1" />
                      {item.shareCount}
                    </span>
                  </div>
                  
                  <div className="flex space-x-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleShare(item, 'facebook')}
                      title="Partager sur Facebook"
                    >
                      <ExternalLink className="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                {item.clientTestimonial && (
                  <div className="mt-3 p-2 bg-gray-50 rounded text-sm italic">
                    "{item.clientTestimonial}"
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
