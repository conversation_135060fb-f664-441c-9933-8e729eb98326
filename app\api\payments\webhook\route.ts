import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'
import Stripe from 'stripe'

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = headers().get('stripe-signature')!

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
    }

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent)
        break
      
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent)
        break
      
      case 'payment_intent.canceled':
        await handlePaymentCanceled(event.data.object as Stripe.PaymentIntent)
        break
      
      case 'account.updated':
        await handleAccountUpdated(event.data.object as Stripe.Account)
        break
      
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}

async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Mettre à jour le paiement en base
    const payment = await prisma.payment.update({
      where: { stripePaymentIntentId: paymentIntent.id },
      data: {
        status: 'COMPLETED',
        completedAt: new Date(),
        stripeChargeId: paymentIntent.latest_charge as string
      },
      include: {
        match: {
          include: {
            request: true,
            prestataire: true,
            client: true
          }
        }
      }
    })

    // Mettre à jour le statut du match
    await prisma.match.update({
      where: { id: payment.matchId },
      data: { status: 'IN_PROGRESS' }
    })

    // Créer une notification pour le prestataire
    await prisma.notification.create({
      data: {
        title: 'Paiement reçu',
        message: `Vous avez reçu un paiement de ${payment.amount} MAD pour "${payment.match.request.title}"`,
        notificationType: 'PAYMENT_RECEIVED',
        userId: payment.prestataireId,
        relatedId: payment.id,
        relatedType: 'PAYMENT',
        actionUrl: `/dashboard/payments/${payment.id}`
      }
    })

    // Créer une notification pour le client
    await prisma.notification.create({
      data: {
        title: 'Paiement confirmé',
        message: `Votre paiement de ${payment.amount} MAD a été confirmé pour "${payment.match.request.title}"`,
        notificationType: 'PAYMENT_RECEIVED',
        userId: payment.clientId,
        relatedId: payment.id,
        relatedType: 'PAYMENT',
        actionUrl: `/dashboard/payments/${payment.id}`
      }
    })

    console.log('Payment succeeded:', paymentIntent.id)

  } catch (error) {
    console.error('Error handling payment succeeded:', error)
  }
}

async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Mettre à jour le paiement en base
    await prisma.payment.update({
      where: { stripePaymentIntentId: paymentIntent.id },
      data: {
        status: 'FAILED',
        failureReason: paymentIntent.last_payment_error?.message || 'Payment failed'
      }
    })

    console.log('Payment failed:', paymentIntent.id)

  } catch (error) {
    console.error('Error handling payment failed:', error)
  }
}

async function handlePaymentCanceled(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Mettre à jour le paiement en base
    await prisma.payment.update({
      where: { stripePaymentIntentId: paymentIntent.id },
      data: { status: 'CANCELLED' }
    })

    console.log('Payment canceled:', paymentIntent.id)

  } catch (error) {
    console.error('Error handling payment canceled:', error)
  }
}

async function handleAccountUpdated(account: Stripe.Account) {
  try {
    // Mettre à jour les informations du compte connecté
    const user = await prisma.user.findFirst({
      where: { stripeAccountId: account.id }
    })

    if (user) {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          stripeAccountStatus: account.details_submitted ? 'ACTIVE' : 'PENDING',
          canReceivePayments: account.charges_enabled
        }
      })
    }

    console.log('Account updated:', account.id)

  } catch (error) {
    console.error('Error handling account updated:', error)
  }
}
