const fs = require('fs');
const path = require('path');

console.log('🔍 Vérification des dépendances Radix UI...\n');

// Lire le package.json pour voir les dépendances installées
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const installedDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };

// Scanner tous les fichiers UI pour trouver les imports Radix
const uiDir = path.join(__dirname, '..', 'components', 'ui');
const uiFiles = fs.readdirSync(uiDir).filter(file => file.endsWith('.tsx'));

const radixImports = new Set();
const missingDeps = new Set();

console.log('📦 Analyse des composants UI...');

uiFiles.forEach(file => {
  const filePath = path.join(uiDir, file);
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Chercher les imports @radix-ui
  const radixMatches = content.match(/@radix-ui\/[a-z-]+/g);
  if (radixMatches) {
    radixMatches.forEach(match => {
      radixImports.add(match);
      if (!installedDeps[match]) {
        missingDeps.add(match);
      }
    });
    console.log(`  📄 ${file}: ${radixMatches.length} imports Radix`);
  }
});

console.log('\n📋 Résumé des dépendances Radix UI:');
console.log(`  Total trouvés: ${radixImports.size}`);
console.log(`  Installés: ${radixImports.size - missingDeps.size}`);
console.log(`  Manquants: ${missingDeps.size}`);

if (missingDeps.size > 0) {
  console.log('\n❌ Modules Radix UI manquants:');
  missingDeps.forEach(dep => {
    console.log(`  - ${dep}`);
  });
  
  console.log('\n🔧 Commande pour installer les modules manquants:');
  console.log(`npm install ${Array.from(missingDeps).join(' ')}`);
} else {
  console.log('\n✅ Tous les modules Radix UI sont installés !');
}

console.log('\n📦 Modules Radix UI détectés:');
Array.from(radixImports).sort().forEach(dep => {
  const isInstalled = installedDeps[dep];
  console.log(`  ${isInstalled ? '✅' : '❌'} ${dep}${isInstalled ? ` (${installedDeps[dep]})` : ''}`);
});

// Vérifier aussi les autres dépendances critiques
console.log('\n🔍 Vérification des autres dépendances critiques...');
const criticalDeps = [
  'class-variance-authority',
  'clsx',
  'tailwind-merge',
  'lucide-react',
  'sonner',
  'next-themes'
];

const missingCritical = [];
criticalDeps.forEach(dep => {
  if (installedDeps[dep]) {
    console.log(`  ✅ ${dep}`);
  } else {
    console.log(`  ❌ ${dep} - MANQUANT`);
    missingCritical.push(dep);
  }
});

if (missingCritical.length > 0) {
  console.log('\n🔧 Commande pour installer les dépendances critiques manquantes:');
  console.log(`npm install ${missingCritical.join(' ')}`);
}

console.log('\n🎯 Prochaines étapes:');
if (missingDeps.size > 0 || missingCritical.length > 0) {
  console.log('  1. Installer les dépendances manquantes (voir commandes ci-dessus)');
}
console.log('  2. Redémarrer le serveur: npm run dev');
console.log('  3. Tester les pages: /dashboard/portfolio, /dashboard/agenda, /dashboard/map');
