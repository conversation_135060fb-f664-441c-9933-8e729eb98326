'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { 
  Search, 
  MoreHorizontal, 
  Eye, 
  CheckCircle, 
  XCircle,
  Clock,
  Star,
  MapPin,
  Calendar,
  DollarSign
} from 'lucide-react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { formatDistanceToNow } from 'date-fns'
import { fr } from 'date-fns/locale'

interface Service {
  id: string
  title: string
  description: string
  category: string
  price?: number
  priceType: string
  status: 'ACTIVE' | 'PAUSED' | 'INACTIVE' | 'PENDING_APPROVAL'
  isPromoted: boolean
  viewCount: number
  contactCount: number
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string
    email: string
    rating?: number
    reviewCount: number
  }
  locationCity?: string
  locationRegion?: string
}

export default function AdminServicesPage() {
  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')

  useEffect(() => {
    fetchServices()
  }, [])

  const fetchServices = async () => {
    try {
      // Simuler les données pour la démo
      setTimeout(() => {
        setServices([
          {
            id: '1',
            title: 'Plomberie et réparations',
            description: 'Service de plomberie professionnel pour tous vos besoins de réparation et installation.',
            category: 'BRICOLAGE_REPARATIONS',
            price: 150,
            priceType: 'HOURLY',
            status: 'ACTIVE',
            isPromoted: true,
            viewCount: 245,
            contactCount: 23,
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(),
            updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(),
            user: {
              id: '1',
              name: 'Ahmed Benali',
              email: '<EMAIL>',
              rating: 4.8,
              reviewCount: 23
            },
            locationCity: 'Casablanca',
            locationRegion: 'Grand Casablanca'
          },
          {
            id: '2',
            title: 'Ménage et nettoyage',
            description: 'Service de ménage professionnel pour maisons et bureaux.',
            category: 'MENAGE_NETTOYAGE',
            price: 80,
            priceType: 'HOURLY',
            status: 'PENDING_APPROVAL',
            isPromoted: false,
            viewCount: 12,
            contactCount: 2,
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(),
            updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
            user: {
              id: '2',
              name: 'Fatima Zahra',
              email: '<EMAIL>',
              reviewCount: 0
            },
            locationCity: 'Rabat',
            locationRegion: 'Rabat-Salé-Kénitra'
          },
          {
            id: '3',
            title: 'Cours de guitare',
            description: 'Cours particuliers de guitare pour débutants et intermédiaires.',
            category: 'COURS_FORMATION',
            price: 200,
            priceType: 'FIXED',
            status: 'ACTIVE',
            isPromoted: false,
            viewCount: 89,
            contactCount: 15,
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(),
            updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(),
            user: {
              id: '3',
              name: 'Youssef Alami',
              email: '<EMAIL>',
              rating: 4.2,
              reviewCount: 8
            },
            locationCity: 'Marrakech',
            locationRegion: 'Marrakech-Safi'
          },
          {
            id: '4',
            title: 'Jardinage et entretien',
            description: 'Services de jardinage et entretien d\'espaces verts.',
            category: 'JARDINAGE_BRICOLAGE',
            status: 'PAUSED',
            isPromoted: false,
            viewCount: 156,
            contactCount: 8,
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 45).toISOString(),
            updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10).toISOString(),
            user: {
              id: '4',
              name: 'Khadija Mansouri',
              email: '<EMAIL>',
              rating: 4.9,
              reviewCount: 45
            },
            locationCity: 'Fès',
            locationRegion: 'Fès-Meknès'
          }
        ])
        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Erreur lors du chargement des services:', error)
      setLoading(false)
    }
  }

  const filteredServices = services.filter(service => {
    const matchesSearch = service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.user.name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesCategory = filterCategory === 'all' || service.category === filterCategory
    const matchesStatus = filterStatus === 'all' || service.status === filterStatus
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  const getStatusBadge = (status: Service['status']) => {
    const variants = {
      ACTIVE: 'bg-green-100 text-green-800',
      PAUSED: 'bg-yellow-100 text-yellow-800',
      INACTIVE: 'bg-gray-100 text-gray-800',
      PENDING_APPROVAL: 'bg-orange-100 text-orange-800'
    }
    
    const labels = {
      ACTIVE: 'Actif',
      PAUSED: 'En pause',
      INACTIVE: 'Inactif',
      PENDING_APPROVAL: 'En attente'
    }

    return (
      <Badge className={variants[status]}>
        {labels[status]}
      </Badge>
    )
  }

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      'BRICOLAGE_REPARATIONS': 'Bricolage & Réparations',
      'MENAGE_NETTOYAGE': 'Ménage & Nettoyage',
      'COURS_FORMATION': 'Cours & Formation',
      'JARDINAGE_BRICOLAGE': 'Jardinage',
      'TRANSPORT_LIVRAISON': 'Transport & Livraison',
      'INFORMATIQUE_TECH': 'Informatique & Tech',
      'BEAUTE_BIEN_ETRE': 'Beauté & Bien-être',
      'EVENEMENTIEL': 'Événementiel'
    }
    return labels[category] || category
  }

  const handleServiceAction = async (serviceId: string, action: 'approve' | 'reject' | 'pause' | 'activate' | 'view') => {
    console.log(`Action ${action} pour le service ${serviceId}`)
    // Implémenter les actions ici
    if (action === 'approve') {
      setServices(prev => prev.map(s => 
        s.id === serviceId ? { ...s, status: 'ACTIVE' as const } : s
      ))
    } else if (action === 'reject') {
      setServices(prev => prev.map(s => 
        s.id === serviceId ? { ...s, status: 'INACTIVE' as const } : s
      ))
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Gestion des services</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des services</h1>
          <p className="text-gray-600">Modérez et gérez les services publiés sur la plateforme</p>
        </div>
        <Button onClick={fetchServices}>
          Actualiser
        </Button>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{services.length}</p>
              <p className="text-sm text-gray-600">Total services</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {services.filter(s => s.status === 'ACTIVE').length}
              </p>
              <p className="text-sm text-gray-600">Actifs</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">
                {services.filter(s => s.status === 'PENDING_APPROVAL').length}
              </p>
              <p className="text-sm text-gray-600">En attente</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">
                {services.filter(s => s.isPromoted).length}
              </p>
              <p className="text-sm text-gray-600">Promus</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtres et recherche */}
      <Card>
        <CardHeader>
          <CardTitle>Filtres</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher par titre, description ou prestataire..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Catégorie" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les catégories</SelectItem>
                <SelectItem value="BRICOLAGE_REPARATIONS">Bricolage & Réparations</SelectItem>
                <SelectItem value="MENAGE_NETTOYAGE">Ménage & Nettoyage</SelectItem>
                <SelectItem value="COURS_FORMATION">Cours & Formation</SelectItem>
                <SelectItem value="JARDINAGE_BRICOLAGE">Jardinage</SelectItem>
                <SelectItem value="TRANSPORT_LIVRAISON">Transport & Livraison</SelectItem>
                <SelectItem value="INFORMATIQUE_TECH">Informatique & Tech</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les statuts</SelectItem>
                <SelectItem value="ACTIVE">Actifs</SelectItem>
                <SelectItem value="PENDING_APPROVAL">En attente</SelectItem>
                <SelectItem value="PAUSED">En pause</SelectItem>
                <SelectItem value="INACTIVE">Inactifs</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Liste des services */}
      <Card>
        <CardHeader>
          <CardTitle>Services ({filteredServices.length})</CardTitle>
          <CardDescription>
            Liste de tous les services publiés sur la plateforme
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Service</TableHead>
                  <TableHead>Prestataire</TableHead>
                  <TableHead>Catégorie</TableHead>
                  <TableHead>Prix</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Performance</TableHead>
                  <TableHead>Créé</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredServices.map((service) => (
                  <TableRow key={service.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <p className="font-medium">{service.title}</p>
                          {service.isPromoted && (
                            <Badge variant="secondary" className="text-xs">
                              <Star className="h-3 w-3 mr-1" />
                              Promu
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-500 line-clamp-2">
                          {service.description}
                        </p>
                        {service.locationCity && (
                          <div className="flex items-center space-x-1 text-xs text-gray-400">
                            <MapPin className="h-3 w-3" />
                            <span>{service.locationCity}</span>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium text-sm">{service.user.name}</p>
                        <p className="text-xs text-gray-500">{service.user.email}</p>
                        {service.user.rating && (
                          <div className="flex items-center space-x-1 text-xs">
                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                            <span>{service.user.rating.toFixed(1)}</span>
                            <span className="text-gray-400">({service.user.reviewCount})</span>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {getCategoryLabel(service.category)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {service.price ? (
                        <div className="flex items-center space-x-1">
                          <DollarSign className="h-3 w-3 text-gray-400" />
                          <span className="font-medium">{service.price} MAD</span>
                          <span className="text-xs text-gray-500">
                            /{service.priceType === 'HOURLY' ? 'h' : service.priceType === 'DAILY' ? 'j' : 'fixe'}
                          </span>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">Sur devis</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(service.status)}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1 text-xs">
                        <div className="flex items-center space-x-1">
                          <Eye className="h-3 w-3 text-gray-400" />
                          <span>{service.viewCount} vues</span>
                        </div>
                        <div className="text-gray-500">
                          {service.contactCount} contacts
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1 text-sm text-gray-500">
                        <Calendar className="h-3 w-3" />
                        <span>
                          {formatDistanceToNow(new Date(service.createdAt), {
                            addSuffix: true,
                            locale: fr
                          })}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleServiceAction(service.id, 'view')}>
                            <Eye className="mr-2 h-4 w-4" />
                            Voir le détail
                          </DropdownMenuItem>
                          {service.status === 'PENDING_APPROVAL' && (
                            <>
                              <DropdownMenuItem 
                                onClick={() => handleServiceAction(service.id, 'approve')}
                                className="text-green-600"
                              >
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Approuver
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handleServiceAction(service.id, 'reject')}
                                className="text-red-600"
                              >
                                <XCircle className="mr-2 h-4 w-4" />
                                Rejeter
                              </DropdownMenuItem>
                            </>
                          )}
                          {service.status === 'ACTIVE' && (
                            <DropdownMenuItem onClick={() => handleServiceAction(service.id, 'pause')}>
                              <Clock className="mr-2 h-4 w-4" />
                              Mettre en pause
                            </DropdownMenuItem>
                          )}
                          {service.status === 'PAUSED' && (
                            <DropdownMenuItem onClick={() => handleServiceAction(service.id, 'activate')}>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Réactiver
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
