

## 🚀 PROMPT UNIQUE COMPLET

```
Crée une plateforme de services complète type AlloVoisins adaptée au marché marocain avec TOUTES les fonctionnalités :

═══════════════════════════════════════════════════════════════════════════════════════════
🎯 STACK TECHNIQUE COMPLÈTE
═══════════════════════════════════════════════════════════════════════════════════════════

FRONTEND :
- Next.js 14 App Router + TypeScript strict + React 18
- Tailwind CSS + shadcn/ui (thème maroc : bleu/vert/orange)
- React Hook Form + Zod validation
- Framer Motion pour animations
- Lucide React icônes + Heroicons
- React Query pour state management
- Socket.io client pour temps réel

BACKEND :
- Next.js API Routes + middleware auth
- Prisma ORM + PostgreSQL (Supabase)
- NextAuth.js v5 (Google + Email + Phone OTP)
- Socket.io serveur pour messagerie
- Stripe + CMI Maroc pour paiements
- Resend pour emails + Twilio SMS
- UploadThing pour images
- Google Maps API + Places API

DEPLOYMENT :
- Vercel pour app web
- Supabase pour database + storage + realtime
- Pusher ou Socket.io pour messaging
- CDN Cloudinary pour images optimisées


═══════════════════════════════════════════════════════════════════════════════════════════
🎨 INTERFACE UTILISATEUR COMPLÈTE
═══════════════════════════════════════════════════════════════════════════════════════════

STRUCTURE PAGES :

/src/app/page.tsx - Landing page moderne :
- Hero section avec CTA
- Sections : Comment ça marche, Catégories, Témoignages, Stats
- Footer complet avec liens utiles
- Design responsive et optimisé SEO

/src/app/dashboard/ - Interface utilisateur principale :
- layout.tsx : Sidebar navigation + header
- page.tsx : Vue d'ensemble avec KPIs et actions rapides  
- services/ : Gestion services (liste, création, modification)
- requests/ : Gestion demandes (liste, création, suivi)
- messages/ : Interface messagerie temps réel
- profile/ : Profil utilisateur complet avec statistiques
- settings/ : Paramètres et préférences
- favorites/ : Services favoris
- history/ : Historique des transactions

/src/app/services/ - Catalogue public :
- page.tsx : Liste services avec filtres avancés
- [id]/ : Page détail service avec galerie, avis, contact
- [category]/ : Services par catégorie
- search/ : Résultats de recherche

/src/app/requests/ - Demandes publiques :
- page.tsx : Liste demandes ouvertes
- [id]/ : Détail demande avec propositions
- create/ : Formulaire création demande

/src/app/messages/ - Messagerie :
- page.tsx : Liste conversations  
- [id]/ : Interface chat temps réel

/src/app/profile/[id]/ - Profils publics utilisateurs

/src/app/admin/ - Interface administration :
- dashboard/ : Vue d'ensemble plateforme
- users/ : Gestion utilisateurs  
- services/ : Modération services
- reports/ : Traitement signalements
- analytics/ : Statistiques détaillées

COMPOSANTS RÉUTILISABLES (/src/components/) :

Layout Components :
- Header : Navigation principale avec recherche, notifications, menu utilisateur
- Sidebar : Navigation dashboard avec indicateurs
- Footer : Liens, newsletter, réseaux sociaux  
- MobileNav : Navigation mobile optimisée

Service Components :
- ServiceCard : Card service avec image, titre, prix, rating, localisation
- ServiceGrid : Grille responsive de services
- ServiceFilters : Filtres avancés (catégorie, prix, distance, rating)
- ServiceDetail : Vue détaillée service avec galerie
- ServiceForm : Formulaire création/édition service

Request Components :
- RequestCard : Card demande avec budget, urgence, localisation
- RequestGrid : Grille demandes
- RequestFilters : Filtres demandes
- RequestDetail : Vue détaillée demande
- RequestForm : Formulaire création demande

User Components :  
- UserProfile : Profil utilisateur avec avatar, stats, badges
- UserCard : Card utilisateur compacte
- UserStats : Statistiques utilisateur (rating, reviews, etc.)
- UserVerification : Badge et processus de vérification

Messaging Components :
- ConversationList : Liste conversations avec previews
- MessageThread : Interface chat avec messages
- MessageInput : Input message avec emoji, fichiers
- MessageBubble : Bulle message avec timestamp, statut lu

Match Components :
- MatchCard : Proposition avec détails prestataire
- MatchList : Liste des matches/propositions
- MatchActions : Boutons accepter/rejeter/contacter

Review Components :
- ReviewCard : Avis avec rating étoiles, commentaire
- ReviewForm : Formulaire laisser avis  
- ReviewStats : Statistiques avis utilisateur
- ReviewList : Liste avis avec pagination

Search Components :
- SearchBar : Barre recherche avec suggestions
- SearchFilters : Filtres de recherche avancés
- SearchResults : Résultats avec tri et pagination
- LocationPicker : Sélecteur localisation avec carte

Common Components :
- Button : Boutons stylés avec variants
- Input : Inputs avec validation
- Modal : Modales réutilisables  
- Toast : Notifications temporaires
- Loading : États de chargement (spinners, skeletons)
- Pagination : Pagination avec pages
- Rating : Composant étoiles interactif
- ImageUpload : Upload d'images avec preview
- LocationInput : Input localisation avec géosuggestions
- PriceInput : Input prix avec formatage
- DatePicker : Sélecteur de dates
- TagInput : Input tags avec autocomplete

DESIGN SYSTEM :

Couleurs Maroc :
- Primary : #2563eb (bleu royal)
- Secondary : #16a34a (vert atlas) 
- Accent : #f97316 (orange sahara)
- Success : #22c55e
- Warning : #eab308
- Error : #ef4444
- Neutral : Grays 50-900

Typography :
- Font : Inter (system font fallback)
- Headings : font-bold, sizes harmonieux
- Body : text-gray-700 dark:text-gray-300
- Captions : text-sm text-gray-500

Spacing :
- Scale : 4, 8, 12, 16, 20, 24, 32, 40, 48, 56, 64px
- Container max-width : 1280px
- Section padding : py-16 lg:py-24

Breakpoints responsive :
- sm: 640px
- md: 768px  
- lg: 1024px
- xl: 1280px
- 2xl: 1536px
═══════════════════════════════════════════════════════════════════════════════════════════
💬 MESSAGERIE TEMPS RÉEL
═══════════════════════════════════════════════════════════════════════════════════════════

Configuration Socket.io :

/src/lib/socket.ts - Serveur Socket.io
/src/hooks/useSocket.ts - Hook client Socket.io
/src/components/messaging/ - Interface messagerie complète

Fonctionnalités :
- Messages texte, images, fichiers, localisation
- Indicateur "en train d'écrire"
- Notifications push temps réel
- Statuts de lecture
- Historique persistent
- Recherche dans conversations
- Réactions emoji aux messages

═══════════════════════════════════════════════════════════════════════════════════════════
💳 SYSTÈME DE PAIEMENTS
═══════════════════════════════════════════════════════════════════════════════════════════

Stripe pour abonnements internationaux :
/src/lib/stripe.ts - Configuration Stripe
/src/app/api/payments/stripe/ - Webhooks et gestion abonnements

CMI Maroc pour paiements locaux :
/src/lib/cmi.ts - Intégration CMI
/src/app/api/payments/cmi/ - Paiements par carte marocaine

Plans d'abonnement :
- FREE : 4 réponses/mois
- BASIC (100 MAD/mois) : 20 réponses + support prioritaire
- PRO (200 MAD/mois) : Illimité + badge pro + analytics
- PREMIUM (500 MAD/mois) : Tout + mise en avant services

═══════════════════════════════════════════════════════════════════════════════════════════
📱 BASES APPLICATION MOBILE
═══════════════════════════════════════════════════════════════════════════════════════════

Structure React Native avec Expo :

/mobile/
- App.tsx - Point d'entrée
- /screens/ - Écrans principaux  
- /components/ - Composants réutilisables
- /navigation/ - Navigation React Navigation
- /services/ - Services API partagés avec web
- /hooks/ - Hooks personnalisés
- /utils/ - Utilitaires

Fonctionnalités mobiles spécifiques :
- Notifications push natives
- Géolocalisation continue
- Appareil photo intégré
- Partage natif
- Contacts téléphone
- Appels directs


═══════════════════════════════════════════════════════════════════════════════════════════
🎯 FONCTIONNALITÉS SPÉCIFIQUES ALLOVOISINS
═══════════════════════════════════════════════════════════════════════════════════════════

Système de badges et gamification :
- Badges prestataires : Nouveaux, Expérimentés, Super Pro, Local Hero
- Badges clients : Nouveau, Fidèle, Top Reviewer
- Système de niveaux basé sur activité

Programme de parrainage :
- Codes promo pour nouveaux utilisateurs  
- Récompenses pour parrains
- Tracking des conversions

Outils prestataires avancés :
- Calendrier de disponibilités
- Devis automatiques
- Templates de réponses
- Statistiques détaillées (vues, contacts, conversions)
- Gestion portfolio travaux

Fonctionnalités client premium :
- Historique détaillé des demandes
- Favoris et listes personnalisées
- Alertes de nouveaux services
- Support prioritaire

Sécurité et confiance :
- Vérification identité (pièces d'identité)
- Système de signalements robuste  
- Modération automatique contenus
- Assurance responsabilité civile (partenariat)

Analytics et business intelligence :
- Dashboard admin avec métriques temps réel
- Rapports de performance par région/catégorie
- Prédictions demande avec ML basique
- Export de données pour partenaires

Optimisations SEO :
- Pages statiques pour catégories
- Schema.org markup
- Sitemap dynamique
- Rich snippets pour services

Conformité GDPR/données personnelles :
- Consentement cookies
- Export/suppression données utilisateur  
- Politique de confidentialité dynamique
- Audit logs pour compliance

═══════════════════════════════════════════════════════════════════════════════════════════
🚀 DÉPLOIEMENT ET PRODUCTION
═══════════════════════════════════════════════════════════════════════════════════════════

Docker configuration pour développement
CI/CD avec GitHub Actions
Monitoring avec Vercel Analytics + Sentry
Tests automatisés avec Jest + Playwright
Scripts de backup base de données
Configuration multi-environnements (dev/staging/prod)

═══════════════════════════════════════════════════════════════════════════════════════════

GÉNÈRE CETTE PLATEFORME COMPLÈTE AVEC :
✅ Toutes les fonctionnalités d'AlloVoisins adaptées au Maroc
✅ Interface moderne et responsive
✅ Architecture scalable et maintenable  
✅ Sécurité et performance optimales
✅ Code production-ready avec bonnes pratiques
✅ Documentation inline complète

Crée une marketplace de services professionnelle, robuste et prête à lancer au Maroc ! 🇲🇦
```