/**
 * @jest-environment node
 */

import { createMocks } from 'node-mocks-http'
import { GET, POST } from '@/app/api/portfolio/route'
import { portfolioFixtures, userFixtures } from '../../fixtures/portfolio'

// Mock auth
jest.mock('@/lib/auth', () => ({
  auth: jest.fn(),
}))

// Mock prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    portfolioItem: {
      findMany: jest.fn(),
      create: jest.fn(),
      count: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
  },
}))

import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

const mockAuth = auth as jest.MockedFunction<typeof auth>
const mockPrisma = prisma as any

describe('/api/portfolio', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/portfolio', () => {
    it('should return portfolio items for authenticated user', async () => {
      // Mock authenticated session
      mockAuth.mockResolvedValue({
        user: { id: '1', email: '<EMAIL>' },
      } as any)

      // Mock database response
      mockPrisma.portfolioItem.findMany.mockResolvedValue(portfolioFixtures)

      const { req } = createMocks({
        method: 'GET',
        url: '/api/portfolio',
      })

      const response = await GET(req as any)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.portfolioItems).toHaveLength(3)
      expect(data.data.portfolioItems[0].title).toBe('Rénovation Cuisine Moderne')
    })

    it('should filter portfolio items by category', async () => {
      mockAuth.mockResolvedValue({
        user: { id: '1', email: '<EMAIL>' },
      } as any)

      const bricolageItems = portfolioFixtures.filter(
        item => item.category === 'BRICOLAGE_REPARATIONS'
      )
      mockPrisma.portfolioItem.findMany.mockResolvedValue(bricolageItems)

      const { req } = createMocks({
        method: 'GET',
        url: '/api/portfolio?category=BRICOLAGE_REPARATIONS',
        query: { category: 'BRICOLAGE_REPARATIONS' },
      })

      const response = await GET(req as any)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.data.portfolioItems).toHaveLength(1)
      expect(data.data.portfolioItems[0].category).toBe('BRICOLAGE_REPARATIONS')
      
      // Verify prisma was called with correct filter
      expect(mockPrisma.portfolioItem.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            category: 'BRICOLAGE_REPARATIONS',
          }),
        })
      )
    })

    it('should filter by user ID when provided', async () => {
      mockAuth.mockResolvedValue({
        user: { id: '1', email: '<EMAIL>' },
      } as any)

      const userItems = portfolioFixtures.filter(item => item.userId === '1')
      mockPrisma.portfolioItem.findMany.mockResolvedValue(userItems)

      const { req } = createMocks({
        method: 'GET',
        url: '/api/portfolio?userId=1',
        query: { userId: '1' },
      })

      const response = await GET(req as any)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(mockPrisma.portfolioItem.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            userId: '1',
          }),
        })
      )
    })

    it('should return 401 for unauthenticated requests', async () => {
      mockAuth.mockResolvedValue(null)

      const { req } = createMocks({
        method: 'GET',
        url: '/api/portfolio',
      })

      const response = await GET(req as any)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Non authentifié')
    })

    it('should handle database errors gracefully', async () => {
      mockAuth.mockResolvedValue({
        user: { id: '1', email: '<EMAIL>' },
      } as any)

      mockPrisma.portfolioItem.findMany.mockRejectedValue(
        new Error('Database connection failed')
      )

      const { req } = createMocks({
        method: 'GET',
        url: '/api/portfolio',
      })

      const response = await GET(req as any)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Erreur serveur')
    })

    it('should apply pagination correctly', async () => {
      mockAuth.mockResolvedValue({
        user: { id: '1', email: '<EMAIL>' },
      } as any)

      mockPrisma.portfolioItem.findMany.mockResolvedValue(portfolioFixtures.slice(0, 2))

      const { req } = createMocks({
        method: 'GET',
        url: '/api/portfolio?limit=2&offset=0',
        query: { limit: '2', offset: '0' },
      })

      const response = await GET(req as any)

      expect(mockPrisma.portfolioItem.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 2,
          skip: 0,
        })
      )
    })
  })

  describe('POST /api/portfolio', () => {
    const validPortfolioData = {
      title: 'Test Project',
      description: 'Test Description',
      category: 'BRICOLAGE_REPARATIONS',
      images: ['https://example.com/image.jpg'],
      tags: 'test,project',
      isPublic: true,
    }

    it('should create portfolio item for authenticated prestataire', async () => {
      mockAuth.mockResolvedValue({
        user: { id: '1', email: '<EMAIL>' },
      } as any)

      mockPrisma.user.findUnique.mockResolvedValue({
        ...userFixtures.prestataire,
        userType: 'PRESTATAIRE',
      })

      const createdItem = {
        id: '1',
        ...validPortfolioData,
        userId: '1',
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      mockPrisma.portfolioItem.create.mockResolvedValue(createdItem)

      const { req } = createMocks({
        method: 'POST',
        url: '/api/portfolio',
        body: validPortfolioData,
      })

      const response = await POST(req as any)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.title).toBe('Test Project')
      expect(mockPrisma.portfolioItem.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          title: 'Test Project',
          userId: '1',
          images: 'https://example.com/image.jpg',
          tags: 'test,project',
        }),
      })
    })

    it('should return 403 for non-prestataire users', async () => {
      mockAuth.mockResolvedValue({
        user: { id: '1', email: '<EMAIL>' },
      } as any)

      mockPrisma.user.findUnique.mockResolvedValue({
        ...userFixtures.client,
        userType: 'CLIENT',
      })

      const { req } = createMocks({
        method: 'POST',
        url: '/api/portfolio',
        body: validPortfolioData,
      })

      const response = await POST(req as any)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Accès réservé aux prestataires')
    })

    it('should validate required fields', async () => {
      mockAuth.mockResolvedValue({
        user: { id: '1', email: '<EMAIL>' },
      } as any)

      mockPrisma.user.findUnique.mockResolvedValue({
        ...userFixtures.prestataire,
        userType: 'PRESTATAIRE',
      })

      const invalidData = {
        description: 'Test Description',
        // Missing required title, category, images
      }

      const { req } = createMocks({
        method: 'POST',
        url: '/api/portfolio',
        body: invalidData,
      })

      const response = await POST(req as any)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Données invalides')
      expect(data.details).toBeDefined()
    })

    it('should handle array to string conversion for images', async () => {
      mockAuth.mockResolvedValue({
        user: { id: '1', email: '<EMAIL>' },
      } as any)

      mockPrisma.user.findUnique.mockResolvedValue({
        ...userFixtures.prestataire,
        userType: 'PRESTATAIRE',
      })

      const dataWithArrayImages = {
        ...validPortfolioData,
        images: ['image1.jpg', 'image2.jpg', 'image3.jpg'],
        videos: ['video1.mp4'],
        beforeImages: ['before1.jpg'],
        afterImages: ['after1.jpg'],
      }

      mockPrisma.portfolioItem.create.mockResolvedValue({
        id: '1',
        ...dataWithArrayImages,
        userId: '1',
      })

      const { req } = createMocks({
        method: 'POST',
        url: '/api/portfolio',
        body: dataWithArrayImages,
      })

      const response = await POST(req as any)

      expect(mockPrisma.portfolioItem.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          images: 'image1.jpg,image2.jpg,image3.jpg',
          videos: 'video1.mp4',
          beforeImages: 'before1.jpg',
          afterImages: 'after1.jpg',
        }),
      })
    })
  })
})
