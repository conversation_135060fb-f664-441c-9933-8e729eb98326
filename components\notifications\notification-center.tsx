'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Bell, 
  Check, 
  CheckCheck, 
  Clock, 
  User, 
  MessageSquare, 
  Calendar, 
  DollarSign,
  Star,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';

interface Notification {
  id: string;
  title: string;
  message: string;
  notificationType: string;
  relatedId?: string;
  relatedType?: string;
  actionUrl?: string;
  isRead: boolean;
  isEmailSent: boolean;
  isPushSent: boolean;
  createdAt: string;
}

const NOTIFICATION_TYPES = {
  NEW_MATCH: { icon: User, color: 'text-blue-600', bg: 'bg-blue-50' },
  MESSAGE_RECEIVED: { icon: MessageSquare, color: 'text-green-600', bg: 'bg-green-50' },
  BOOKING_CREATED: { icon: Calendar, color: 'text-purple-600', bg: 'bg-purple-50' },
  BOOKING_CONFIRMED: { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-50' },
  BOOKING_CANCELLED: { icon: XCircle, color: 'text-red-600', bg: 'bg-red-50' },
  PAYMENT_RECEIVED: { icon: DollarSign, color: 'text-green-600', bg: 'bg-green-50' },
  REVIEW_RECEIVED: { icon: Star, color: 'text-yellow-600', bg: 'bg-yellow-50' },
  URGENT_REQUEST: { icon: Zap, color: 'text-orange-600', bg: 'bg-orange-50' },
  SYSTEM_ALERT: { icon: AlertCircle, color: 'text-red-600', bg: 'bg-red-50' },
  INFO: { icon: Info, color: 'text-blue-600', bg: 'bg-blue-50' },
};

export function NotificationCenter() {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (session?.user?.id) {
      fetchNotifications();
      // Polling pour les nouvelles notifications
      const interval = setInterval(fetchNotifications, 30000); // Toutes les 30 secondes
      return () => clearInterval(interval);
    }
  }, [session]);

  const fetchNotifications = async () => {
    try {
      const response = await fetch('/api/notifications?limit=50');
      const data = await response.json();
      
      if (data.success) {
        setNotifications(data.data.notifications);
        setUnreadCount(data.data.unreadCount);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des notifications:', error);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isRead: true }),
      });

      if (response.ok) {
        setNotifications(prev => 
          prev.map(notif => 
            notif.id === notificationId 
              ? { ...notif, isRead: true }
              : notif
          )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Erreur lors du marquage comme lu:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/notifications', {
        method: 'PUT',
      });

      if (response.ok) {
        setNotifications(prev => 
          prev.map(notif => ({ ...notif, isRead: true }))
        );
        setUnreadCount(0);
        toast.success('Toutes les notifications ont été marquées comme lues');
      }
    } catch (error) {
      console.error('Erreur lors du marquage global:', error);
      toast.error('Erreur lors du marquage des notifications');
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }

    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }
  };

  const getNotificationIcon = (type: string) => {
    const config = NOTIFICATION_TYPES[type as keyof typeof NOTIFICATION_TYPES];
    return config || NOTIFICATION_TYPES.INFO;
  };

  const groupedNotifications = notifications.reduce((acc, notification) => {
    const today = new Date();
    const notifDate = new Date(notification.createdAt);
    const diffInDays = Math.floor((today.getTime() - notifDate.getTime()) / (1000 * 60 * 60 * 24));
    
    let group = 'Plus ancien';
    if (diffInDays === 0) group = 'Aujourd\'hui';
    else if (diffInDays === 1) group = 'Hier';
    else if (diffInDays <= 7) group = 'Cette semaine';
    
    if (!acc[group]) acc[group] = [];
    acc[group].push(notification);
    return acc;
  }, {} as Record<string, Notification[]>);

  const unreadNotifications = notifications.filter(n => !n.isRead);
  const readNotifications = notifications.filter(n => n.isRead);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs">
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0" align="end">
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Notifications</CardTitle>
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  disabled={loading}
                >
                  <CheckCheck className="w-4 h-4 mr-1" />
                  Tout marquer
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs defaultValue="all" className="w-full">
              <TabsList className="grid w-full grid-cols-3 mx-4 mb-2">
                <TabsTrigger value="all" className="text-xs">
                  Toutes ({notifications.length})
                </TabsTrigger>
                <TabsTrigger value="unread" className="text-xs">
                  Non lues ({unreadCount})
                </TabsTrigger>
                <TabsTrigger value="read" className="text-xs">
                  Lues ({readNotifications.length})
                </TabsTrigger>
              </TabsList>

              <ScrollArea className="h-96">
                <TabsContent value="all" className="mt-0">
                  {notifications.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Bell className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>Aucune notification</p>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {Object.entries(groupedNotifications).map(([group, groupNotifications]) => (
                        <div key={group}>
                          <div className="px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50">
                            {group}
                          </div>
                          {groupNotifications.map((notification) => {
                            const iconConfig = getNotificationIcon(notification.notificationType);
                            const Icon = iconConfig.icon;
                            
                            return (
                              <div
                                key={notification.id}
                                className={`px-4 py-3 hover:bg-gray-50 cursor-pointer border-l-4 transition-colors ${
                                  notification.isRead 
                                    ? 'border-transparent' 
                                    : 'border-blue-500 bg-blue-50/30'
                                }`}
                                onClick={() => handleNotificationClick(notification)}
                              >
                                <div className="flex items-start space-x-3">
                                  <div className={`p-2 rounded-full ${iconConfig.bg}`}>
                                    <Icon className={`w-4 h-4 ${iconConfig.color}`} />
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between">
                                      <h4 className={`text-sm font-medium truncate ${
                                        notification.isRead ? 'text-gray-700' : 'text-gray-900'
                                      }`}>
                                        {notification.title}
                                      </h4>
                                      {!notification.isRead && (
                                        <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 ml-2"></div>
                                      )}
                                    </div>
                                    <p className={`text-xs mt-1 line-clamp-2 ${
                                      notification.isRead ? 'text-gray-500' : 'text-gray-600'
                                    }`}>
                                      {notification.message}
                                    </p>
                                    <div className="flex items-center mt-2 text-xs text-gray-400">
                                      <Clock className="w-3 h-3 mr-1" />
                                      {formatDistanceToNow(new Date(notification.createdAt), {
                                        addSuffix: true,
                                        locale: fr
                                      })}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="unread" className="mt-0">
                  {unreadNotifications.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <CheckCircle className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>Aucune notification non lue</p>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {unreadNotifications.map((notification) => {
                        const iconConfig = getNotificationIcon(notification.notificationType);
                        const Icon = iconConfig.icon;
                        
                        return (
                          <div
                            key={notification.id}
                            className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-l-4 border-blue-500 bg-blue-50/30 transition-colors"
                            onClick={() => handleNotificationClick(notification)}
                          >
                            <div className="flex items-start space-x-3">
                              <div className={`p-2 rounded-full ${iconConfig.bg}`}>
                                <Icon className={`w-4 h-4 ${iconConfig.color}`} />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between">
                                  <h4 className="text-sm font-medium text-gray-900 truncate">
                                    {notification.title}
                                  </h4>
                                  <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 ml-2"></div>
                                </div>
                                <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                  {notification.message}
                                </p>
                                <div className="flex items-center mt-2 text-xs text-gray-400">
                                  <Clock className="w-3 h-3 mr-1" />
                                  {formatDistanceToNow(new Date(notification.createdAt), {
                                    addSuffix: true,
                                    locale: fr
                                  })}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="read" className="mt-0">
                  {readNotifications.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Bell className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>Aucune notification lue</p>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {readNotifications.map((notification) => {
                        const iconConfig = getNotificationIcon(notification.notificationType);
                        const Icon = iconConfig.icon;
                        
                        return (
                          <div
                            key={notification.id}
                            className="px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors opacity-75"
                            onClick={() => handleNotificationClick(notification)}
                          >
                            <div className="flex items-start space-x-3">
                              <div className={`p-2 rounded-full ${iconConfig.bg}`}>
                                <Icon className={`w-4 h-4 ${iconConfig.color}`} />
                              </div>
                              <div className="flex-1 min-w-0">
                                <h4 className="text-sm font-medium text-gray-700 truncate">
                                  {notification.title}
                                </h4>
                                <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                                  {notification.message}
                                </p>
                                <div className="flex items-center mt-2 text-xs text-gray-400">
                                  <Clock className="w-3 h-3 mr-1" />
                                  {formatDistanceToNow(new Date(notification.createdAt), {
                                    addSuffix: true,
                                    locale: fr
                                  })}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </TabsContent>
              </ScrollArea>
            </Tabs>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
}
