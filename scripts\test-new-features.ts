import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDatabaseModels() {
  console.log('🧪 Test des nouveaux modèles de base de données...\n');

  try {
    // Test PortfolioItem
    console.log('📸 Test PortfolioItem...');
    const portfolioCount = await prisma.portfolioItem.count();
    console.log(`   ✅ ${portfolioCount} éléments de portfolio trouvés`);

    if (portfolioCount > 0) {
      const samplePortfolio = await prisma.portfolioItem.findFirst({
        include: {
          user: {
            select: { name: true, userType: true }
          }
        }
      });
      console.log(`   📋 Exemple: "${samplePortfolio?.title}" par ${samplePortfolio?.user.name}`);
    }

    // Test AvailabilitySlot
    console.log('\n📅 Test AvailabilitySlot...');
    const slotsCount = await prisma.availabilitySlot.count();
    console.log(`   ✅ ${slotsCount} créneaux de disponibilité trouvés`);

    if (slotsCount > 0) {
      const sampleSlot = await prisma.availabilitySlot.findFirst({
        include: {
          user: {
            select: { name: true }
          }
        }
      });
      const dayNames = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
      console.log(`   📋 Exemple: ${dayNames[sampleSlot?.dayOfWeek || 0]} ${sampleSlot?.startTime}-${sampleSlot?.endTime} (${sampleSlot?.user.name})`);
    }

    // Test Booking
    console.log('\n📋 Test Booking...');
    const bookingsCount = await prisma.booking.count();
    console.log(`   ✅ ${bookingsCount} réservations trouvées`);

    if (bookingsCount > 0) {
      const sampleBooking = await prisma.booking.findFirst({
        include: {
          match: {
            include: {
              client: { select: { name: true } },
              prestataire: { select: { name: true } }
            }
          }
        }
      });
      console.log(`   📋 Exemple: ${sampleBooking?.scheduledDate.toLocaleDateString()} à ${sampleBooking?.scheduledTime}`);
      console.log(`       Client: ${sampleBooking?.match.client.name} → Prestataire: ${sampleBooking?.match.prestataire.name}`);
    }

    // Test Notifications
    console.log('\n🔔 Test Notifications...');
    const notificationsCount = await prisma.notification.count();
    const unreadCount = await prisma.notification.count({
      where: { isRead: false }
    });
    console.log(`   ✅ ${notificationsCount} notifications trouvées (${unreadCount} non lues)`);

    if (notificationsCount > 0) {
      const sampleNotif = await prisma.notification.findFirst({
        include: {
          user: { select: { name: true } }
        }
      });
      console.log(`   📋 Exemple: "${sampleNotif?.title}" pour ${sampleNotif?.user.name}`);
    }

    console.log('\n✅ Tous les modèles de base de données fonctionnent correctement !');

  } catch (error) {
    console.error('❌ Erreur lors du test des modèles:', error);
    throw error;
  }
}

async function testAPIEndpoints() {
  console.log('\n🌐 Test des endpoints API...\n');

  const baseUrl = 'http://localhost:3000';
  
  const endpoints = [
    { path: '/api/portfolio', method: 'GET', description: 'Liste des portfolios' },
    { path: '/api/availability', method: 'GET', description: 'Créneaux de disponibilité' },
    { path: '/api/bookings', method: 'GET', description: 'Réservations' },
    { path: '/api/notifications', method: 'GET', description: 'Notifications' },
  ];

  console.log('⚠️  Note: Ces tests nécessitent que le serveur soit démarré (npm run dev)');
  console.log('⚠️  Et une session utilisateur authentifiée pour certains endpoints\n');

  for (const endpoint of endpoints) {
    console.log(`🔗 ${endpoint.method} ${endpoint.path} - ${endpoint.description}`);
    
    try {
      const response = await fetch(`${baseUrl}${endpoint.path}`, {
        method: endpoint.method,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        console.log(`   ✅ Status: ${response.status} ${response.statusText}`);
      } else if (response.status === 401) {
        console.log(`   ⚠️  Status: ${response.status} (Authentification requise - normal)`);
      } else {
        console.log(`   ❌ Status: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.log(`   ❌ Erreur: Serveur non démarré ou inaccessible`);
    }
  }
}

async function generateTestReport() {
  console.log('\n📊 Génération du rapport de test...\n');

  try {
    // Statistiques générales
    const stats = {
      users: await prisma.user.count(),
      prestataires: await prisma.user.count({
        where: {
          OR: [
            { userType: 'PRESTATAIRE' },
            { userType: 'BOTH' }
          ]
        }
      }),
      services: await prisma.service.count(),
      requests: await prisma.request.count(),
      matches: await prisma.match.count(),
      portfolioItems: await prisma.portfolioItem.count(),
      availabilitySlots: await prisma.availabilitySlot.count(),
      bookings: await prisma.booking.count(),
      notifications: await prisma.notification.count(),
      unreadNotifications: await prisma.notification.count({
        where: { isRead: false }
      }),
    };

    console.log('📈 RAPPORT DE TEST - NOUVELLES FONCTIONNALITÉS');
    console.log('=' .repeat(50));
    console.log(`👥 Utilisateurs totaux: ${stats.users}`);
    console.log(`🛠️  Prestataires: ${stats.prestataires}`);
    console.log(`💼 Services: ${stats.services}`);
    console.log(`📋 Demandes: ${stats.requests}`);
    console.log(`🤝 Matches: ${stats.matches}`);
    console.log('');
    console.log('🆕 NOUVELLES FONCTIONNALITÉS:');
    console.log(`📸 Éléments de portfolio: ${stats.portfolioItems}`);
    console.log(`📅 Créneaux de disponibilité: ${stats.availabilitySlots}`);
    console.log(`📋 Réservations: ${stats.bookings}`);
    console.log(`🔔 Notifications: ${stats.notifications} (${stats.unreadNotifications} non lues)`);
    console.log('');

    // Vérifications de cohérence
    console.log('🔍 VÉRIFICATIONS:');
    
    if (stats.prestataires > 0 && stats.portfolioItems === 0) {
      console.log('⚠️  Aucun élément de portfolio - Exécuter le script de seeding');
    } else {
      console.log('✅ Portfolio: Données présentes');
    }

    if (stats.prestataires > 0 && stats.availabilitySlots === 0) {
      console.log('⚠️  Aucun créneau de disponibilité - Exécuter le script de seeding');
    } else {
      console.log('✅ Agenda: Données présentes');
    }

    if (stats.matches > 0 && stats.bookings === 0) {
      console.log('⚠️  Aucune réservation malgré les matches existants');
    } else if (stats.bookings > 0) {
      console.log('✅ Réservations: Données présentes');
    }

    if (stats.users > 0 && stats.notifications === 0) {
      console.log('⚠️  Aucune notification - Exécuter le script de seeding');
    } else {
      console.log('✅ Notifications: Données présentes');
    }

    console.log('');
    console.log('🎯 PROCHAINES ACTIONS RECOMMANDÉES:');
    
    if (stats.portfolioItems === 0 || stats.availabilitySlots === 0 || stats.notifications === 0) {
      console.log('1. Exécuter: npx tsx scripts/seed-new-features.ts');
    }
    
    console.log('2. Démarrer le serveur: npm run dev');
    console.log('3. Tester les nouvelles pages:');
    console.log('   - http://localhost:3000/dashboard/portfolio');
    console.log('   - http://localhost:3000/dashboard/agenda');
    console.log('   - http://localhost:3000/dashboard/map');
    console.log('4. Tester les fonctionnalités de partage social');
    console.log('5. Vérifier le centre de notifications');

  } catch (error) {
    console.error('❌ Erreur lors de la génération du rapport:', error);
    throw error;
  }
}

async function main() {
  console.log('🚀 Test des Nouvelles Fonctionnalités QribLik\n');
  
  try {
    await testDatabaseModels();
    await generateTestReport();
    
    // Test des APIs seulement si demandé
    if (process.argv.includes('--test-api')) {
      await testAPIEndpoints();
    } else {
      console.log('\n💡 Pour tester les APIs, ajoutez --test-api (serveur requis)');
    }
    
    console.log('\n🎉 Tests terminés avec succès !');
    
  } catch (error) {
    console.error('\n❌ Échec des tests:', error);
    process.exit(1);
  }
}

main()
  .catch((e) => {
    console.error('❌ Erreur critique:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
