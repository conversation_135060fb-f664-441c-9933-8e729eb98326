import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from './providers'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'QirbLik - Marketplace de Services au Maroc',
  description: 'Trouvez et proposez des services de proximité au Maroc',
  keywords: 'services, maroc, bricolage, ménage, cours, jardinage, réparations',
  authors: [{ name: 'QribLik' }],
  creator: 'QribLik',
  metadataBase: new URL('https://qriblik.ma'),
  openGraph: {
    type: 'website',
    locale: 'fr_MA',
    url: 'https://qriblik.ma',
    title: 'QribLik - Services de Proximité au Maroc',
    description: 'La plateforme n°1 des services entre particuliers au Maroc.',
    siteName: 'QribLik',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'QribLik - Services de Proximité au Maroc',
    description: 'La plateforme n°1 des services entre particuliers au Maroc.',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="fr" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}