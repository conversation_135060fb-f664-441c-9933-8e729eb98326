import { prisma } from '../lib/prisma'
import { hash } from 'bcryptjs'

async function createTestUsers() {
  console.log('🧪 Création des utilisateurs de test...\n')

  try {
    // Supprimer les utilisateurs de test existants
    await prisma.user.deleteMany({
      where: {
        email: {
          in: [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
          ]
        }
      }
    })

    // Hasher les mots de passe
    const hashedPassword = await hash('password123', 12)

    // Créer les utilisateurs de test
    const testUsers = [
      {
        name: 'Test User',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: 'PRESTATAIRE',
        locationCity: 'Casablanca',
        locationRegion: 'Casablanca-Settat',
        phone: '+212600000001',
        rating: 4.8,
        reviewCount: 24,
        isVerified: true,
        bio: 'Artisan expérimenté spécialisé dans les rénovations',
        responseTime: 30,
        completionRate: 0.95,
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: 'PRESTATAIRE',
        locationCity: 'Rabat',
        locationRegion: 'Rabat-Salé-Kénitra',
        phone: '+212600000002',
        rating: 4.6,
        reviewCount: 18,
        isVerified: true,
        bio: 'Professionnel du bricolage et des réparations',
        responseTime: 45,
        completionRate: 0.92,
      },
      {
        name: 'Fatima Client',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: 'CLIENT',
        locationCity: 'Marrakech',
        locationRegion: 'Marrakech-Safi',
        phone: '+212600000003',
        rating: null,
        reviewCount: 0,
        isVerified: true,
        bio: 'Cliente régulière de services de proximité',
      },
      {
        name: 'Admin QribLik',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: 'BOTH',
        locationCity: 'Casablanca',
        locationRegion: 'Casablanca-Settat',
        phone: '+212600000004',
        rating: 5.0,
        reviewCount: 50,
        isVerified: true,
        bio: 'Administrateur et prestataire expert',
        responseTime: 15,
        completionRate: 1.0,
      }
    ]

    // Insérer les utilisateurs
    for (const userData of testUsers) {
      const user = await prisma.user.create({
        data: userData
      })
      console.log(`✅ Utilisateur créé: ${user.name} (${user.email})`)
    }

    // Créer quelques éléments de portfolio pour les prestataires
    console.log('\n📸 Création d\'éléments de portfolio de test...')
    
    const prestataires = await prisma.user.findMany({
      where: {
        userType: {
          in: ['PRESTATAIRE', 'BOTH']
        }
      }
    })

    for (const prestataire of prestataires) {
      await prisma.portfolioItem.create({
        data: {
          title: `Projet Test - ${prestataire.name}`,
          description: 'Description de test pour le portfolio',
          category: 'BRICOLAGE_REPARATIONS',
          images: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136',
          tags: 'test,portfolio,exemple',
          isPublic: true,
          isFeatured: false,
          viewCount: Math.floor(Math.random() * 100),
          likeCount: Math.floor(Math.random() * 20),
          shareCount: Math.floor(Math.random() * 5),
          locationCity: prestataire.locationCity,
          locationRegion: prestataire.locationRegion,
          userId: prestataire.id,
        }
      })
      console.log(`✅ Portfolio créé pour: ${prestataire.name}`)
    }

    // Créer des créneaux de disponibilité
    console.log('\n📅 Création de créneaux de disponibilité...')
    
    for (const prestataire of prestataires) {
      // Créer des créneaux pour la semaine (Lundi à Vendredi)
      for (let day = 1; day <= 5; day++) {
        await prisma.availabilitySlot.create({
          data: {
            userId: prestataire.id,
            dayOfWeek: day,
            startTime: '09:00',
            endTime: '12:00',
            isAvailable: true,
            slotType: 'WORK',
          }
        })
        
        await prisma.availabilitySlot.create({
          data: {
            userId: prestataire.id,
            dayOfWeek: day,
            startTime: '14:00',
            endTime: '17:00',
            isAvailable: true,
            slotType: 'WORK',
          }
        })
      }
      console.log(`✅ Disponibilités créées pour: ${prestataire.name}`)
    }

    // Créer quelques notifications de test
    console.log('\n🔔 Création de notifications de test...')
    
    const allUsers = await prisma.user.findMany()
    
    for (const user of allUsers) {
      await prisma.notification.create({
        data: {
          title: 'Bienvenue sur QribLik !',
          message: 'Votre compte de test a été créé avec succès.',
          notificationType: 'SYSTEM',
          userId: user.id,
          isRead: false,
          isEmailSent: false,
          isPushSent: false,
        }
      })
      console.log(`✅ Notification créée pour: ${user.name}`)
    }

    console.log('\n🎉 Utilisateurs de test créés avec succès !')
    console.log('\n📋 Comptes disponibles:')
    console.log('  👤 <EMAIL> / password123 (Prestataire)')
    console.log('  🔧 <EMAIL> / password123 (Prestataire)')
    console.log('  👥 <EMAIL> / password123 (Client)')
    console.log('  👑 <EMAIL> / password123 (Admin)')
    
    console.log('\n🧪 Prêt pour les tests E2E !')

  } catch (error) {
    console.error('❌ Erreur lors de la création des utilisateurs de test:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Exécuter le script
if (require.main === module) {
  createTestUsers()
    .then(() => {
      console.log('✅ Script terminé avec succès')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Erreur:', error)
      process.exit(1)
    })
}

export { createTestUsers }
