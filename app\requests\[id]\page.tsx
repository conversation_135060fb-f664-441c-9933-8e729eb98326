'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { 
  MapPin, 
  Star, 
  Clock, 
  User, 
  MessageSquare, 
  Heart, 
  Share2, 
  Phone,
  Mail,
  Calendar,
  AlertCircle,
  ArrowLeft,
  DollarSign,
  CheckCircle,
  XCircle
} from 'lucide-react'
import Link from 'next/link'
import { motion } from 'framer-motion'

interface Request {
  id: string
  title: string
  description: string
  category: string
  subCategory?: string
  tags: string
  budget?: number
  budgetType: string
  urgency: string
  deadline?: string
  images: string
  status: string
  viewCount: number
  proposalCount: number
  requirements?: string
  locationCity?: string
  locationRegion?: string
  client: {
    id: string
    name: string
    image?: string
    rating?: number
    reviewCount: number
    locationCity?: string
    locationRegion?: string
  }
  createdAt: string
}

interface Proposal {
  id: string
  message: string
  price: number
  priceType: string
  deliveryTime: string
  status: string
  provider: {
    id: string
    name: string
    image?: string
    rating?: number
    reviewCount: number
    responseTime?: number
    completionRate: number
  }
  createdAt: string
}

const categories = [
  { value: 'BRICOLAGE_REPARATIONS', label: 'Bricolage & Réparations' },
  { value: 'MENAGE_NETTOYAGE', label: 'Ménage & Nettoyage' },
  { value: 'JARDINAGE_ESPACES_VERTS', label: 'Jardinage & Espaces verts' },
  { value: 'DEMENAGEMENT_TRANSPORT', label: 'Déménagement & Transport' },
  { value: 'COURS_PARTICULIERS', label: 'Cours particuliers' },
  { value: 'SERVICES_PERSONNE', label: 'Services à la personne' },
  { value: 'EVENEMENTS_ANIMATION', label: 'Événements & Animation' },
  { value: 'BEAUTE_BIEN_ETRE', label: 'Beauté & Bien-être' },
  { value: 'INFORMATIQUE_TECH', label: 'Informatique & Tech' },
  { value: 'AUTOMOBILE', label: 'Automobile' },
  { value: 'IMMOBILIER', label: 'Immobilier' },
  { value: 'AUTRES', label: 'Autres' }
]

const urgencyLevels = [
  { value: 'LOW', label: 'Faible', color: 'bg-green-100 text-green-800' },
  { value: 'NORMAL', label: 'Normal', color: 'bg-blue-100 text-blue-800' },
  { value: 'HIGH', label: 'Élevé', color: 'bg-orange-100 text-orange-800' },
  { value: 'EMERGENCY', label: 'Urgent', color: 'bg-red-100 text-red-800' }
]

export default function RequestDetailPage() {
  const params = useParams()
  const [request, setRequest] = useState<Request | null>(null)
  const [proposals, setProposals] = useState<Proposal[]>([])
  const [loading, setLoading] = useState(true)
  const [proposalsLoading, setProposalsLoading] = useState(true)
  const [isFavorited, setIsFavorited] = useState(false)

  useEffect(() => {
    const fetchRequest = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/requests/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setRequest(data)
        }
      } catch (error) {
        console.error('Error fetching request:', error)
      } finally {
        setLoading(false)
      }
    }

    const fetchProposals = async () => {
      try {
        setProposalsLoading(true)
        const response = await fetch(`/api/requests/${params.id}/proposals`)
        if (response.ok) {
          const data = await response.json()
          setProposals(data.proposals || [])
        }
      } catch (error) {
        console.error('Error fetching proposals:', error)
      } finally {
        setProposalsLoading(false)
      }
    }

    if (params.id) {
      fetchRequest()
      fetchProposals()
    }
  }, [params.id])

  const getCategoryLabel = (category: string) => {
    return categories.find(c => c.value === category)?.label || category
  }

  const getBudgetDisplay = (budget?: number, budgetType?: string) => {
    if (!budget) return 'Budget non spécifié'
    return `${budget} MAD ${budgetType === 'HOURLY' ? '/heure' : budgetType === 'DAILY' ? '/jour' : ''}`
  }

  const getUrgencyInfo = (urgency: string) => {
    return urgencyLevels.find(u => u.value === urgency) || urgencyLevels[1]
  }

  const getImageUrls = (images: string) => {
    return images.split(',').filter(Boolean)
  }

  const getTags = (tags: string) => {
    return tags.split(',').filter(Boolean)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-muted/30">
        <Header />
        <main className="container py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-6">
              <Card className="animate-pulse">
                <div className="h-64 bg-muted rounded-t-lg"></div>
                <CardHeader>
                  <div className="h-6 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-4 bg-muted rounded"></div>
                    <div className="h-4 bg-muted rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="space-y-6">
              <Card className="animate-pulse">
                <CardHeader>
                  <div className="h-6 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="h-4 bg-muted rounded"></div>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (!request) {
    return (
      <div className="min-h-screen bg-muted/30">
        <Header />
        <main className="container py-8">
          <Card className="text-center py-12">
            <CardContent>
              <h2 className="text-2xl font-bold mb-4">Demande non trouvée</h2>
              <p className="text-muted-foreground mb-6">
                La demande que vous recherchez n'existe pas ou a été supprimée.
              </p>
              <Button asChild>
                <Link href="/requests">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Retour aux demandes
                </Link>
              </Button>
            </CardContent>
          </Card>
        </main>
        <Footer />
      </div>
    )
  }

  const imageUrls = getImageUrls(request.images)
  const tags = getTags(request.tags)
  const urgencyInfo = getUrgencyInfo(request.urgency)

  return (
    <div className="min-h-screen bg-muted/30">
      <Header />
      
      <main className="container py-8">
        {/* Back Button */}
        <Button variant="ghost" asChild className="mb-6">
          <Link href="/requests">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour aux demandes
          </Link>
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Request Images */}
            {imageUrls.length > 0 && (
              <Card>
                <div className="aspect-video overflow-hidden rounded-t-lg">
                  <img
                    src={imageUrls[0] || '/placeholder-request.jpg'}
                    alt={request.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                {imageUrls.length > 1 && (
                  <div className="p-4">
                    <div className="flex space-x-2 overflow-x-auto">
                      {imageUrls.slice(1).map((url, index) => (
                        <img
                          key={index}
                          src={url}
                          alt={`${request.title} ${index + 2}`}
                          className="w-20 h-20 object-cover rounded cursor-pointer hover:opacity-80 transition-opacity"
                        />
                      ))}
                    </div>
                  </div>
                )}
              </Card>
            )}

            {/* Request Details */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">
                      {getCategoryLabel(request.category)}
                    </Badge>
                    {request.subCategory && (
                      <Badge variant="outline">{request.subCategory}</Badge>
                    )}
                    <Badge className={urgencyInfo.color}>
                      {urgencyInfo.label}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setIsFavorited(!isFavorited)}
                    >
                      <Heart className={`h-4 w-4 ${isFavorited ? 'fill-red-500 text-red-500' : ''}`} />
                    </Button>
                    <Button variant="outline" size="icon">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <CardTitle className="text-3xl mb-2">{request.title}</CardTitle>
                <CardDescription className="text-lg">
                  Publié le {formatDate(request.createdAt)}
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Description</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {request.description}
                  </p>
                </div>

                {/* Requirements */}
                {request.requirements && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Exigences</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {request.requirements}
                    </p>
                  </div>
                )}

                {/* Tags */}
                {tags.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {tags.map((tag, index) => (
                        <Badge key={index} variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      {request.viewCount} vues
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      {request.proposalCount} propositions
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Proposals */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Propositions ({proposals.length})</span>
                  <Badge variant="outline">{request.status}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {proposalsLoading ? (
                  <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="animate-pulse">
                        <div className="flex items-start space-x-4 p-4 border rounded-lg">
                          <div className="w-12 h-12 bg-muted rounded-full"></div>
                          <div className="flex-1 space-y-2">
                            <div className="h-4 bg-muted rounded w-1/4"></div>
                            <div className="h-4 bg-muted rounded w-3/4"></div>
                            <div className="h-4 bg-muted rounded w-1/2"></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : proposals.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageSquare className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">Aucune proposition</h3>
                    <p className="text-muted-foreground">
                      Soyez le premier à proposer vos services pour cette demande.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {proposals.map((proposal) => (
                      <motion.div
                        key={proposal.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-10 w-10">
                              <AvatarImage src={proposal.provider.image} />
                              <AvatarFallback>
                                {proposal.provider.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <h4 className="font-semibold">{proposal.provider.name}</h4>
                              <div className="flex items-center space-x-1">
                                <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                <span className="text-sm text-muted-foreground">
                                  {proposal.provider.rating?.toFixed(1) || 'Nouveau'} ({proposal.provider.reviewCount})
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-primary">
                              {proposal.price} MAD
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Livraison: {proposal.deliveryTime}
                            </div>
                          </div>
                        </div>
                        
                        <p className="text-muted-foreground mb-3">
                          {proposal.message}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <div className="text-sm text-muted-foreground">
                            Proposé le {formatDate(proposal.createdAt)}
                          </div>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline">
                              <MessageSquare className="mr-2 h-3 w-3" />
                              Discuter
                            </Button>
                            <Button size="sm">
                              <CheckCircle className="mr-2 h-3 w-3" />
                              Accepter
                            </Button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Budget & Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <DollarSign className="h-5 w-5 mr-2" />
                  Budget
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-2xl font-bold text-primary">
                  {getBudgetDisplay(request.budget, request.budgetType)}
                </div>
                
                {request.deadline && (
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>Échéance: {formatDate(request.deadline)}</span>
                  </div>
                )}

                <Separator />

                <Button className="w-full" size="lg">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Faire une proposition
                </Button>
                <Button variant="outline" className="w-full">
                  <Phone className="mr-2 h-4 w-4" />
                  Contacter le client
                </Button>
              </CardContent>
            </Card>

            {/* Client Info */}
            <Card>
              <CardHeader>
                <CardTitle>Client</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start space-x-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={request.client.image} />
                    <AvatarFallback>
                      {request.client.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-semibold">{request.client.name}</h3>
                    <div className="flex items-center space-x-1 mb-2">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">
                        {request.client.rating?.toFixed(1) || 'Nouveau'}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        ({request.client.reviewCount} avis)
                      </span>
                    </div>
                    {request.client.locationCity && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="h-3 w-3 mr-1" />
                        {request.client.locationCity}
                        {request.client.locationRegion && `, ${request.client.locationRegion}`}
                      </div>
                    )}
                  </div>
                </div>

                <Button variant="outline" className="w-full mt-4">
                  <Mail className="mr-2 h-4 w-4" />
                  Voir le profil
                </Button>
              </CardContent>
            </Card>

            {/* Location */}
            {request.locationCity && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MapPin className="h-5 w-5 mr-2" />
                    Localisation
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-3">
                    {request.locationCity}
                    {request.locationRegion && `, ${request.locationRegion}`}
                  </p>
                  <Button variant="outline" className="w-full">
                    Voir sur la carte
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
