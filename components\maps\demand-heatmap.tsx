'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MapPin, 
  TrendingUp, 
  Users, 
  Clock, 
  Filter,
  RefreshCw,
  Navigation,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';

interface DemandZone {
  id: string;
  city: string;
  region: string;
  lat: number;
  lng: number;
  demandCount: number;
  averagePrice: number;
  categories: {
    category: string;
    count: number;
    avgPrice: number;
  }[];
  trend: 'up' | 'down' | 'stable';
  urgentRequests: number;
  lastUpdated: string;
}

interface DemandHeatmapProps {
  userLocation?: {
    lat: number;
    lng: number;
  };
  onZoneSelect?: (zone: DemandZone) => void;
}

const MOROCCAN_CITIES = [
  { name: 'Casablanca', region: 'Casablanca-Settat', lat: 33.5731, lng: -7.5898 },
  { name: 'Ra<PERSON>', region: 'Rabat-Salé-Kénitra', lat: 34.0209, lng: -6.8416 },
  { name: 'Marrakech', region: 'Marrakech-Safi', lat: 31.6295, lng: -7.9811 },
  { name: 'Fès', region: 'Fès-Meknès', lat: 34.0181, lng: -5.0078 },
  { name: 'Tangier', region: 'Tanger-Tétouan-Al Hoceïma', lat: 35.7595, lng: -5.8340 },
  { name: 'Agadir', region: 'Souss-Massa', lat: 30.4278, lng: -9.5981 },
  { name: 'Meknès', region: 'Fès-Meknès', lat: 33.8935, lng: -5.5473 },
  { name: 'Oujda', region: 'Oriental', lat: 34.6814, lng: -1.9086 },
  { name: 'Kenitra', region: 'Rabat-Salé-Kénitra', lat: 34.2610, lng: -6.5802 },
  { name: 'Tétouan', region: 'Tanger-Tétouan-Al Hoceïma', lat: 35.5889, lng: -5.3626 },
];

const CATEGORY_COLORS = {
  'BRICOLAGE_REPARATIONS': 'bg-blue-500',
  'MENAGE_NETTOYAGE': 'bg-green-500',
  'JARDINAGE_ESPACES_VERTS': 'bg-emerald-500',
  'COURS_FORMATION': 'bg-purple-500',
  'TRANSPORT_DEMENAGEMENT': 'bg-orange-500',
  'EVENEMENTIEL': 'bg-pink-500',
  'BEAUTE_BIEN_ETRE': 'bg-rose-500',
  'INFORMATIQUE_NUMERIQUE': 'bg-indigo-500',
  'AUTRE': 'bg-gray-500',
};

const CATEGORY_LABELS = {
  'BRICOLAGE_REPARATIONS': 'Bricolage',
  'MENAGE_NETTOYAGE': 'Ménage',
  'JARDINAGE_ESPACES_VERTS': 'Jardinage',
  'COURS_FORMATION': 'Formation',
  'TRANSPORT_DEMENAGEMENT': 'Transport',
  'EVENEMENTIEL': 'Événementiel',
  'BEAUTE_BIEN_ETRE': 'Beauté',
  'INFORMATIQUE_NUMERIQUE': 'Informatique',
  'AUTRE': 'Autre',
};

export function DemandHeatmap({ userLocation, onZoneSelect }: DemandHeatmapProps) {
  const [demandZones, setDemandZones] = useState<DemandZone[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'demand' | 'price' | 'distance'>('demand');
  const [userPosition, setUserPosition] = useState<{ lat: number; lng: number } | null>(
    userLocation || null
  );

  useEffect(() => {
    fetchDemandData();
    if (!userLocation) {
      getCurrentLocation();
    }
  }, [selectedCategory]);

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserPosition({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          });
        },
        (error) => {
          console.error('Erreur de géolocalisation:', error);
          // Position par défaut (Casablanca)
          setUserPosition({ lat: 33.5731, lng: -7.5898 });
        }
      );
    }
  };

  const fetchDemandData = async () => {
    try {
      setLoading(true);
      
      // Simulation de données - à remplacer par un vrai appel API
      const mockData: DemandZone[] = MOROCCAN_CITIES.map((city, index) => ({
        id: `zone-${index}`,
        city: city.name,
        region: city.region,
        lat: city.lat,
        lng: city.lng,
        demandCount: Math.floor(Math.random() * 100) + 10,
        averagePrice: Math.floor(Math.random() * 500) + 100,
        categories: Object.keys(CATEGORY_LABELS).map(category => ({
          category,
          count: Math.floor(Math.random() * 20) + 1,
          avgPrice: Math.floor(Math.random() * 300) + 50,
        })),
        trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as 'up' | 'down' | 'stable',
        urgentRequests: Math.floor(Math.random() * 10),
        lastUpdated: new Date().toISOString(),
      }));

      setDemandZones(mockData);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      toast.error('Erreur lors du chargement de la carte des demandes');
    } finally {
      setLoading(false);
    }
  };

  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number) => {
    const R = 6371; // Rayon de la Terre en km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const sortedZones = [...demandZones].sort((a, b) => {
    switch (sortBy) {
      case 'demand':
        return b.demandCount - a.demandCount;
      case 'price':
        return b.averagePrice - a.averagePrice;
      case 'distance':
        if (!userPosition) return 0;
        const distA = calculateDistance(userPosition.lat, userPosition.lng, a.lat, a.lng);
        const distB = calculateDistance(userPosition.lat, userPosition.lng, b.lat, b.lng);
        return distA - distB;
      default:
        return 0;
    }
  });

  const getIntensityColor = (demandCount: number) => {
    const maxDemand = Math.max(...demandZones.map(z => z.demandCount));
    const intensity = demandCount / maxDemand;
    
    if (intensity > 0.8) return 'bg-red-500';
    if (intensity > 0.6) return 'bg-orange-500';
    if (intensity > 0.4) return 'bg-yellow-500';
    if (intensity > 0.2) return 'bg-green-500';
    return 'bg-blue-500';
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down':
        return <TrendingUp className="w-4 h-4 text-red-600 rotate-180" />;
      default:
        return <div className="w-4 h-4 bg-gray-400 rounded-full" />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Contrôles */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="w-5 h-5" />
              <span>Carte des Demandes - Maroc</span>
            </CardTitle>
            <Button onClick={fetchDemandData} size="sm" variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Actualiser
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Toutes les catégories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes les catégories</SelectItem>
                  {Object.entries(CATEGORY_LABELS).map(([key, label]) => (
                    <SelectItem key={key} value={key}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <Select value={sortBy} onValueChange={(value: 'demand' | 'price' | 'distance') => setSortBy(value)}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="demand">Trier par demande</SelectItem>
                <SelectItem value="price">Trier par prix</SelectItem>
                <SelectItem value="distance">Trier par distance</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Légende */}
      <Card>
        <CardContent className="p-4">
          <h3 className="font-medium mb-3">Intensité des demandes</h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-500 rounded"></div>
              <span className="text-sm">Faible</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span className="text-sm">Modérée</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-yellow-500 rounded"></div>
              <span className="text-sm">Élevée</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-orange-500 rounded"></div>
              <span className="text-sm">Très élevée</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span className="text-sm">Critique</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Liste des zones */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sortedZones.map((zone) => {
          const distance = userPosition 
            ? calculateDistance(userPosition.lat, userPosition.lng, zone.lat, zone.lng)
            : null;
          
          return (
            <Card 
              key={zone.id} 
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => onZoneSelect?.(zone)}
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">{zone.city}</h3>
                    <p className="text-sm text-gray-600">{zone.region}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getTrendIcon(zone.trend)}
                    <div className={`w-3 h-3 rounded-full ${getIntensityColor(zone.demandCount)}`}></div>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-blue-600" />
                    <span>{zone.demandCount} demandes</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600 font-medium">
                      {zone.averagePrice} MAD
                    </span>
                  </div>
                </div>
                
                {zone.urgentRequests > 0 && (
                  <div className="flex items-center space-x-2">
                    <Zap className="w-4 h-4 text-orange-500" />
                    <Badge variant="destructive" className="text-xs">
                      {zone.urgentRequests} urgentes
                    </Badge>
                  </div>
                )}
                
                {distance && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Navigation className="w-4 h-4" />
                    <span>{distance.toFixed(1)} km</span>
                  </div>
                )}
                
                {/* Top catégories */}
                <div>
                  <p className="text-xs text-gray-500 mb-2">Catégories populaires:</p>
                  <div className="flex flex-wrap gap-1">
                    {zone.categories
                      .sort((a, b) => b.count - a.count)
                      .slice(0, 3)
                      .map((cat) => (
                        <Badge 
                          key={cat.category} 
                          variant="secondary" 
                          className="text-xs"
                        >
                          {CATEGORY_LABELS[cat.category as keyof typeof CATEGORY_LABELS]} ({cat.count})
                        </Badge>
                      ))}
                  </div>
                </div>
                
                <div className="text-xs text-gray-500 pt-2 border-t">
                  <Clock className="w-3 h-3 inline mr-1" />
                  Mis à jour {new Date(zone.lastUpdated).toLocaleTimeString()}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
