const fs = require('fs');
const path = require('path');

console.log('🎯 VÉRIFICATION FINALE - QribLik\n');

// 1. Vérifier les dépendances critiques
console.log('📦 1. Vérification des dépendances critiques...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };

const criticalDeps = [
  // Radix UI essentiels
  '@radix-ui/react-switch',
  '@radix-ui/react-dialog',
  '@radix-ui/react-select',
  '@radix-ui/react-tabs',
  '@radix-ui/react-popover',
  '@radix-ui/react-scroll-area',
  '@radix-ui/react-tooltip',
  
  // Autres critiques
  'sonner',
  'next-themes',
  'date-fns',
  'lucide-react',
  'class-variance-authority',
  'clsx',
  'tailwind-merge'
];

let missingCount = 0;
criticalDeps.forEach(dep => {
  if (deps[dep]) {
    console.log(`  ✅ ${dep}`);
  } else {
    console.log(`  ❌ ${dep} - MANQUANT`);
    missingCount++;
  }
});

// 2. Vérifier les fichiers critiques
console.log('\n📄 2. Vérification des fichiers critiques...');
const criticalFiles = [
  'app/providers.tsx',
  'components/ui/switch.tsx',
  'components/ui/sonner.tsx',
  'app/dashboard/portfolio/page.tsx',
  'app/dashboard/agenda/page.tsx',
  'app/dashboard/map/page.tsx'
];

let missingFiles = 0;
criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - MANQUANT`);
    missingFiles++;
  }
});

// 3. Vérifier la configuration
console.log('\n⚙️ 3. Vérification de la configuration...');
const configChecks = [
  { file: '.env.local', desc: 'Variables d\'environnement' },
  { file: 'prisma/schema.prisma', desc: 'Schéma de base de données' },
  { file: 'lib/auth.ts', desc: 'Configuration NextAuth' }
];

configChecks.forEach(check => {
  if (fs.existsSync(check.file)) {
    console.log(`  ✅ ${check.desc} (${check.file})`);
  } else {
    console.log(`  ⚠️  ${check.desc} (${check.file}) - À vérifier`);
  }
});

// 4. Résumé et recommandations
console.log('\n📊 RÉSUMÉ:');
console.log(`  📦 Dépendances manquantes: ${missingCount}`);
console.log(`  📄 Fichiers manquants: ${missingFiles}`);

if (missingCount === 0 && missingFiles === 0) {
  console.log('\n🎉 EXCELLENT ! Tous les éléments critiques sont en place.');
  console.log('\n🚀 PROCHAINES ÉTAPES:');
  console.log('  1. Vérifier que NEXTAUTH_SECRET est défini dans .env.local');
  console.log('  2. Redémarrer le serveur: npm run dev');
  console.log('  3. Tester les pages:');
  console.log('     - http://localhost:3000/dashboard/portfolio');
  console.log('     - http://localhost:3000/dashboard/agenda');
  console.log('     - http://localhost:3000/dashboard/map');
  console.log('\n✅ Le problème "Module not found" devrait être résolu !');
} else {
  console.log('\n⚠️  ACTIONS REQUISES:');
  if (missingCount > 0) {
    console.log('  1. Installer les dépendances manquantes');
  }
  if (missingFiles > 0) {
    console.log('  2. Vérifier les fichiers manquants');
  }
  console.log('  3. Redémarrer le serveur après corrections');
}

// 5. Test des imports problématiques
console.log('\n🔍 4. Test des imports dans les pages...');
const pagesToCheck = [
  'app/dashboard/portfolio/page.tsx',
  'app/dashboard/agenda/page.tsx'
];

pagesToCheck.forEach(page => {
  if (fs.existsSync(page)) {
    const content = fs.readFileSync(page, 'utf8');
    console.log(`  📄 ${page}:`);
    
    // Vérifier les imports critiques
    const imports = [
      { pattern: "from 'sonner'", name: 'sonner' },
      { pattern: "from 'next-auth/react'", name: 'next-auth' },
      { pattern: "from 'lucide-react'", name: 'lucide-react' },
      { pattern: "from '@/components/ui/switch'", name: 'switch component' }
    ];
    
    imports.forEach(imp => {
      if (content.includes(imp.pattern)) {
        console.log(`    ✅ ${imp.name}`);
      } else {
        console.log(`    ⚠️  ${imp.name} - Non utilisé`);
      }
    });
  }
});

console.log('\n💡 CONSEILS:');
console.log('  - Si des erreurs persistent, nettoyez le cache: Remove-Item -Recurse -Force .next');
console.log('  - Redémarrez complètement le serveur après les installations');
console.log('  - Vérifiez les logs du terminal pour des erreurs spécifiques');

console.log('\n📞 SUPPORT:');
console.log('  - Consultez RESOLUTION_FINALE.md pour un guide détaillé');
console.log('  - Utilisez les scripts de diagnostic en cas de problème');
