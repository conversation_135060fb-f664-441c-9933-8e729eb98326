'use client'

import { useState } from 'react'
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js'
import { getStripe } from '@/lib/stripe'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CreditCard, Shield, Info } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface PaymentFormProps {
  clientSecret: string
  amount: number
  platformFee: number
  currency?: string
  description?: string
  onSuccess?: () => void
  onError?: (error: string) => void
}

function PaymentFormContent({ 
  amount, 
  platformFee, 
  currency = 'MAD', 
  description,
  onSuccess,
  onError 
}: Omit<PaymentFormProps, 'clientSecret'>) {
  const stripe = useStripe()
  const elements = useElements()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements) {
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const { error: submitError } = await elements.submit()
      if (submitError) {
        setError(submitError.message || 'Erreur lors de la soumission')
        setIsLoading(false)
        return
      }

      const { error: confirmError } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/dashboard/payments/success`,
        },
      })

      if (confirmError) {
        setError(confirmError.message || 'Erreur lors du paiement')
        onError?.(confirmError.message || 'Erreur lors du paiement')
      } else {
        onSuccess?.()
      }
    } catch (err) {
      setError('Une erreur inattendue s\'est produite')
      onError?.('Une erreur inattendue s\'est produite')
    } finally {
      setIsLoading(false)
    }
  }

  const providerAmount = amount - platformFee

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Paiement sécurisé
        </CardTitle>
        <CardDescription>
          {description || 'Finaliser votre paiement'}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Résumé du paiement */}
        <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Montant total</span>
            <span className="font-semibold">{amount.toFixed(2)} {currency}</span>
          </div>
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Frais de plateforme (5%)</span>
            <span className="text-gray-600">{platformFee.toFixed(2)} {currency}</span>
          </div>
          <div className="flex justify-between items-center text-sm border-t pt-2">
            <span className="text-gray-600">Montant au prestataire</span>
            <span className="text-green-600 font-medium">{providerAmount.toFixed(2)} {currency}</span>
          </div>
        </div>

        {/* Informations de sécurité */}
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription className="text-sm">
            Vos informations de paiement sont sécurisées par Stripe et chiffrées SSL.
          </AlertDescription>
        </Alert>

        {/* Formulaire de paiement */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <PaymentElement 
            options={{
              layout: 'tabs'
            }}
          />

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button 
            type="submit" 
            disabled={!stripe || isLoading}
            className="w-full"
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Traitement en cours...
              </>
            ) : (
              <>
                Payer {amount.toFixed(2)} {currency}
              </>
            )}
          </Button>
        </form>

        {/* Méthodes de paiement acceptées */}
        <div className="text-center space-y-2">
          <p className="text-xs text-gray-500">Méthodes de paiement acceptées</p>
          <div className="flex justify-center gap-2">
            <Badge variant="outline" className="text-xs">Visa</Badge>
            <Badge variant="outline" className="text-xs">Mastercard</Badge>
            <Badge variant="outline" className="text-xs">CMI</Badge>
            <Badge variant="outline" className="text-xs">Banque</Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function PaymentForm(props: PaymentFormProps) {
  const stripePromise = getStripe()

  if (!props.clientSecret) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Elements 
      stripe={stripePromise} 
      options={{
        clientSecret: props.clientSecret,
        appearance: {
          theme: 'stripe',
          variables: {
            colorPrimary: '#059669',
            colorBackground: '#ffffff',
            colorText: '#1f2937',
            colorDanger: '#dc2626',
            fontFamily: 'Inter, system-ui, sans-serif',
            spacingUnit: '4px',
            borderRadius: '8px',
          }
        }
      }}
    >
      <PaymentFormContent {...props} />
    </Elements>
  )
}
