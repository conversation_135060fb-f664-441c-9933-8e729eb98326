import { NextResponse } from 'next/server'
import prisma from '@/lib/prisma'

export async function GET() {
  try {
    // Test simple de connexion à la base de données
    await prisma.$queryRaw`SELECT 1`
    
    // Test de récupération des données
    const userCount = await prisma.user.count()
    const serviceCount = await prisma.service.count()
    const requestCount = await prisma.request.count()
    
    return NextResponse.json({
      status: 'success',
      message: 'Base de données accessible',
      data: {
        userCount,
        serviceCount,
        requestCount
      }
    })
  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json(
      {
        status: 'error',
        message: 'Erreur de connexion à la base de données',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
