'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { 
  MapPin, 
  Star, 
  Clock, 
  User, 
  MessageSquare, 
  Heart, 
  Share2, 
  Phone,
  Mail,
  Calendar,
  CheckCircle,
  ArrowLeft
} from 'lucide-react'
import Link from 'next/link'
import { motion } from 'framer-motion'

interface Service {
  id: string
  title: string
  description: string
  shortDesc?: string
  category: string
  subCategory?: string
  tags: string
  price?: number
  priceType: string
  images: string
  video?: string
  isRemote: boolean
  status: string
  viewCount: number
  contactCount: number
  requirements?: string
  duration?: string
  deliverables: string
  locationCity?: string
  locationRegion?: string
  user: {
    id: string
    name: string
    image?: string
    rating?: number
    reviewCount: number
    responseTime?: number
    completionRate: number
    locationCity?: string
    locationRegion?: string
  }
  createdAt: string
}

const categories = [
  { value: 'BRICOLAGE_REPARATIONS', label: 'Bricolage & Réparations' },
  { value: 'MENAGE_NETTOYAGE', label: 'Ménage & Nettoyage' },
  { value: 'JARDINAGE_ESPACES_VERTS', label: 'Jardinage & Espaces verts' },
  { value: 'DEMENAGEMENT_TRANSPORT', label: 'Déménagement & Transport' },
  { value: 'COURS_PARTICULIERS', label: 'Cours particuliers' },
  { value: 'SERVICES_PERSONNE', label: 'Services à la personne' },
  { value: 'EVENEMENTS_ANIMATION', label: 'Événements & Animation' },
  { value: 'BEAUTE_BIEN_ETRE', label: 'Beauté & Bien-être' },
  { value: 'INFORMATIQUE_TECH', label: 'Informatique & Tech' },
  { value: 'AUTOMOBILE', label: 'Automobile' },
  { value: 'IMMOBILIER', label: 'Immobilier' },
  { value: 'AUTRES', label: 'Autres' }
]

export default function ServiceDetailPage() {
  const params = useParams()
  const [service, setService] = useState<Service | null>(null)
  const [loading, setLoading] = useState(true)
  const [isFavorited, setIsFavorited] = useState(false)

  useEffect(() => {
    const fetchService = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/services/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setService(data)
        }
      } catch (error) {
        console.error('Error fetching service:', error)
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchService()
    }
  }, [params.id])

  const getCategoryLabel = (category: string) => {
    return categories.find(c => c.value === category)?.label || category
  }

  const getPriceDisplay = (price?: number, priceType?: string) => {
    if (!price) return 'Sur devis'
    return `${price} MAD ${priceType === 'HOURLY' ? '/heure' : priceType === 'DAILY' ? '/jour' : ''}`
  }

  const getImageUrls = (images: string) => {
    return images.split(',').filter(Boolean)
  }

  const getTags = (tags: string) => {
    return tags.split(',').filter(Boolean)
  }

  const getDeliverables = (deliverables: string) => {
    return deliverables.split(',').filter(Boolean)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-muted/30">
        <Header />
        <main className="container py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-6">
              <Card className="animate-pulse">
                <div className="h-64 bg-muted rounded-t-lg"></div>
                <CardHeader>
                  <div className="h-6 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-4 bg-muted rounded"></div>
                    <div className="h-4 bg-muted rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="space-y-6">
              <Card className="animate-pulse">
                <CardHeader>
                  <div className="h-6 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="h-4 bg-muted rounded"></div>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (!service) {
    return (
      <div className="min-h-screen bg-muted/30">
        <Header />
        <main className="container py-8">
          <Card className="text-center py-12">
            <CardContent>
              <h2 className="text-2xl font-bold mb-4">Service non trouvé</h2>
              <p className="text-muted-foreground mb-6">
                Le service que vous recherchez n'existe pas ou a été supprimé.
              </p>
              <Button asChild>
                <Link href="/services">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Retour aux services
                </Link>
              </Button>
            </CardContent>
          </Card>
        </main>
        <Footer />
      </div>
    )
  }

  const imageUrls = getImageUrls(service.images)
  const tags = getTags(service.tags)
  const deliverables = getDeliverables(service.deliverables)

  return (
    <div className="min-h-screen bg-muted/30">
      <Header />
      
      <main className="container py-8">
        {/* Back Button */}
        <Button variant="ghost" asChild className="mb-6">
          <Link href="/services">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour aux services
          </Link>
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Service Images */}
            <Card>
              <div className="aspect-video overflow-hidden rounded-t-lg">
                <img
                  src={imageUrls[0] || '/placeholder-service.jpg'}
                  alt={service.title}
                  className="w-full h-full object-cover"
                />
              </div>
              {imageUrls.length > 1 && (
                <div className="p-4">
                  <div className="flex space-x-2 overflow-x-auto">
                    {imageUrls.slice(1).map((url, index) => (
                      <img
                        key={index}
                        src={url}
                        alt={`${service.title} ${index + 2}`}
                        className="w-20 h-20 object-cover rounded cursor-pointer hover:opacity-80 transition-opacity"
                      />
                    ))}
                  </div>
                </div>
              )}
            </Card>

            {/* Service Details */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">
                      {getCategoryLabel(service.category)}
                    </Badge>
                    {service.subCategory && (
                      <Badge variant="outline">{service.subCategory}</Badge>
                    )}
                    {service.isRemote && (
                      <Badge variant="outline">À distance</Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setIsFavorited(!isFavorited)}
                    >
                      <Heart className={`h-4 w-4 ${isFavorited ? 'fill-red-500 text-red-500' : ''}`} />
                    </Button>
                    <Button variant="outline" size="icon">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <CardTitle className="text-3xl mb-2">{service.title}</CardTitle>
                <CardDescription className="text-lg">
                  {service.shortDesc || service.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Description</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {service.description}
                  </p>
                </div>

                {/* Requirements */}
                {service.requirements && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Prérequis</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {service.requirements}
                    </p>
                  </div>
                )}

                {/* Deliverables */}
                {deliverables.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Livrables</h3>
                    <ul className="space-y-2">
                      {deliverables.map((deliverable, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-muted-foreground">{deliverable}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Tags */}
                {tags.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {tags.map((tag, index) => (
                        <Badge key={index} variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      {service.viewCount} vues
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      {service.contactCount} contacts
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Price & Contact */}
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-primary">
                  {getPriceDisplay(service.price, service.priceType)}
                </CardTitle>
                {service.duration && (
                  <CardDescription>
                    Durée estimée : {service.duration}
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full" size="lg">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Contacter le prestataire
                </Button>
                <Button variant="outline" className="w-full">
                  <Phone className="mr-2 h-4 w-4" />
                  Appeler
                </Button>
                <Button variant="outline" className="w-full">
                  <Calendar className="mr-2 h-4 w-4" />
                  Planifier un rendez-vous
                </Button>
              </CardContent>
            </Card>

            {/* Provider Info */}
            <Card>
              <CardHeader>
                <CardTitle>Prestataire</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start space-x-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={service.user.image} />
                    <AvatarFallback>
                      {service.user.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-semibold">{service.user.name}</h3>
                    <div className="flex items-center space-x-1 mb-2">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">
                        {service.user.rating?.toFixed(1) || 'Nouveau'}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        ({service.user.reviewCount} avis)
                      </span>
                    </div>
                    {service.user.locationCity && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="h-3 w-3 mr-1" />
                        {service.user.locationCity}
                        {service.user.locationRegion && `, ${service.user.locationRegion}`}
                      </div>
                    )}
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="space-y-3">
                  {service.user.responseTime && (
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Temps de réponse</span>
                      <span className="font-medium">{service.user.responseTime} min</span>
                    </div>
                  )}
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Taux de réussite</span>
                    <span className="font-medium">{service.user.completionRate}%</span>
                  </div>
                </div>

                <Button variant="outline" className="w-full mt-4">
                  <Mail className="mr-2 h-4 w-4" />
                  Voir le profil
                </Button>
              </CardContent>
            </Card>

            {/* Location */}
            {service.locationCity && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MapPin className="h-5 w-5 mr-2" />
                    Localisation
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    {service.locationCity}
                    {service.locationRegion && `, ${service.locationRegion}`}
                  </p>
                  {!service.isRemote && (
                    <Button variant="outline" className="w-full mt-3">
                      Voir sur la carte
                    </Button>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
