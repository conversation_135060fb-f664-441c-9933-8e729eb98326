# 🧪 Guide d'Utilisation des Tests - QribLik

## 🚀 **Démarrage Rapide**

### **Installation des Dépendances**
```bash
# Installer les navigateurs Playwright
npm run playwright:install

# Vérifier l'installation
npm run test:unit
```

### **Commandes Essentielles**
```bash
# Tests unitaires
npm run test:unit

# Tests d'intégration
npm run test:integration

# Tests E2E
npm run test:e2e

# Tous les tests
npm run test:all

# Tests avec couverture
npm run test:coverage
```

---

## 📁 **Structure des Tests**

```
tests/
├── unit/                    # Tests unitaires
│   ├── components/         # Tests des composants React
│   │   └── Portfolio.test.tsx
│   ├── utils/              # Tests des utilitaires
│   │   └── date.test.ts
│   └── lib/                # Tests des bibliothèques
├── integration/            # Tests d'intégration
│   ├── api/                # Tests des APIs
│   │   └── portfolio.test.ts
│   └── auth/               # Tests d'authentification
├── e2e/                    # Tests end-to-end
│   ├── auth.spec.ts        # Parcours d'authentification
│   └── portfolio.spec.ts   # Gestion du portfolio
├── fixtures/               # Données de test
│   └── portfolio.ts
├── mocks/                  # Mocks et stubs
└── helpers/                # Utilitaires de test
```

---

## 🎯 **Types de Tests**

### **1. Tests Unitaires** 🔬
**Objectif :** Tester des fonctions et composants isolés

```bash
# Exécuter tous les tests unitaires
npm run test:unit

# Exécuter en mode watch
npm run test:watch

# Avec couverture de code
npm run test:coverage
```

**Exemple :**
```typescript
// tests/unit/utils/date.test.ts
describe('formatRelativeTime', () => {
  it('should format recent dates correctly', () => {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    const result = formatRelativeTime(oneHourAgo)
    expect(result).toMatch(/il y a environ 1 heure/)
  })
})
```

### **2. Tests d'Intégration** 🔗
**Objectif :** Tester l'interaction entre composants

```bash
# Exécuter les tests d'intégration
npm run test:integration
```

**Exemple :**
```typescript
// tests/integration/api/portfolio.test.ts
describe('GET /api/portfolio', () => {
  it('should return portfolio items for authenticated user', async () => {
    const response = await GET(mockRequest)
    expect(response.status).toBe(200)
  })
})
```

### **3. Tests E2E** 🎭
**Objectif :** Tester les parcours utilisateur complets

```bash
# Exécuter les tests E2E
npm run test:e2e

# Avec interface graphique
npm run test:e2e:ui

# En mode visible (headed)
npm run test:e2e:headed
```

**Exemple :**
```typescript
// tests/e2e/portfolio.spec.ts
test('should create new portfolio item', async ({ page }) => {
  await page.goto('/dashboard/portfolio')
  await page.getByRole('button', { name: 'Ajouter un projet' }).click()
  // ... rest of test
})
```

---

## 🛠️ **Configuration**

### **Jest (Tests Unitaires/Intégration)**
```javascript
// jest.config.js
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
}
```

### **Playwright (Tests E2E)**
```typescript
// playwright.config.ts
export default defineConfig({
  testDir: './tests/e2e',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
})
```

---

## 📊 **Couverture de Code**

### **Objectifs de Couverture**
- **Fonctions critiques** : 95%+
- **Composants UI** : 85%+
- **APIs** : 90%+
- **Utilitaires** : 95%+

### **Générer un Rapport**
```bash
# Générer le rapport de couverture
npm run test:coverage

# Ouvrir le rapport HTML
# Windows
start coverage/lcov-report/index.html

# macOS
open coverage/lcov-report/index.html
```

### **Interpréter les Métriques**
- **Lines** : Pourcentage de lignes exécutées
- **Functions** : Pourcentage de fonctions appelées
- **Branches** : Pourcentage de branches conditionnelles testées
- **Statements** : Pourcentage d'instructions exécutées

---

## 🎨 **Bonnes Pratiques**

### **Nomenclature des Tests**
```typescript
// ✅ Bon
describe('Portfolio Component', () => {
  describe('when user is authenticated', () => {
    it('should display portfolio items', () => {})
    it('should allow creating new items', () => {})
  })
  
  describe('when user is not authenticated', () => {
    it('should redirect to login', () => {})
  })
})

// ❌ Mauvais
describe('Portfolio', () => {
  it('works', () => {})
  it('test2', () => {})
})
```

### **Structure AAA (Arrange-Act-Assert)**
```typescript
it('should calculate duration correctly', () => {
  // Arrange
  const startTime = '09:00'
  const endTime = '17:00'
  
  // Act
  const result = calculateDuration(startTime, endTime)
  
  // Assert
  expect(result).toBe(480) // 8 hours = 480 minutes
})
```

### **Mocking Efficace**
```typescript
// Mock des dépendances externes
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: { user: { id: '1', userType: 'PRESTATAIRE' } },
    status: 'authenticated',
  })),
}))

// Mock des APIs
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({ success: true, data: [] }),
  })
)
```

### **Tests E2E Robustes**
```typescript
// ✅ Attendre les éléments
await expect(page.getByText('Portfolio')).toBeVisible()

// ✅ Utiliser des sélecteurs stables
await page.getByRole('button', { name: 'Ajouter' }).click()
await page.getByTestId('portfolio-form').fill('Test')

// ✅ Gérer les états de chargement
await page.waitForLoadState('networkidle')
```

---

## 🚨 **Debugging des Tests**

### **Tests Unitaires**
```bash
# Mode debug avec Node.js inspector
node --inspect-brk node_modules/.bin/jest --runInBand

# Tests spécifiques
npm run test -- --testNamePattern="should create portfolio"

# Mode verbose
npm run test -- --verbose
```

### **Tests E2E**
```bash
# Mode debug avec interface
npm run test:e2e:ui

# Mode headed (voir le navigateur)
npm run test:e2e:headed

# Tests spécifiques
npx playwright test auth.spec.ts

# Générer des traces
npx playwright test --trace on
```

### **Outils de Debug**
- **Jest** : `console.log()`, `debugger`, VS Code debugger
- **Playwright** : Trace viewer, screenshots, vidéos
- **React Testing Library** : `screen.debug()`

---

## 📈 **Intégration CI/CD**

### **GitHub Actions**
```yaml
# .github/workflows/tests.yml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:ci
```

### **Scripts de Pré-commit**
```bash
# Installer husky pour les hooks
npm install --save-dev husky

# Configurer le hook pre-commit
npx husky add .husky/pre-commit "npm run test:unit"
```

---

## 📚 **Ressources et Formation**

### **Documentation**
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright Documentation](https://playwright.dev/docs/intro)

### **Patterns de Test**
- **Test-Driven Development (TDD)** : Écrire les tests avant le code
- **Behavior-Driven Development (BDD)** : Tests basés sur le comportement
- **Page Object Model** : Structurer les tests E2E

### **Formation Équipe**
1. **Atelier Jest & RTL** : Bases des tests unitaires
2. **Session Playwright** : Tests E2E efficaces
3. **Code Review** : Révision des tests
4. **Debugging Workshop** : Techniques de débogage

---

## 🎯 **Checklist Qualité**

### **Avant de Committer**
- [ ] Tests unitaires passent
- [ ] Couverture de code maintenue
- [ ] Tests d'intégration OK
- [ ] Pas de tests ignorés (`it.skip`)
- [ ] Noms de tests descriptifs

### **Avant le Déploiement**
- [ ] Tous les tests passent
- [ ] Tests E2E sur environnement de staging
- [ ] Performance des tests acceptable
- [ ] Pas de tests flaky (instables)

### **Maintenance Régulière**
- [ ] Révision mensuelle des tests obsolètes
- [ ] Mise à jour des dépendances de test
- [ ] Optimisation des temps d'exécution
- [ ] Documentation à jour

---

## 🆘 **Support et Dépannage**

### **Problèmes Courants**

**Tests qui échouent de manière intermittente :**
```bash
# Augmenter les timeouts
jest.setTimeout(10000)

# Attendre les éléments async
await waitFor(() => expect(element).toBeInTheDocument())
```

**Problèmes de mocks :**
```bash
# Nettoyer les mocks entre les tests
beforeEach(() => {
  jest.clearAllMocks()
})
```

**Tests E2E lents :**
```bash
# Parallélisation
npx playwright test --workers=4

# Tests ciblés
npx playwright test --grep="critical"
```

### **Aide**
- **Documentation** : Consultez les guides dans `/docs`
- **Issues** : Créez une issue pour les bugs de tests
- **Formation** : Sessions d'équipe régulières

---

*Guide des tests mis à jour - Septembre 2024*

**Objectif : QribLik 100% testé et fiable** 🎯
