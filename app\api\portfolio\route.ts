import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const createPortfolioSchema = z.object({
  title: z.string().min(1, 'Le titre est requis'),
  description: z.string().optional(),
  category: z.string().min(1, 'La catégorie est requise'),
  images: z.array(z.string()).min(1, 'Au moins une image est requise'),
  videos: z.array(z.string()).optional(),
  beforeImages: z.array(z.string()).optional(),
  afterImages: z.array(z.string()).optional(),
  projectDate: z.string().optional(),
  duration: z.string().optional(),
  clientTestimonial: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  locationAddress: z.string().optional(),
  locationCity: z.string().optional(),
  locationRegion: z.string().optional(),
});

// GET /api/portfolio - Récupérer les éléments du portfolio
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const category = searchParams.get('category');
    const isPublic = searchParams.get('public') === 'true';
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    const where: any = {};
    
    if (userId) {
      where.userId = userId;
    }
    
    if (category) {
      where.category = category;
    }
    
    if (isPublic) {
      where.isPublic = true;
    }

    const portfolioItems = await prisma.portfolioItem.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            rating: true,
            reviewCount: true,
            locationCity: true,
            locationRegion: true,
          },
        },
      },
      orderBy: [
        { isFeatured: 'desc' },
        { createdAt: 'desc' },
      ],
      take: limit,
      skip: offset,
    });

    // Transformer les données pour l'affichage
    const transformedItems = portfolioItems.map((item: any) => ({
      ...item,
      images: item.images ? item.images.split(',') : [],
      videos: item.videos ? item.videos.split(',') : [],
      beforeImages: item.beforeImages ? item.beforeImages.split(',') : [],
      afterImages: item.afterImages ? item.afterImages.split(',') : [],
      tags: item.tags ? item.tags.split(',') : [],
    }));

    return NextResponse.json({
      success: true,
      data: transformedItems,
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du portfolio:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}

// POST /api/portfolio - Créer un nouvel élément de portfolio
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = createPortfolioSchema.parse(body);

    // Vérifier que l'utilisateur est un prestataire
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { userType: true },
    });

    if (!user || (user.userType !== 'PRESTATAIRE' && user.userType !== 'BOTH')) {
      return NextResponse.json(
        { success: false, error: 'Seuls les prestataires peuvent créer un portfolio' },
        { status: 403 }
      );
    }

    const portfolioItem = await prisma.portfolioItem.create({
      data: {
        title: validatedData.title,
        description: validatedData.description,
        category: validatedData.category,
        images: validatedData.images.join(','),
        videos: validatedData.videos?.join(',') || '',
        beforeImages: validatedData.beforeImages?.join(',') || '',
        afterImages: validatedData.afterImages?.join(',') || '',
        projectDate: validatedData.projectDate ? new Date(validatedData.projectDate) : null,
        duration: validatedData.duration,
        clientTestimonial: validatedData.clientTestimonial,
        tags: validatedData.tags?.join(',') || '',
        isPublic: validatedData.isPublic,
        isFeatured: validatedData.isFeatured,
        locationAddress: validatedData.locationAddress,
        locationCity: validatedData.locationCity,
        locationRegion: validatedData.locationRegion,
        userId: session.user.id,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            rating: true,
            reviewCount: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        ...portfolioItem,
        images: portfolioItem.images ? portfolioItem.images.split(',') : [],
        videos: portfolioItem.videos ? portfolioItem.videos.split(',') : [],
        beforeImages: portfolioItem.beforeImages ? portfolioItem.beforeImages.split(',') : [],
        afterImages: portfolioItem.afterImages ? portfolioItem.afterImages.split(',') : [],
        tags: portfolioItem.tags ? portfolioItem.tags.split(',') : [],
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Données invalides', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Erreur lors de la création du portfolio:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
