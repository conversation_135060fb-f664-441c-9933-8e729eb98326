import NextAuth from "next-auth"
import Google from "next-auth/providers/google"
import Credentials from "next-auth/providers/credentials"
import { compare } from "bcryptjs"
import { config } from "./config"
import { prisma } from "./prisma"

export const { handlers, auth, signIn, signOut } = NextAuth({
  secret: config.nextAuthSecret,
  providers: [
    Google({
      clientId: config.googleClientId,
      clientSecret: config.googleClientSecret,
    }),
    Credentials({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          console.log('Missing credentials')
          return null
        }

        try {
          const user = await prisma.user.findUnique({
            where: { email: credentials.email as string },
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              password: true,
            }
          })

          if (!user) {
            return null
          }

          if (!user.password) {
            return null
          }

          const isPasswordValid = await compare(
            credentials.password as string,
            user.password
          )

          if (!isPasswordValid) {
            return null
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
          }
        } catch (error) {
          console.error('Auth error:', error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        
        // Récupérer les données utilisateur lors de la première connexion
        try {
          const dbUser = await prisma.user.findUnique({
            where: { id: user.id },
            select: {
              id: true,
              userType: true,
              locationCity: true,
              locationRegion: true,
              rating: true,
              reviewCount: true,
            }
          })
          
          if (dbUser) {
            token.userType = dbUser.userType
            token.locationCity = dbUser.locationCity
            token.locationRegion = dbUser.locationRegion
            token.rating = dbUser.rating
            token.reviewCount = dbUser.reviewCount
          }
        } catch (error) {
          console.error('Error fetching user data for JWT:', error)
        }
      }
      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        // Utiliser les données du token au lieu d'appeler Prisma
        session.user.id = token.id as string
        session.user.userType = token.userType as string
        session.user.locationCity = token.locationCity as string
        session.user.locationRegion = token.locationRegion as string
        session.user.rating = token.rating as number
        session.user.reviewCount = token.reviewCount as number
      }
      return session
    },
    async redirect({ url, baseUrl }) {
      // Permet les redirections vers des URLs relatives
      if (url.startsWith("/")) return `${baseUrl}${url}`
      // Permet les redirections vers le même domaine
      else if (new URL(url).origin === baseUrl) return url
      // Redirection par défaut vers le dashboard
      return `${baseUrl}/dashboard`
    },
  },
  pages: {
    signIn: "/auth/signin",
    newUser: "/auth/signup",
  },
})