console.log('🧪 Test Rapide des Modules\n');

// Test des imports critiques
try {
  console.log('📦 Test des modules Node.js...');
  
  // Test des modules basiques
  require('fs');
  console.log('  ✅ fs');
  
  require('path');
  console.log('  ✅ path');
  
  // Test si les modules sont installés
  const packageJson = require('../package.json');
  console.log('  ✅ package.json lu');
  
  console.log('\n📋 Modules critiques dans package.json:');
  const criticalModules = [
    'sonner',
    'next-themes', 
    'date-fns',
    'lucide-react',
    'next-auth',
    'react',
    'next'
  ];
  
  const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  criticalModules.forEach(module => {
    if (deps[module]) {
      console.log(`  ✅ ${module}@${deps[module]}`);
    } else {
      console.log(`  ❌ ${module} - MANQUANT`);
    }
  });
  
  console.log('\n🎯 Résultat: Tous les modules semblent correctement installés');
  console.log('\n🚀 Prochaines étapes:');
  console.log('  1. Assurez-vous que NEXTAUTH_SECRET est défini dans .env.local');
  console.log('  2. Redémarrez le serveur: npm run dev');
  console.log('  3. Testez les pages: http://localhost:3000/dashboard/portfolio');
  
} catch (error) {
  console.error('❌ Erreur lors du test:', error.message);
  console.log('\n🔧 Actions correctives:');
  console.log('  1. Exécuter: npm install');
  console.log('  2. Vérifier que vous êtes dans le bon répertoire');
  console.log('  3. Vérifier les permissions de fichiers');
}
