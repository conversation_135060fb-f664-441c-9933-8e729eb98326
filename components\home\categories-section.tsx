'use client'

import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { 
  <PERSON><PERSON>, 
  Sparkles, 
  TreePine, 
  GraduationCap, 
  Truck, 
  Heart, 
  PartyPopper, 
  Smartphone,
  Car,
  Home,
  MoreHorizontal,
  ArrowRight 
} from 'lucide-react'
import { motion } from 'framer-motion'

const categories = [
  {
    id: 'bricolage',
    name: 'Bricolage & Réparations',
    icon: Wrench,
    count: '15,432',
    color: 'text-blue-600 bg-blue-50',
    description: 'Plomberie, électricité, menuiserie...'
  },
  {
    id: 'menage',
    name: 'Ménage & Nettoyage',
    icon: Sparkles,
    count: '12,876',
    color: 'text-pink-600 bg-pink-50',
    description: '<PERSON>én<PERSON>, repassage, nettoyage...'
  },
  {
    id: 'jardinage',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    icon: TreePine,
    count: '8,943',
    color: 'text-green-600 bg-green-50',
    description: 'Tonte, taille, plantation...'
  },
  {
    id: 'cours',
    name: 'Cours particuliers',
    icon: GraduationCap,
    count: '11,234',
    color: 'text-purple-600 bg-purple-50',
    description: 'Soutien scolaire, langues...'
  },
  {
    id: 'demenagement',
    name: 'Déménagement',
    icon: Truck,
    count: '5,567',
    color: 'text-orange-600 bg-orange-50',
    description: 'Transport, portage, emballage...'
  },
  {
    id: 'bien-etre',
    name: 'Bien-être',
    icon: Heart,
    count: '4,321',
    color: 'text-red-600 bg-red-50',
    description: 'Massage, coiffure, esthétique...'
  },
  {
    id: 'evenements',
    name: 'Événements',
    icon: PartyPopper,
    count: '3,789',
    color: 'text-yellow-600 bg-yellow-50',
    description: 'Animation, traiteur, décoration...'
  },
  {
    id: 'informatique',
    name: 'Informatique',
    icon: Smartphone,
    count: '6,654',
    color: 'text-indigo-600 bg-indigo-50',
    description: 'Réparation, installation, formation...'
  },
  {
    id: 'automobile',
    name: 'Automobile',
    icon: Car,
    count: '4,123',
    color: 'text-gray-600 bg-gray-50',
    description: 'Réparation, entretien, lavage...'
  },
  {
    id: 'immobilier',
    name: 'Immobilier',
    icon: Home,
    count: '2,987',
    color: 'text-teal-600 bg-teal-50',
    description: 'Peinture, rénovation, décoration...'
  }
]

export function CategoriesSection() {
  return (
    <section className="py-16 lg:py-24 bg-muted/30">
      <div className="container">
        <motion.div 
          className="text-center space-y-4 mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl font-bold">
            Tous les services dont vous avez besoin
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Des milliers de prestataires qualifiés dans toutes les catégories
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-8">
          {categories.map((category, index) => {
            const Icon = category.icon
            return (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                viewport={{ once: true }}
              >
                <Link
                  href={`/services/${category.id}`}
                  className="group block p-6 bg-card rounded-xl border border-border hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                >
                  <div className="text-center space-y-3">
                    <div className={`w-12 h-12 ${category.color} rounded-xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-sm leading-tight mb-1">
                        {category.name}
                      </h3>
                      <p className="text-xs text-muted-foreground mb-2">
                        {category.description}
                      </p>
                      <span className="text-xs font-medium text-primary">
                        {category.count} services
                      </span>
                    </div>
                  </div>
                </Link>
              </motion.div>
            )
          })}
        </div>

        <motion.div 
          className="text-center"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          viewport={{ once: true }}
        >
          <Button variant="outline" size="lg" asChild>
            <Link href="/services" className="inline-flex items-center">
              Voir toutes les catégories
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  )
}