import { test, expect } from '@playwright/test';

test.describe('Portfolio Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login as prestataire before each test
    await page.goto('/auth/signin');
    await page.getByLabel('Email').fill('<EMAIL>');
    await page.getByLabel('Mot de passe').fill('password123');
    await page.getByRole('button', { name: 'Se connecter' }).click();
    await expect(page).toHaveURL('/dashboard');
  });

  test('should display portfolio page with existing items', async ({ page }) => {
    await page.goto('/dashboard/portfolio');
    
    // Check page title and description
    await expect(page.getByText('Mon Portfolio')).toBeVisible();
    await expect(page.getByText('Gérez et mettez en valeur vos réalisations')).toBeVisible();
    
    // Should show portfolio items if any exist
    const portfolioGrid = page.locator('[data-testid="portfolio-grid"]');
    await expect(portfolioGrid).toBeVisible();
  });

  test('should create new portfolio item', async ({ page }) => {
    await page.goto('/dashboard/portfolio');
    
    // Click add new project button
    await page.getByRole('button', { name: 'Ajouter un projet' }).click();
    
    // Modal should open
    await expect(page.getByText('Nouveau Projet')).toBeVisible();
    
    // Fill the form
    await page.getByLabel('Titre du projet').fill('Test Rénovation Cuisine');
    await page.getByLabel('Description').fill('Rénovation complète d\'une cuisine moderne avec îlot central');
    
    // Select category
    await page.getByRole('combobox', { name: 'Catégorie' }).click();
    await page.getByRole('option', { name: 'Bricolage & Réparations' }).click();
    
    // Add tags
    await page.getByLabel('Tags').fill('rénovation, cuisine, moderne');
    
    // Set project date
    await page.getByLabel('Date du projet').fill('2024-08-15');
    
    // Set duration
    await page.getByLabel('Durée').fill('2 semaines');
    
    // Add client testimonial
    await page.getByLabel('Témoignage client').fill('Excellent travail, très professionnel !');
    
    // Set visibility to public
    await page.getByRole('switch', { name: 'Projet public' }).check();
    
    // Submit form
    await page.getByRole('button', { name: 'Créer le projet' }).click();
    
    // Should show success message
    await expect(page.getByText('Projet créé avec succès')).toBeVisible();
    
    // Should close modal and show new item in grid
    await expect(page.getByText('Nouveau Projet')).not.toBeVisible();
    await expect(page.getByText('Test Rénovation Cuisine')).toBeVisible();
  });

  test('should edit existing portfolio item', async ({ page }) => {
    await page.goto('/dashboard/portfolio');
    
    // Find first portfolio item and click edit
    const firstItem = page.locator('[data-testid="portfolio-item"]').first();
    await firstItem.hover();
    await firstItem.getByRole('button', { name: 'Modifier' }).click();
    
    // Modal should open with existing data
    await expect(page.getByText('Modifier le Projet')).toBeVisible();
    
    // Update title
    const titleInput = page.getByLabel('Titre du projet');
    await titleInput.clear();
    await titleInput.fill('Titre Modifié');
    
    // Update description
    const descInput = page.getByLabel('Description');
    await descInput.clear();
    await descInput.fill('Description mise à jour');
    
    // Save changes
    await page.getByRole('button', { name: 'Sauvegarder' }).click();
    
    // Should show success message
    await expect(page.getByText('Projet mis à jour avec succès')).toBeVisible();
    
    // Should show updated content
    await expect(page.getByText('Titre Modifié')).toBeVisible();
  });

  test('should delete portfolio item', async ({ page }) => {
    await page.goto('/dashboard/portfolio');
    
    // Find first portfolio item and click delete
    const firstItem = page.locator('[data-testid="portfolio-item"]').first();
    const itemTitle = await firstItem.getByRole('heading').textContent();
    
    await firstItem.hover();
    await firstItem.getByRole('button', { name: 'Supprimer' }).click();
    
    // Confirmation dialog should appear
    await expect(page.getByText('Confirmer la suppression')).toBeVisible();
    await expect(page.getByText('Cette action est irréversible')).toBeVisible();
    
    // Confirm deletion
    await page.getByRole('button', { name: 'Supprimer définitivement' }).click();
    
    // Should show success message
    await expect(page.getByText('Projet supprimé avec succès')).toBeVisible();
    
    // Item should no longer be visible
    if (itemTitle) {
      await expect(page.getByText(itemTitle)).not.toBeVisible();
    }
  });

  test('should filter portfolio by category', async ({ page }) => {
    await page.goto('/dashboard/portfolio');
    
    // Wait for items to load
    await page.waitForSelector('[data-testid="portfolio-item"]');
    
    // Get initial count of items
    const initialItems = await page.locator('[data-testid="portfolio-item"]').count();
    
    // Apply category filter
    await page.getByRole('combobox', { name: 'Catégorie' }).click();
    await page.getByRole('option', { name: 'Bricolage & Réparations' }).click();
    
    // Wait for filter to apply
    await page.waitForTimeout(1000);
    
    // Should show filtered results
    const filteredItems = await page.locator('[data-testid="portfolio-item"]').count();
    
    // Verify filtering worked (could be same or less items)
    expect(filteredItems).toBeLessThanOrEqual(initialItems);
  });

  test('should toggle item visibility', async ({ page }) => {
    await page.goto('/dashboard/portfolio');
    
    // Find first portfolio item
    const firstItem = page.locator('[data-testid="portfolio-item"]').first();
    await firstItem.hover();
    
    // Click visibility toggle
    const visibilityButton = firstItem.getByRole('button', { name: /Visibilité/ });
    await visibilityButton.click();
    
    // Should show confirmation or immediate change
    // This depends on implementation - might be immediate or require confirmation
    await page.waitForTimeout(500);
  });

  test('should share portfolio item', async ({ page }) => {
    await page.goto('/dashboard/portfolio');
    
    // Find first portfolio item and click share
    const firstItem = page.locator('[data-testid="portfolio-item"]').first();
    await firstItem.hover();
    await firstItem.getByRole('button', { name: 'Partager' }).click();
    
    // Share modal should open
    await expect(page.getByText('Partager ce projet')).toBeVisible();
    
    // Should show social media options
    await expect(page.getByRole('button', { name: 'Facebook' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Twitter' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'LinkedIn' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'WhatsApp' })).toBeVisible();
    
    // Test copy link functionality
    await page.getByRole('button', { name: 'Copier le lien' }).click();
    await expect(page.getByText('Lien copié')).toBeVisible();
  });

  test('should handle empty portfolio state', async ({ page }) => {
    // This test assumes we can create a user with no portfolio items
    // or we clear existing items first
    
    await page.goto('/dashboard/portfolio');
    
    // Should show empty state
    await expect(page.getByText('Aucun projet dans votre portfolio')).toBeVisible();
    await expect(page.getByText('Commencez par ajouter votre premier projet')).toBeVisible();
    
    // Should show call-to-action button
    await expect(page.getByRole('button', { name: 'Ajouter un projet' })).toBeVisible();
  });

  test('should validate form inputs', async ({ page }) => {
    await page.goto('/dashboard/portfolio');
    
    // Open create modal
    await page.getByRole('button', { name: 'Ajouter un projet' }).click();
    
    // Try to submit empty form
    await page.getByRole('button', { name: 'Créer le projet' }).click();
    
    // Should show validation errors
    await expect(page.getByText('Le titre est requis')).toBeVisible();
    await expect(page.getByText('La catégorie est requise')).toBeVisible();
    
    // Fill title with too short text
    await page.getByLabel('Titre du projet').fill('Ab');
    await page.getByRole('button', { name: 'Créer le projet' }).click();
    
    await expect(page.getByText('Le titre doit contenir au moins 3 caractères')).toBeVisible();
  });

  test('should handle image upload', async ({ page }) => {
    await page.goto('/dashboard/portfolio');
    
    // Open create modal
    await page.getByRole('button', { name: 'Ajouter un projet' }).click();
    
    // Find file upload area
    const uploadArea = page.getByText('Glissez vos images ici ou cliquez pour sélectionner');
    await expect(uploadArea).toBeVisible();
    
    // Note: File upload testing in Playwright requires actual files
    // In a real test, you would use page.setInputFiles()
    // For now, we just verify the upload area is present
  });

  test('should display portfolio statistics', async ({ page }) => {
    await page.goto('/dashboard/portfolio');
    
    // Should show portfolio stats
    await expect(page.getByText('Statistiques du portfolio')).toBeVisible();
    
    // Should show various metrics
    const statsSection = page.locator('[data-testid="portfolio-stats"]');
    await expect(statsSection.getByText(/Total des vues/)).toBeVisible();
    await expect(statsSection.getByText(/Total des likes/)).toBeVisible();
    await expect(statsSection.getByText(/Total des partages/)).toBeVisible();
  });

  test('should work on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/dashboard/portfolio');
    
    // Should be responsive
    await expect(page.getByText('Mon Portfolio')).toBeVisible();
    
    // Grid should adapt to mobile
    const portfolioGrid = page.locator('[data-testid="portfolio-grid"]');
    await expect(portfolioGrid).toBeVisible();
    
    // Mobile menu should work
    const mobileMenuButton = page.getByRole('button', { name: 'Menu' });
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click();
      await expect(page.getByText('Portfolio')).toBeVisible();
    }
  });
});
