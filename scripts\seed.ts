import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const categories = [
  'BRICOLAGE_REPARATIONS',
  'MENAGE_NETTOYAGE', 
  'JARDINAGE_ESPACES_VERTS',
  'DEMENAGEMENT_TRANSPORT',
  'COURS_PARTICULIERS',
  'SERVICES_PERSONNE',
  'EVENEMENTS_ANIMATION',
  'BEAUTE_BIEN_ETRE',
  'INFORMATIQUE_TECH',
  'AUTOMOBILE',
  'IMMOBILIER',
  'AUTRES'
]

const cities = [
  'Casablanca', 'Rabat', 'Marrakech', 'Fès', 'Agadir', 'Tanger', 'Mekn<PERSON>', '<PERSON>ujda', '<PERSON><PERSON><PERSON>ra', 'Té<PERSON>uan'
]

const sampleUsers = [
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+212 6 12 34 56 78',
    userType: 'PRESTATAIRE',
    bio: 'Plombier expérimenté avec 10 ans d\'expérience. Spécialisé dans les réparations urgentes.',
    locationCity: 'Casablanca',
    locationRegion: 'Casablanca-Settat',
    locationLat: 33.5731,
    locationLng: -7.5898,
    rating: 4.8,
    reviewCount: 45,
    responseTime: 30,
    completionRate: 95
  },
  {
    name: 'Fatima Zahra',
    email: '<EMAIL>', 
    phone: '+212 6 23 45 67 89',
    userType: 'PRESTATAIRE',
    bio: 'Femme de ménage professionnelle. Service de qualité et ponctualité garanties.',
    locationCity: 'Rabat',
    locationRegion: 'Rabat-Salé-Kénitra',
    locationLat: 34.0209,
    locationLng: -6.8416,
    rating: 4.9,
    reviewCount: 32,
    responseTime: 15,
    completionRate: 98
  },
  {
    name: 'Youssef Alami',
    email: '<EMAIL>',
    phone: '+212 6 34 56 78 90',
    userType: 'BOTH',
    bio: 'Jardinier et cours d\'anglais. Polyvalent et disponible.',
    locationCity: 'Marrakech',
    locationRegion: 'Marrakech-Safi',
    locationLat: 31.6295,
    locationLng: -7.9811,
    rating: 4.7,
    reviewCount: 28,
    responseTime: 45,
    completionRate: 92
  },
  {
    name: 'Aicha Mansouri',
    email: '<EMAIL>',
    phone: '+212 6 45 67 89 01',
    userType: 'CLIENT',
    bio: 'Recherche des services de qualité pour ma famille.',
    locationCity: 'Fès',
    locationRegion: 'Fès-Meknès',
    locationLat: 34.0331,
    locationLng: -5.0003,
    rating: 4.5,
    reviewCount: 12,
    responseTime: 60,
    completionRate: 88
  }
]

const sampleServices = [
  {
    title: 'Réparation plomberie urgente',
    description: 'Service de réparation plomberie 24h/24. Fuites, robinets, chauffe-eau, canalisations. Intervention rapide dans tout Casablanca.',
    shortDesc: 'Plombier urgent 24h/24',
    category: 'BRICOLAGE_REPARATIONS',
    subCategory: 'Plomberie',
    tags: 'plomberie,urgent,réparation,fuite,robinet',
    price: 200,
    priceType: 'FIXED',
    images: 'https://images.pexels.com/photos/162553/keys-workshop-mechanic-tools-162553.jpeg,https://images.pexels.com/photos/162553/keys-workshop-mechanic-tools-162553.jpeg',
    isRemote: false,
    requirements: 'Photos du problème, adresse exacte',
    duration: '1-3 heures',
    deliverables: 'Réparation terminée,test de fonctionnement,facture détaillée',
    locationCity: 'Casablanca',
    locationRegion: 'Casablanca-Settat',
    locationLat: 33.5731,
    locationLng: -7.5898,
    status: 'ACTIVE'
  },
  {
    title: 'Femme de ménage professionnelle',
    description: 'Service de ménage complet : nettoyage, repassage, organisation. Personnel qualifié et de confiance.',
    shortDesc: 'Ménage complet et repassage',
    category: 'MENAGE_NETTOYAGE',
    subCategory: 'Ménage',
    tags: 'ménage,nettoyage,repassage,organisation',
    price: 150,
    priceType: 'HOURLY',
    images: 'https://images.pexels.com/photos/4099237/pexels-photo-4099237.jpeg,https://images.pexels.com/photos/4099237/pexels-photo-4099237.jpeg',
    isRemote: false,
    requirements: 'Accès au logement, produits de nettoyage fournis',
    duration: '2-4 heures',
    deliverables: 'Nettoyage complet,repassage,organisation',
    locationCity: 'Rabat',
    locationRegion: 'Rabat-Salé-Kénitra',
    locationLat: 34.0209,
    locationLng: -6.8416,
    status: 'ACTIVE'
  },
  {
    title: 'Cours d\'anglais à domicile',
    description: 'Cours d\'anglais personnalisés pour tous niveaux. Professeur expérimenté avec méthodes modernes.',
    shortDesc: 'Cours d\'anglais personnalisés',
    category: 'COURS_PARTICULIERS',
    subCategory: 'Langues',
    tags: 'anglais,cours,éducation,langue,personnalisé',
    price: 100,
    priceType: 'HOURLY',
    images: 'https://images.pexels.com/photos/5212317/pexels-photo-5212317.jpeg,https://images.pexels.com/photos/5212317/pexels-photo-5212317.jpeg',
    isRemote: true,
    requirements: 'Niveau actuel, objectifs d\'apprentissage',
    duration: '1-2 heures par session',
    deliverables: 'Cours personnalisé,exercices,évaluation',
    locationCity: 'Marrakech',
    locationRegion: 'Marrakech-Safi',
    locationLat: 31.6295,
    locationLng: -7.9811,
    status: 'ACTIVE'
  },
  {
    title: 'Jardinage et entretien espaces verts',
    description: 'Service complet de jardinage : tonte, taille, plantation, entretien. Création et maintenance de jardins.',
    shortDesc: 'Jardinage et entretien complet',
    category: 'JARDINAGE_ESPACES_VERTS',
    subCategory: 'Jardinage',
    tags: 'jardinage,tonte,taille,plantation,entretien',
    price: 120,
    priceType: 'HOURLY',
    images: 'https://images.pexels.com/photos/1072824/pexels-photo-1072824.jpeg,https://images.pexels.com/photos/1072824/pexels-photo-1072824.jpeg',
    isRemote: false,
    requirements: 'Accès au jardin, outils fournis',
    duration: '2-6 heures',
    deliverables: 'Tonte effectuée,taille réalisée,plantation terminée',
    locationCity: 'Fès',
    locationRegion: 'Fès-Meknès',
    locationLat: 34.0331,
    locationLng: -5.0003,
    status: 'ACTIVE'
  }
]

const sampleRequests = [
  {
    title: 'Besoin d\'un plombier pour réparer une fuite',
    description: 'Fuite d\'eau importante sous l\'évier de la cuisine. Besoin d\'intervention rapide.',
    category: 'BRICOLAGE_REPARATIONS',
    subCategory: 'Plomberie',
    tags: 'plomberie,fuite,urgent,évier',
    budget: 300,
    budgetType: 'FIXED',
    urgency: 'HIGH',
    images: 'https://images.pexels.com/photos/162553/keys-workshop-mechanic-tools-162553.jpeg',
    attachments: '',
    isPublic: true,
    locationCity: 'Casablanca',
    locationRegion: 'Casablanca-Settat',
    locationLat: 33.5731,
    locationLng: -7.5898,
    status: 'OPEN'
  },
  {
    title: 'Recherche femme de ménage pour maison',
    description: 'Ménage hebdomadaire pour maison de 4 pièces. Recherche personne fiable et ponctuelle.',
    category: 'MENAGE_NETTOYAGE',
    subCategory: 'Ménage',
    tags: 'ménage,hebdomadaire,maison,fiable',
    budget: 200,
    budgetType: 'HOURLY',
    urgency: 'NORMAL',
    images: 'https://images.pexels.com/photos/4099237/pexels-photo-4099237.jpeg',
    attachments: '',
    isPublic: true,
    locationCity: 'Rabat',
    locationRegion: 'Rabat-Salé-Kénitra',
    locationLat: 34.0209,
    locationLng: -6.8416,
    status: 'OPEN'
  }
]

async function main() {
  console.log('🌱 Seeding database...')

  // Clear existing data
  await prisma.review.deleteMany()
  await prisma.match.deleteMany()
  await prisma.conversation.deleteMany()
  await prisma.message.deleteMany()
  await prisma.notification.deleteMany()
  await prisma.service.deleteMany()
  await prisma.request.deleteMany()
  await prisma.user.deleteMany()

  // Create users
  const users = []
  for (const userData of sampleUsers) {
    const user = await prisma.user.create({
      data: userData
    })
    users.push(user)
    console.log(`✅ Created user: ${user.name}`)
  }

  // Create services
  const services = []
  for (let i = 0; i < sampleServices.length; i++) {
    const serviceData = {
      ...sampleServices[i],
      userId: users[i % users.length].id
    }
    const service = await prisma.service.create({
      data: serviceData
    })
    services.push(service)
    console.log(`✅ Created service: ${service.title}`)
  }

  // Create requests
  const requests = []
  for (let i = 0; i < sampleRequests.length; i++) {
    const requestData = {
      ...sampleRequests[i],
      clientId: users[users.length - 1].id // Use last user as client
    }
    const request = await prisma.request.create({
      data: requestData
    })
    requests.push(request)
    console.log(`✅ Created request: ${request.title}`)
  }

  // Create some matches
  if (services.length > 0 && requests.length > 0) {
    const match = await prisma.match.create({
      data: {
        requestId: requests[0].id,
        serviceId: services[0].id,
        prestataireId: services[0].userId,
        clientId: requests[0].clientId,
        message: 'Bonjour, je peux intervenir rapidement pour réparer votre fuite.',
        proposedPrice: 250,
        proposedDelay: 'Dans les 2 heures',
        status: 'PENDING'
      }
    })
    console.log(`✅ Created match: ${match.id}`)
  }

  console.log('🎉 Database seeded successfully!')
  console.log(`📊 Created:`)
  console.log(`   - ${users.length} users`)
  console.log(`   - ${services.length} services`)
  console.log(`   - ${requests.length} requests`)
  console.log(`   - 1 match`)
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
