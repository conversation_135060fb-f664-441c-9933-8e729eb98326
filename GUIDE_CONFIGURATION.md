# 🔧 Guide de Configuration QribLik

## 📋 **Étapes de Configuration**

### **1. Base de Données**

```bash
# Générer le client Prisma
npx prisma generate

# Appliquer les nouveaux modèles
npx prisma db push

# Vérifier avec Prisma Studio
npx prisma studio
```

### **2. Variables d'Environnement**

Copiez `.env.example` vers `.env.local` et configurez :

```bash
cp .env.example .env.local
```

#### **🗺️ APIs de Géolocalisation (Recommandées)**

```env
# Google Maps (pour la carte des demandes)
GOOGLE_MAPS_API_KEY="your-google-maps-api-key"
GOOGLE_PLACES_API_KEY="your-google-places-api-key"
```

**Comment obtenir les clés Google Maps :**
1. Aller sur [Google Cloud Console](https://console.cloud.google.com/)
2. Créer un nouveau projet ou sélectionner un existant
3. Activer les APIs : Maps JavaScript API, Places API, Geocoding API
4. Créer des clés API dans "Credentials"
5. Restreindre les clés par domaine pour la sécurité

#### **📁 Upload d'Images (Recommandé)**

```env
# UploadThing (pour le portfolio)
UPLOADTHING_SECRET="your-uploadthing-secret"
UPLOADTHING_APP_ID="your-uploadthing-app-id"
```

**Comment configurer UploadThing :**
1. Aller sur [UploadThing.com](https://uploadthing.com)
2. Créer un compte et un nouveau projet
3. Copier les clés API et App ID
4. Configurer les restrictions de fichiers (images, 10MB max)

### **3. Test des Nouvelles Fonctionnalités**

#### **🎨 Portfolio**
```bash
# Démarrer le serveur de développement
npm run dev

# Tester les endpoints
curl -X GET http://localhost:3000/api/portfolio
```

**Pages à tester :**
- `/dashboard/portfolio` - Interface portfolio
- `/dashboard/agenda` - Planning artisan
- `/dashboard/map` - Carte des demandes

#### **📱 Fonctionnalités à Valider**
- [ ] Création d'éléments de portfolio
- [ ] Upload d'images (si UploadThing configuré)
- [ ] Gestion des créneaux d'agenda
- [ ] Visualisation de la carte des demandes
- [ ] Partage sur réseaux sociaux
- [ ] Centre de notifications

### **4. Données de Test**

Créons un script pour ajouter des données de test :

```bash
# Créer des données de test
npx tsx scripts/seed-new-features.ts
```

### **5. Vérification de Production**

#### **🔒 Sécurité**
- [ ] Variables d'environnement sécurisées
- [ ] Clés API restreintes par domaine
- [ ] HTTPS activé
- [ ] Rate limiting sur les APIs

#### **📊 Performance**
- [ ] Images optimisées et compressées
- [ ] Cache des données de géolocalisation
- [ ] Lazy loading des composants lourds
- [ ] Monitoring des erreurs

#### **🌐 SEO & Accessibilité**
- [ ] Métadonnées des nouvelles pages
- [ ] Alt text sur les images
- [ ] Navigation clavier
- [ ] Contrastes de couleurs

## 🚨 **Problèmes Courants et Solutions**

### **Erreur : "Cannot find module"**
```bash
# Installer les dépendances manquantes
npm install date-fns sonner
```

### **Erreur Prisma : "Schema drift detected"**
```bash
# Réinitialiser et repousser le schéma
npx prisma db push --force-reset
```

### **Erreur Upload : "UploadThing not configured"**
```bash
# Vérifier les variables d'environnement
echo $UPLOADTHING_SECRET
echo $UPLOADTHING_APP_ID
```

### **Erreur Maps : "API key not valid"**
- Vérifier que les APIs sont activées dans Google Cloud
- Vérifier les restrictions de domaine
- Vérifier la facturation activée

## 🎯 **Checklist de Déploiement**

### **Pré-déploiement**
- [ ] Tests unitaires passent
- [ ] Base de données migrée
- [ ] Variables d'environnement configurées
- [ ] Build de production réussi

### **Déploiement**
- [ ] Backup de la base de données
- [ ] Déploiement du code
- [ ] Migration de la base de données
- [ ] Vérification des endpoints

### **Post-déploiement**
- [ ] Tests de fumée sur les nouvelles fonctionnalités
- [ ] Monitoring des erreurs
- [ ] Performance des nouvelles pages
- [ ] Feedback utilisateurs

## 📞 **Support**

En cas de problème :
1. Vérifier les logs de l'application
2. Tester en mode développement
3. Vérifier la configuration des APIs
4. Consulter la documentation Prisma/NextJS

---

*Guide mis à jour : Septembre 2024*
