'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, Clock, Database, Users, ShoppingCart, MessageSquare } from 'lucide-react'
import Link from 'next/link'

interface TestResult {
  name: string
  status: 'success' | 'error' | 'pending'
  message: string
  endpoint?: string
}

export default function TestPage() {
  const [tests, setTests] = useState<TestResult[]>([])
  const [running, setRunning] = useState(false)

  const runTests = async () => {
    setRunning(true)
    setTests([])

    const testResults: TestResult[] = []

    // Test 1: Database Connection
    testResults.push({
      name: 'Connexion à la base de données',
      status: 'pending',
      message: 'Test en cours...'
    })
    setTests([...testResults])

    try {
      const dbResponse = await fetch('/api/test/db')
      testResults[0] = {
        name: 'Connexion à la base de données',
        status: dbResponse.ok ? 'success' : 'error',
        message: dbResponse.ok ? 'Base de données accessible' : 'Erreur de connexion',
        endpoint: '/api/test/db'
      }
    } catch (error) {
      testResults[0] = {
        name: 'Connexion à la base de données',
        status: 'error',
        message: 'Erreur de connexion à la base de données'
      }
    }
    setTests([...testResults])

    // Test 2: Services API
    testResults.push({
      name: 'API Services',
      status: 'pending',
      message: 'Test en cours...'
    })
    setTests([...testResults])

    try {
      const servicesResponse = await fetch('/api/services')
      testResults[1] = {
        name: 'API Services',
        status: servicesResponse.ok ? 'success' : 'error',
        message: servicesResponse.ok ? 'API Services fonctionnelle' : 'Erreur API Services',
        endpoint: '/api/services'
      }
    } catch (error) {
      testResults[1] = {
        name: 'API Services',
        status: 'error',
        message: 'Erreur API Services'
      }
    }
    setTests([...testResults])

    // Test 3: Requests API
    testResults.push({
      name: 'API Requests',
      status: 'pending',
      message: 'Test en cours...'
    })
    setTests([...testResults])

    try {
      const requestsResponse = await fetch('/api/requests')
      testResults[2] = {
        name: 'API Requests',
        status: requestsResponse.ok ? 'success' : 'error',
        message: requestsResponse.ok ? 'API Requests fonctionnelle' : 'Erreur API Requests',
        endpoint: '/api/requests'
      }
    } catch (error) {
      testResults[2] = {
        name: 'API Requests',
        status: 'error',
        message: 'Erreur API Requests'
      }
    }
    setTests([...testResults])

    // Test 4: Authentication
    testResults.push({
      name: 'Authentification NextAuth',
      status: 'pending',
      message: 'Test en cours...'
    })
    setTests([...testResults])

    try {
      const authResponse = await fetch('/api/auth/session')
      testResults[3] = {
        name: 'Authentification NextAuth',
        status: authResponse.ok ? 'success' : 'error',
        message: authResponse.ok ? 'NextAuth configuré' : 'Erreur NextAuth',
        endpoint: '/api/auth/session'
      }
    } catch (error) {
      testResults[3] = {
        name: 'Authentification NextAuth',
        status: 'error',
        message: 'Erreur NextAuth'
      }
    }
    setTests([...testResults])

    // Test 5: Pages
    testResults.push({
      name: 'Pages principales',
      status: 'pending',
      message: 'Test en cours...'
    })
    setTests([...testResults])

    try {
      const pagesResponse = await fetch('/')
      testResults[4] = {
        name: 'Pages principales',
        status: pagesResponse.ok ? 'success' : 'error',
        message: pagesResponse.ok ? 'Pages accessibles' : 'Erreur pages',
        endpoint: '/'
      }
    } catch (error) {
      testResults[4] = {
        name: 'Pages principales',
        status: 'error',
        message: 'Erreur pages'
      }
    }
    setTests([...testResults])

    setRunning(false)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500 animate-spin" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-50 border-green-200'
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'pending':
        return 'bg-yellow-50 border-yellow-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  const successCount = tests.filter(t => t.status === 'success').length
  const errorCount = tests.filter(t => t.status === 'error').length
  const pendingCount = tests.filter(t => t.status === 'pending').length

  return (
    <div className="min-h-screen bg-muted/30">
      <Header />
      
      <main className="container py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-4xl font-bold mb-4">Tests de l'application QribLik</h1>
            <p className="text-xl text-muted-foreground">
              Vérification du bon fonctionnement de toutes les fonctionnalités
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Database className="h-8 w-8 text-blue-500" />
                  <div>
                    <p className="text-2xl font-bold">{successCount + errorCount + pendingCount}</p>
                    <p className="text-sm text-muted-foreground">Tests total</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-8 w-8 text-green-500" />
                  <div>
                    <p className="text-2xl font-bold text-green-600">{successCount}</p>
                    <p className="text-sm text-muted-foreground">Réussis</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <XCircle className="h-8 w-8 text-red-500" />
                  <div>
                    <p className="text-2xl font-bold text-red-600">{errorCount}</p>
                    <p className="text-sm text-muted-foreground">Échecs</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Clock className="h-8 w-8 text-yellow-500" />
                  <div>
                    <p className="text-2xl font-bold text-yellow-600">{pendingCount}</p>
                    <p className="text-sm text-muted-foreground">En cours</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Test Button */}
          <div className="mb-8">
            <Button 
              onClick={runTests} 
              disabled={running}
              size="lg"
              className="w-full md:w-auto"
            >
              {running ? (
                <>
                  <Clock className="mr-2 h-4 w-4 animate-spin" />
                  Tests en cours...
                </>
              ) : (
                <>
                  <Database className="mr-2 h-4 w-4" />
                  Lancer les tests
                </>
              )}
            </Button>
          </div>

          {/* Test Results */}
          <div className="space-y-4">
            {tests.map((test, index) => (
              <Card key={index} className={getStatusColor(test.status)}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(test.status)}
                      <div>
                        <h3 className="font-semibold">{test.name}</h3>
                        <p className="text-sm text-muted-foreground">{test.message}</p>
                        {test.endpoint && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Endpoint: {test.endpoint}
                          </p>
                        )}
                      </div>
                    </div>
                    <Badge variant={test.status === 'success' ? 'default' : test.status === 'error' ? 'destructive' : 'secondary'}>
                      {test.status === 'success' ? 'Réussi' : test.status === 'error' ? 'Échec' : 'En cours'}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Quick Links */}
          <div className="mt-12">
            <h2 className="text-2xl font-bold mb-6">Liens rapides</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button asChild variant="outline" className="h-20 flex-col">
                <Link href="/">
                  <Users className="h-6 w-6 mb-2" />
                  Accueil
                </Link>
              </Button>
              
              <Button asChild variant="outline" className="h-20 flex-col">
                <Link href="/services">
                  <ShoppingCart className="h-6 w-6 mb-2" />
                  Services
                </Link>
              </Button>
              
              <Button asChild variant="outline" className="h-20 flex-col">
                <Link href="/requests">
                  <MessageSquare className="h-6 w-6 mb-2" />
                  Demandes
                </Link>
              </Button>
              
              <Button asChild variant="outline" className="h-20 flex-col">
                <Link href="/dashboard">
                  <Database className="h-6 w-6 mb-2" />
                  Dashboard
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
