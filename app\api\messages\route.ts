import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createMessageSchema = z.object({
  conversationId: z.string(),
  content: z.string().min(1, 'Le message ne peut pas être vide'),
  messageType: z.enum(['TEXT', 'IMAGE', 'FILE', 'LOCATION']).default('TEXT'),
  attachments: z.array(z.string()).default([]),
})

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const conversationId = searchParams.get('conversationId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')

    if (!conversationId) {
      return NextResponse.json({ error: 'ID de conversation requis' }, { status: 400 })
    }

    // Vérifier que l'utilisateur fait partie de la conversation
    const participant = await prisma.conversationParticipant.findFirst({
      where: {
        conversationId,
        userId: session.user.id
      }
    })

    if (!participant) {
      return NextResponse.json({ error: 'Accès refusé à cette conversation' }, { status: 403 })
    }

    // Récupérer les messages
    const messages = await prisma.message.findMany({
      where: {
        conversationId,
        isDeleted: false
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    })

    // Marquer les messages comme lus
    await prisma.message.updateMany({
      where: {
        conversationId,
        senderId: { not: session.user.id },
        readAt: null
      },
      data: {
        readAt: new Date(),
        readBy: session.user.id
      }
    })

    return NextResponse.json({
      messages: messages.reverse(), // Inverser pour avoir les plus anciens en premier
      pagination: {
        page,
        limit,
        hasMore: messages.length === limit
      }
    })

  } catch (error) {
    console.error('Erreur lors de la récupération des messages:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createMessageSchema.parse(body)

    // Vérifier que l'utilisateur fait partie de la conversation
    const participant = await prisma.conversationParticipant.findFirst({
      where: {
        conversationId: validatedData.conversationId,
        userId: session.user.id
      }
    })

    if (!participant) {
      return NextResponse.json({ error: 'Accès refusé à cette conversation' }, { status: 403 })
    }

    // Créer le message
    const message = await prisma.message.create({
      data: {
        content: validatedData.content,
        messageType: validatedData.messageType,
        attachments: validatedData.attachments.join(','),
        conversationId: validatedData.conversationId,
        senderId: session.user.id,
        readBy: session.user.id
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    // Mettre à jour la conversation avec le dernier message
    await prisma.conversation.update({
      where: { id: validatedData.conversationId },
      data: {
        lastMessage: validatedData.content,
        lastMessageAt: new Date()
      }
    })

    return NextResponse.json({ message })

  } catch (error) {
    console.error('Erreur lors de la création du message:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Données invalides', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
