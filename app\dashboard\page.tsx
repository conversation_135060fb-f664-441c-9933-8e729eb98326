'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  MessageSquare, 
  Star, 
  Calendar,
  TrendingUp,
  Clock,
  CheckCircle,
  Plus,
  Eye,
  Heart
} from 'lucide-react'
import { motion } from 'framer-motion'

const statsData = [
  {
    id: 1,
    title: 'Services actifs',
    value: '12',
    icon: CheckCircle,
    color: 'text-green-600 bg-green-50',
    trend: '+2'
  },
  {
    id: 2,
    title: 'Demandes reçues',
    value: '48',
    icon: MessageSquare,
    color: 'text-blue-600 bg-blue-50',
    trend: '+8'
  },
  {
    id: 3,
    title: 'Note moyenne',
    value: '4.8',
    icon: Star,
    color: 'text-yellow-600 bg-yellow-50',
    trend: '+0.2'
  },
  {
    id: 4,
    title: 'Revenus du mois',
    value: '2,450 MAD',
    icon: TrendingUp,
    color: 'text-purple-600 bg-purple-50',
    trend: '+15%'
  }
]

const recentActivity = [
  {
    id: 1,
    type: 'new_request',
    title: 'Nouvelle demande pour "Réparation plomberie"',
    time: 'Il y a 2 heures',
    action: 'Répondre'
  },
  {
    id: 2,
    type: 'message',
    title: 'Message de Ahmed B. pour votre service de jardinage',
    time: 'Il y a 4 heures',
    action: 'Voir'
  },
  {
    id: 3,
    type: 'review',
    title: 'Nouvel avis 5⭐ de Fatima Z.',
    time: 'Il y a 1 jour',
    action: 'Voir'
  },
  {
    id: 4,
    type: 'booking',
    title: 'Service "Cours d\'anglais" réservé pour demain',
    time: 'Il y a 2 jours',
    action: 'Confirmer'
  }
]

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-muted/30">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Dashboard</h1>
              <p className="text-muted-foreground">
                Gérez vos services et suivez votre activité
              </p>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline">
                <Calendar className="mr-2 h-4 w-4" />
                Planning
              </Button>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Nouveau service
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container py-8">
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {statsData.map((stat, index) => {
              const Icon = stat.icon
              return (
                <motion.div
                  key={stat.id}
                  className="bg-card p-6 rounded-xl border border-border shadow-sm hover:shadow-md transition-shadow"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 ${stat.color} rounded-xl flex items-center justify-center`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <span className="text-sm font-medium text-green-600">
                      {stat.trend}
                    </span>
                  </div>
                  <div>
                    <div className="text-2xl font-bold mb-1">
                      {stat.value}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {stat.title}
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Recent Activity */}
            <motion.div 
              className="lg:col-span-2"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              <div className="bg-card rounded-xl border border-border shadow-sm">
                <div className="p-6 border-b border-border">
                  <h2 className="text-xl font-semibold">Activité récente</h2>
                </div>
                <div className="divide-y divide-border">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="p-6 hover:bg-muted/50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="font-medium mb-1">
                            {activity.title}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {activity.time}
                          </p>
                        </div>
                        <Button variant="outline" size="sm">
                          {activity.action}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="p-6 border-t border-border">
                  <Button variant="ghost" className="w-full">
                    Voir toute l'activité
                  </Button>
                </div>
              </div>
            </motion.div>

            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
            >
              <div className="space-y-6">
                {/* Quick Actions Card */}
                <div className="bg-card rounded-xl border border-border shadow-sm">
                  <div className="p-6 border-b border-border">
                    <h2 className="text-xl font-semibold">Actions rapides</h2>
                  </div>
                  <div className="p-6 space-y-3">
                    <Button className="w-full justify-start">
                      <Plus className="mr-2 h-4 w-4" />
                      Créer un service
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <MessageSquare className="mr-2 h-4 w-4" />
                      Messages (3)
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Calendar className="mr-2 h-4 w-4" />
                      Mes rendez-vous
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Users className="mr-2 h-4 w-4" />
                      Mon profil
                    </Button>
                  </div>
                </div>

                {/* Performance Card */}
                <div className="bg-gradient-to-br from-primary to-secondary p-6 rounded-xl text-white">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-semibold">Performance ce mois</h3>
                      <p className="text-white/80 text-sm">
                        Excellent travail ! Continuez ainsi.
                      </p>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-2xl font-bold">92%</div>
                        <div className="text-sm text-white/80">Taux de satisfaction</div>
                      </div>
                      <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                        <TrendingUp className="h-6 w-6" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}