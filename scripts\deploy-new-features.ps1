# 🚀 Script de Déploiement - Nouvelles Fonctionnalités QribLik
# PowerShell Script pour Windows

Write-Host "🚀 Déploiement des Nouvelles Fonctionnalités QribLik" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Gray

# Fonction pour vérifier si une commande existe
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Vérification des prérequis
Write-Host "🔍 Vérification des prérequis..." -ForegroundColor Yellow

if (-not (Test-Command "node")) {
    Write-Host "❌ Node.js n'est pas installé" -ForegroundColor Red
    exit 1
}

if (-not (Test-Command "npm")) {
    Write-Host "❌ npm n'est pas installé" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Node.js et npm sont installés" -ForegroundColor Green

# Vérifier si .env.local existe
if (-not (Test-Path ".env.local")) {
    Write-Host "⚠️  Fichier .env.local manquant" -ForegroundColor Yellow
    Write-Host "📋 Copie de .env.example vers .env.local..." -ForegroundColor Blue
    Copy-Item ".env.example" ".env.local"
    Write-Host "✅ Fichier .env.local créé - Veuillez le configurer avant de continuer" -ForegroundColor Green
    Write-Host "🔧 Variables importantes à configurer:" -ForegroundColor Cyan
    Write-Host "   - DATABASE_URL (base de données)" -ForegroundColor White
    Write-Host "   - NEXTAUTH_SECRET (authentification)" -ForegroundColor White
    Write-Host "   - GOOGLE_MAPS_API_KEY (optionnel - carte)" -ForegroundColor White
    Write-Host "   - UPLOADTHING_SECRET (optionnel - upload images)" -ForegroundColor White
    
    $continue = Read-Host "Continuer le déploiement ? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        Write-Host "⏸️  Déploiement interrompu pour configuration" -ForegroundColor Yellow
        exit 0
    }
}

# Étape 1: Installation des dépendances
Write-Host "📦 Installation des dépendances..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Échec de l'installation des dépendances" -ForegroundColor Red
    exit 1
}
Write-Host "✅ Dépendances installées" -ForegroundColor Green

# Étape 2: Génération du client Prisma
Write-Host "🗄️  Génération du client Prisma..." -ForegroundColor Yellow
npx prisma generate
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Échec de la génération Prisma" -ForegroundColor Red
    exit 1
}
Write-Host "✅ Client Prisma généré" -ForegroundColor Green

# Étape 3: Migration de la base de données
Write-Host "🔄 Migration de la base de données..." -ForegroundColor Yellow
Write-Host "⚠️  Cette étape va modifier votre base de données" -ForegroundColor Yellow

$migrate = Read-Host "Continuer avec la migration ? (y/N)"
if ($migrate -eq "y" -or $migrate -eq "Y") {
    npx prisma db push
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Échec de la migration" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Base de données migrée" -ForegroundColor Green
} else {
    Write-Host "⏭️  Migration ignorée" -ForegroundColor Yellow
}

# Étape 4: Seeding des nouvelles données (optionnel)
Write-Host "🌱 Ajout de données de test..." -ForegroundColor Yellow
$seed = Read-Host "Ajouter des données de test pour les nouvelles fonctionnalités ? (y/N)"
if ($seed -eq "y" -or $seed -eq "Y") {
    npx tsx scripts/seed-new-features.ts
    if ($LASTEXITCODE -ne 0) {
        Write-Host "⚠️  Échec du seeding (non critique)" -ForegroundColor Yellow
    } else {
        Write-Host "✅ Données de test ajoutées" -ForegroundColor Green
    }
}

# Étape 5: Test des nouvelles fonctionnalités
Write-Host "🧪 Test des nouvelles fonctionnalités..." -ForegroundColor Yellow
npx tsx scripts/test-new-features.ts
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️  Certains tests ont échoué (vérifiez les logs)" -ForegroundColor Yellow
} else {
    Write-Host "✅ Tests réussis" -ForegroundColor Green
}

# Étape 6: Build de production
Write-Host "🏗️  Build de production..." -ForegroundColor Yellow
$build = Read-Host "Effectuer le build de production ? (y/N)"
if ($build -eq "y" -or $build -eq "Y") {
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Échec du build" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Build réussi" -ForegroundColor Green
}

# Résumé final
Write-Host ""
Write-Host "🎉 Déploiement terminé !" -ForegroundColor Green
Write-Host "=" * 40 -ForegroundColor Gray
Write-Host ""
Write-Host "📋 Nouvelles fonctionnalités déployées:" -ForegroundColor Cyan
Write-Host "   ✅ Portfolio Artisans (/dashboard/portfolio)" -ForegroundColor White
Write-Host "   ✅ Agenda Intégré (/dashboard/agenda)" -ForegroundColor White
Write-Host "   ✅ Carte des Demandes (/dashboard/map)" -ForegroundColor White
Write-Host "   ✅ Partage Réseaux Sociaux" -ForegroundColor White
Write-Host "   ✅ Notifications Améliorées" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Prochaines étapes:" -ForegroundColor Yellow
Write-Host "   1. Démarrer le serveur: npm run dev" -ForegroundColor White
Write-Host "   2. Tester les nouvelles pages" -ForegroundColor White
Write-Host "   3. Configurer les APIs externes (Maps, UploadThing)" -ForegroundColor White
Write-Host "   4. Déployer en production" -ForegroundColor White
Write-Host ""
Write-Host "📚 Documentation:" -ForegroundColor Cyan
Write-Host "   - GUIDE_CONFIGURATION.md (configuration détaillée)" -ForegroundColor White
Write-Host "   - NOUVELLES_FONCTIONNALITES.md (documentation complète)" -ForegroundColor White
Write-Host ""
Write-Host "💡 En cas de problème, consultez les logs ci-dessus" -ForegroundColor Blue
