# 🔐 Script Complet de Correction d'Authentification - QribLik
# PowerShell Script pour Windows

Write-Host "🔐 Correction Complète de l'Authentification QribLik" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Gray

# Étape 1: Diagnostic initial
Write-Host "🔍 Étape 1: Diagnostic des problèmes..." -ForegroundColor Yellow
try {
    node scripts/fix-auth-issues.js
    Write-Host "✅ Diagnostic terminé" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Problème lors du diagnostic" -ForegroundColor Yellow
}

# Étape 2: Validation des corrections
Write-Host "`n🔍 Étape 2: Validation des corrections..." -ForegroundColor Yellow
try {
    $validationResult = node scripts/validate-auth-fix.js
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Validation réussie" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Problèmes détectés lors de la validation" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Erreur lors de la validation" -ForegroundColor Red
}

# Étape 3: Mise à jour de la base de données
Write-Host "`n🗄️ Étape 3: Mise à jour de la base de données..." -ForegroundColor Yellow
try {
    npx prisma db push --force-reset
    Write-Host "✅ Base de données mise à jour" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur lors de la mise à jour de la base de données" -ForegroundColor Red
    Write-Host "💡 Vérifiez que Prisma est correctement configuré" -ForegroundColor Blue
}

# Étape 4: Création des utilisateurs de test
Write-Host "`n👥 Étape 4: Création des utilisateurs de test..." -ForegroundColor Yellow
try {
    npx tsx scripts/create-test-users.ts
    Write-Host "✅ Utilisateurs de test créés" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur lors de la création des utilisateurs" -ForegroundColor Red
    Write-Host "💡 Vérifiez que tsx est installé: npm install -g tsx" -ForegroundColor Blue
}

# Étape 5: Nettoyage du cache
Write-Host "`n🧹 Étape 5: Nettoyage du cache..." -ForegroundColor Yellow
try {
    if (Test-Path ".next") {
        Remove-Item -Recurse -Force .next
        Write-Host "✅ Cache Next.js nettoyé" -ForegroundColor Green
    }
    
    if (Test-Path "node_modules/.cache") {
        Remove-Item -Recurse -Force "node_modules/.cache"
        Write-Host "✅ Cache Node.js nettoyé" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Problème lors du nettoyage du cache" -ForegroundColor Yellow
}

# Étape 6: Test de compilation
Write-Host "`n🔨 Étape 6: Test de compilation..." -ForegroundColor Yellow
try {
    npx tsc --noEmit
    Write-Host "✅ Compilation TypeScript réussie" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreurs de compilation détectées" -ForegroundColor Red
    Write-Host "💡 Corrigez les erreurs TypeScript avant de continuer" -ForegroundColor Blue
}

# Étape 7: Démarrage du serveur (optionnel)
Write-Host "`n🚀 Étape 7: Démarrage du serveur..." -ForegroundColor Yellow
$startServer = Read-Host "Voulez-vous démarrer le serveur maintenant ? (y/N)"
if ($startServer -eq "y" -or $startServer -eq "Y") {
    Write-Host "🔄 Démarrage du serveur de développement..." -ForegroundColor Blue
    Write-Host "💡 Le serveur va démarrer. Testez sur http://localhost:3000/auth/signin" -ForegroundColor Blue
    Write-Host "💡 Utilisez Ctrl+C pour arrêter le serveur" -ForegroundColor Blue
    npm run dev
} else {
    Write-Host "⏭️  Serveur non démarré" -ForegroundColor Yellow
}

# Résumé final
Write-Host "`n" -NoNewline
Write-Host "🎉 CORRECTION D'AUTHENTIFICATION TERMINÉE !" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Gray

Write-Host "`n📋 Résumé des actions effectuées:" -ForegroundColor Cyan
Write-Host "  ✅ Diagnostic des problèmes d'authentification" -ForegroundColor White
Write-Host "  ✅ Validation des corrections appliquées" -ForegroundColor White
Write-Host "  ✅ Mise à jour de la base de données" -ForegroundColor White
Write-Host "  ✅ Création des utilisateurs de test" -ForegroundColor White
Write-Host "  ✅ Nettoyage du cache" -ForegroundColor White
Write-Host "  ✅ Vérification de la compilation" -ForegroundColor White

Write-Host "`n🧪 Comptes de test disponibles:" -ForegroundColor Cyan
Write-Host "  👤 <EMAIL> / password123 (Prestataire)" -ForegroundColor White
Write-Host "  🔧 <EMAIL> / password123 (Prestataire)" -ForegroundColor White
Write-Host "  👥 <EMAIL> / password123 (Client)" -ForegroundColor White
Write-Host "  👑 <EMAIL> / password123 (Admin)" -ForegroundColor White

Write-Host "`n🚀 Prochaines étapes:" -ForegroundColor Yellow
Write-Host "  1. Démarrer le serveur: npm run dev" -ForegroundColor White
Write-Host "  2. Tester la connexion: http://localhost:3000/auth/signin" -ForegroundColor White
Write-Host "  3. Vérifier la redirection vers /dashboard" -ForegroundColor White
Write-Host "  4. Exécuter les tests E2E: npm run test:e2e" -ForegroundColor White

Write-Host "`n📚 Documentation:" -ForegroundColor Cyan
Write-Host "  - RESOLUTION_AUTH.md (guide de résolution)" -ForegroundColor White
Write-Host "  - GUIDE_TESTS.md (guide des tests)" -ForegroundColor White
Write-Host "  - validation-report.json (rapport de validation)" -ForegroundColor White

Write-Host "`n💡 En cas de problème:" -ForegroundColor Blue
Write-Host "  - Consultez les logs du serveur" -ForegroundColor White
Write-Host "  - Vérifiez les logs du navigateur (F12)" -ForegroundColor White
Write-Host "  - Relancez ce script si nécessaire" -ForegroundColor White
Write-Host "  - Consultez RESOLUTION_AUTH.md pour le débogage" -ForegroundColor White

Write-Host "`n🎯 Objectif atteint: Authentification fonctionnelle et tests E2E réussis !" -ForegroundColor Green
