'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, Loader2, ArrowLeft, MessageSquare } from 'lucide-react'

export default function PaymentSuccessPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [paymentDetails, setPaymentDetails] = useState<any>(null)

  const paymentIntentId = searchParams.get('payment_intent')
  const paymentIntentClientSecret = searchParams.get('payment_intent_client_secret')

  useEffect(() => {
    if (paymentIntentId) {
      // Simuler la récupération des détails du paiement
      // En production, vous feriez un appel API pour récupérer les détails
      setTimeout(() => {
        setPaymentDetails({
          id: paymentIntentId,
          amount: 500, // Exemple
          currency: 'MAD',
          description: 'Paiement pour service'
        })
        setLoading(false)
      }, 1000)
    } else {
      setLoading(false)
    }
  }, [paymentIntentId])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="max-w-md mx-auto">
          <CardContent className="flex flex-col items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin mb-4" />
            <p className="text-gray-600">Confirmation du paiement...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <CardTitle className="text-green-900 text-2xl">Paiement réussi !</CardTitle>
          <CardDescription>
            Votre paiement a été traité avec succès.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {paymentDetails && (
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Montant payé</span>
                <span className="font-semibold">{paymentDetails.amount} {paymentDetails.currency}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">ID de transaction</span>
                <span className="text-sm font-mono">{paymentDetails.id.slice(-8)}</span>
              </div>
            </div>
          )}

          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Le prestataire a été notifié de votre paiement et peut maintenant commencer le travail.
              Vous recevrez une confirmation par email.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <h3 className="font-semibold text-gray-900">Prochaines étapes :</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                Le prestataire va commencer le travail selon les termes convenus
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                Vous pouvez suivre l'avancement via la messagerie
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                Une fois le travail terminé, vous pourrez laisser un avis
              </li>
            </ul>
          </div>

          <div className="flex flex-col gap-3">
            <Button 
              onClick={() => router.push('/dashboard/messages')}
              className="w-full"
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              Aller à la messagerie
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => router.push('/dashboard/payments')}
              className="w-full"
            >
              Voir mes paiements
            </Button>
            
            <Button 
              variant="ghost"
              onClick={() => router.push('/dashboard')}
              className="w-full"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Retour au dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
