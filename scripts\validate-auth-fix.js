const fs = require('fs');
const { execSync } = require('child_process');

console.log('✅ Validation de la Correction d\'Authentification\n');

let allChecksPass = true;
const issues = [];

// 1. Vérifier les fichiers critiques
console.log('📁 1. Vérification des fichiers critiques...');

const criticalFiles = [
  { path: 'lib/auth.ts', desc: 'Configuration NextAuth' },
  { path: 'middleware.ts', desc: 'Middleware de protection' },
  { path: 'app/auth/signin/page.tsx', desc: 'Page de connexion' },
  { path: '.env.local', desc: 'Variables d\'environnement' },
  { path: 'scripts/create-test-users.ts', desc: 'Script utilisateurs de test' }
];

criticalFiles.forEach(file => {
  if (fs.existsSync(file.path)) {
    console.log(`  ✅ ${file.desc}`);
  } else {
    console.log(`  ❌ ${file.desc} - MANQUANT`);
    issues.push(`Fichier manquant: ${file.path}`);
    allChecksPass = false;
  }
});

// 2. Vérifier le contenu des fichiers clés
console.log('\n🔍 2. Vérification du contenu des fichiers...');

// Vérifier auth.ts
if (fs.existsSync('lib/auth.ts')) {
  const authContent = fs.readFileSync('lib/auth.ts', 'utf8');
  
  const authChecks = [
    { pattern: 'async redirect', desc: 'Callback de redirection' },
    { pattern: 'baseUrl}/dashboard', desc: 'Redirection par défaut' },
    { pattern: 'pages:', desc: 'Pages personnalisées' }
  ];
  
  authChecks.forEach(check => {
    if (authContent.includes(check.pattern)) {
      console.log(`  ✅ ${check.desc} configuré`);
    } else {
      console.log(`  ❌ ${check.desc} manquant`);
      issues.push(`Configuration manquante dans auth.ts: ${check.desc}`);
      allChecksPass = false;
    }
  });
}

// Vérifier signin page
if (fs.existsSync('app/auth/signin/page.tsx')) {
  const signinContent = fs.readFileSync('app/auth/signin/page.tsx', 'utf8');
  
  if (signinContent.includes('window.location.href')) {
    console.log('  ✅ Redirection forcée configurée');
  } else {
    console.log('  ⚠️  Redirection forcée non configurée');
    issues.push('Page de connexion sans redirection forcée');
  }
  
  if (signinContent.includes('Identifiants invalides')) {
    console.log('  ✅ Message d\'erreur cohérent');
  } else {
    console.log('  ⚠️  Message d\'erreur incohérent avec les tests');
  }
}

// 3. Vérifier les variables d'environnement
console.log('\n🔧 3. Vérification des variables d\'environnement...');

if (fs.existsSync('.env.local')) {
  const envContent = fs.readFileSync('.env.local', 'utf8');
  
  const envVars = ['NEXTAUTH_SECRET', 'NEXTAUTH_URL', 'DATABASE_URL'];
  
  envVars.forEach(varName => {
    if (envContent.includes(`${varName}=`) && !envContent.includes(`${varName}=\n`)) {
      console.log(`  ✅ ${varName} configuré`);
    } else {
      console.log(`  ❌ ${varName} manquant ou vide`);
      issues.push(`Variable d'environnement: ${varName}`);
      allChecksPass = false;
    }
  });
} else {
  console.log('  ❌ Fichier .env.local manquant');
  issues.push('Fichier .env.local manquant');
  allChecksPass = false;
}

// 4. Vérifier la base de données
console.log('\n🗄️ 4. Vérification de la base de données...');

const dbPath = 'prisma/dev.db';
if (fs.existsSync(dbPath)) {
  console.log('  ✅ Base de données présente');
  
  // Vérifier la taille (doit contenir des données)
  const stats = fs.statSync(dbPath);
  if (stats.size > 50000) { // Plus de 50KB = probablement des données
    console.log('  ✅ Base de données contient des données');
  } else {
    console.log('  ⚠️  Base de données semble vide');
    issues.push('Base de données potentiellement vide');
  }
} else {
  console.log('  ❌ Base de données manquante');
  issues.push('Base de données manquante');
  allChecksPass = false;
}

// 5. Vérifier les tests E2E
console.log('\n🧪 5. Vérification des tests E2E...');

const testFiles = [
  'tests/e2e/auth.spec.ts',
  'tests/e2e/portfolio.spec.ts'
];

testFiles.forEach(testFile => {
  if (fs.existsSync(testFile)) {
    console.log(`  ✅ ${testFile} présent`);
    
    const testContent = fs.readFileSync(testFile, 'utf8');
    if (testContent.includes('waitForURL')) {
      console.log(`    ✅ Attente de redirection configurée`);
    } else {
      console.log(`    ⚠️  Attente de redirection manquante`);
    }
  } else {
    console.log(`  ❌ ${testFile} manquant`);
    issues.push(`Fichier de test manquant: ${testFile}`);
  }
});

// 6. Test de compilation
console.log('\n🔨 6. Test de compilation...');

try {
  console.log('  🔄 Vérification TypeScript...');
  execSync('npx tsc --noEmit', { stdio: 'pipe' });
  console.log('  ✅ Compilation TypeScript réussie');
} catch (error) {
  console.log('  ❌ Erreurs de compilation TypeScript');
  issues.push('Erreurs de compilation TypeScript');
  allChecksPass = false;
}

// 7. Résumé et recommandations
console.log('\n📊 RÉSUMÉ DE LA VALIDATION:');

if (allChecksPass && issues.length === 0) {
  console.log('🎉 TOUTES LES VÉRIFICATIONS SONT PASSÉES !');
  console.log('\n✅ Votre système d\'authentification est prêt');
  console.log('\n🚀 PROCHAINES ÉTAPES:');
  console.log('  1. Créer les utilisateurs de test: npx tsx scripts/create-test-users.ts');
  console.log('  2. Redémarrer le serveur: npm run dev');
  console.log('  3. Tester manuellement: http://localhost:3000/auth/signin');
  console.log('  4. Exécuter les tests E2E: npm run test:e2e');
} else {
  console.log('⚠️  PROBLÈMES DÉTECTÉS');
  console.log(`\n❌ ${issues.length} problème(s) à résoudre:`);
  issues.forEach((issue, index) => {
    console.log(`  ${index + 1}. ${issue}`);
  });
  
  console.log('\n🔧 ACTIONS CORRECTIVES:');
  
  if (issues.some(i => i.includes('auth.ts'))) {
    console.log('  - Vérifier la configuration NextAuth dans lib/auth.ts');
  }
  
  if (issues.some(i => i.includes('environnement'))) {
    console.log('  - Exécuter: node scripts/fix-auth-issues.js');
  }
  
  if (issues.some(i => i.includes('base de données'))) {
    console.log('  - Exécuter: npx prisma db push');
  }
  
  if (issues.some(i => i.includes('TypeScript'))) {
    console.log('  - Corriger les erreurs TypeScript affichées');
  }
}

// 8. Créer un rapport de validation
const report = {
  timestamp: new Date().toISOString(),
  allChecksPass,
  issuesCount: issues.length,
  issues,
  nextSteps: allChecksPass ? [
    'Créer les utilisateurs de test',
    'Redémarrer le serveur',
    'Tester manuellement',
    'Exécuter les tests E2E'
  ] : [
    'Résoudre les problèmes listés',
    'Relancer la validation',
    'Tester après corrections'
  ]
};

fs.writeFileSync('validation-report.json', JSON.stringify(report, null, 2));
console.log('\n📄 Rapport sauvegardé dans validation-report.json');

// 9. Code de sortie
if (allChecksPass) {
  console.log('\n🎯 VALIDATION RÉUSSIE - Prêt pour les tests !');
  process.exit(0);
} else {
  console.log('\n⚠️  VALIDATION ÉCHOUÉE - Corrections nécessaires');
  process.exit(1);
}
