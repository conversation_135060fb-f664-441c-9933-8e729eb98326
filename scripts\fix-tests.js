const fs = require('fs');
const { execSync } = require('child_process');

console.log('🔧 Correction et Test du Système de Tests\n');

// 1. Vérifier Jest
console.log('🧪 1. Test de Jest...');
try {
  execSync('npx jest tests/unit/simple.test.ts --silent', { stdio: 'pipe' });
  console.log('✅ Jest fonctionne correctement');
} catch (error) {
  console.log('❌ Problème avec Jest');
  console.log('💡 Vérifiez la configuration dans jest.config.js');
  process.exit(1);
}

// 2. Tester les scripts npm
console.log('\n📦 2. Test des scripts npm...');
try {
  execSync('npm run test:unit -- tests/unit/simple.test.ts', { stdio: 'pipe' });
  console.log('✅ Scripts npm fonctionnent');
} catch (error) {
  console.log('❌ Problème avec les scripts npm');
  console.log('💡 Vérifiez package.json');
}

// 3. Vérifier la structure des tests
console.log('\n📁 3. Vérification de la structure...');
const testDirs = [
  'tests/unit',
  'tests/integration', 
  'tests/e2e',
  'tests/fixtures',
  'tests/helpers'
];

testDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    const files = fs.readdirSync(dir);
    console.log(`✅ ${dir} (${files.length} fichiers)`);
  } else {
    console.log(`⚠️  ${dir} manquant`);
  }
});

// 4. Compter les tests
console.log('\n📊 4. Inventaire des tests...');
let testCount = 0;

function countTests(dir) {
  if (!fs.existsSync(dir)) return 0;
  
  const files = fs.readdirSync(dir, { withFileTypes: true });
  let count = 0;
  
  files.forEach(file => {
    if (file.isDirectory()) {
      count += countTests(`${dir}/${file.name}`);
    } else if (file.name.endsWith('.test.ts') || file.name.endsWith('.test.tsx')) {
      count++;
    }
  });
  
  return count;
}

const unitTests = countTests('tests/unit');
const integrationTests = countTests('tests/integration');
const e2eTests = countTests('tests/e2e');

console.log(`  📋 Tests unitaires: ${unitTests}`);
console.log(`  🔗 Tests d'intégration: ${integrationTests}`);
console.log(`  🎭 Tests E2E: ${e2eTests}`);
console.log(`  📊 Total: ${unitTests + integrationTests + e2eTests}`);

// 5. Recommandations
console.log('\n💡 5. Recommandations...');

if (unitTests === 0) {
  console.log('⚠️  Aucun test unitaire détecté');
  console.log('💡 Créez des tests dans tests/unit/');
}

if (integrationTests === 0) {
  console.log('⚠️  Aucun test d\'intégration détecté');
  console.log('💡 Créez des tests dans tests/integration/');
}

// 6. Prochaines étapes
console.log('\n🚀 Prochaines étapes:');
console.log('1. Tests simples: npm run test:unit -- tests/unit/simple.test.ts');
console.log('2. Tous les tests unitaires: npm run test:unit');
console.log('3. Tests d\'intégration: npm run test:integration');
console.log('4. Tests E2E: npm run test:e2e');
console.log('5. Couverture: npm run test:coverage');

// 7. Diagnostic des problèmes courants
console.log('\n🔍 Diagnostic des problèmes...');

// Vérifier les dépendances de test
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const devDeps = packageJson.devDependencies || {};

const testDeps = [
  'jest',
  '@testing-library/react',
  '@testing-library/jest-dom',
  '@playwright/test',
  '@types/jest'
];

testDeps.forEach(dep => {
  if (devDeps[dep]) {
    console.log(`✅ ${dep} installé`);
  } else {
    console.log(`⚠️  ${dep} manquant`);
  }
});

console.log('\n🎯 Système de tests configuré et prêt !');
console.log('📚 Consultez GUIDE_TESTS.md pour plus d\'informations.');
