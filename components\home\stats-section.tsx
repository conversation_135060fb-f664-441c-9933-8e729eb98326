'use client'

import { motion } from 'framer-motion'
import { Users, Star, CheckCircle, MapPin } from 'lucide-react'

const stats = [
  {
    id: 1,
    icon: Users,
    value: '100,000+',
    label: 'Prestataires actifs',
    color: 'text-blue-600 bg-blue-50'
  },
  {
    id: 2,
    icon: CheckCircle,
    value: '500,000+',
    label: 'Services réalisés',
    color: 'text-green-600 bg-green-50'
  },
  {
    id: 3,
    icon: Star,
    value: '4.8/5',
    label: 'Note moyenne',
    color: 'text-yellow-600 bg-yellow-50'
  },
  {
    id: 4,
    icon: MapPin,
    value: '50+',
    label: 'Villes couvertes',
    color: 'text-purple-600 bg-purple-50'
  }
]

export function StatsSection() {
  return (
    <section className="py-16 lg:py-24 bg-gradient-to-r from-primary/5 to-secondary/5">
      <div className="container">
        <motion.div 
          className="text-center space-y-4 mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl font-bold">
            QribLik en chiffres
          </h2>
          <p className="text-xl text-muted-foreground">
            La confiance de milliers de Marocains
          </p>
        </motion.div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <motion.div
                key={stat.id}
                className="text-center"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
              >
                <div className={`w-16 h-16 ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                  <Icon className="h-8 w-8" />
                </div>
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold gradient-text">
                    {stat.value}
                  </div>
                  <div className="text-muted-foreground font-medium">
                    {stat.label}
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>
      </div>
    </section>
  )
}