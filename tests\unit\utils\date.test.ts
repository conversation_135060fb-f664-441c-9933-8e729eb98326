import { formatDistanceToNow, format, isToday, isYesterday, startOfWeek, endOfWeek } from 'date-fns'
import { fr } from 'date-fns/locale'

// Utility functions for date handling in QribLik
export const formatRelativeTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return formatDistanceToNow(dateObj, { addSuffix: true, locale: fr })
}

export const formatDisplayDate = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isToday(dateObj)) {
    return "Aujourd'hui"
  }
  
  if (isYesterday(dateObj)) {
    return 'Hier'
  }
  
  return format(dateObj, 'dd MMMM yyyy', { locale: fr })
}

export const getWeekBounds = (date: Date = new Date()) => {
  return {
    start: startOfWeek(date, { weekStartsOn: 1 }), // Monday
    end: endOfWeek(date, { weekStartsOn: 1 }),
  }
}

export const isWithinWorkingHours = (time: string): boolean => {
  const [hours, minutes] = time.split(':').map(Number)
  const totalMinutes = hours * 60 + minutes
  
  // Working hours: 8:00 - 18:00
  const startWork = 8 * 60 // 8:00
  const endWork = 18 * 60   // 18:00
  
  return totalMinutes >= startWork && totalMinutes <= endWork
}

export const calculateDuration = (startTime: string, endTime: string): number => {
  const [startHours, startMinutes] = startTime.split(':').map(Number)
  const [endHours, endMinutes] = endTime.split(':').map(Number)
  
  const startTotalMinutes = startHours * 60 + startMinutes
  const endTotalMinutes = endHours * 60 + endMinutes
  
  return endTotalMinutes - startTotalMinutes
}

describe('Date Utilities', () => {
  describe('formatRelativeTime', () => {
    it('should format recent dates correctly', () => {
      const now = new Date()
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
      
      const result = formatRelativeTime(oneHourAgo)
      expect(result).toMatch(/il y a environ 1 heure/)
    })

    it('should handle string dates', () => {
      const dateString = '2024-09-17T10:00:00.000Z'
      const result = formatRelativeTime(dateString)
      expect(result).toContain('il y a')
    })

    it('should handle future dates', () => {
      const future = new Date(Date.now() + 60 * 60 * 1000) // 1 hour from now
      const result = formatRelativeTime(future)
      expect(result).toContain('dans')
    })
  })

  describe('formatDisplayDate', () => {
    beforeEach(() => {
      // Mock current date for consistent testing
      jest.useFakeTimers()
      jest.setSystemTime(new Date('2024-09-17T12:00:00.000Z'))
    })

    afterEach(() => {
      jest.useRealTimers()
    })

    it('should return "Aujourd\'hui" for today', () => {
      const today = new Date('2024-09-17T10:00:00.000Z')
      const result = formatDisplayDate(today)
      expect(result).toBe("Aujourd'hui")
    })

    it('should return "Hier" for yesterday', () => {
      const yesterday = new Date('2024-09-16T10:00:00.000Z')
      const result = formatDisplayDate(yesterday)
      expect(result).toBe('Hier')
    })

    it('should return formatted date for older dates', () => {
      const oldDate = new Date('2024-09-15T10:00:00.000Z')
      const result = formatDisplayDate(oldDate)
      expect(result).toMatch(/15 septembre 2024/)
    })

    it('should handle string input', () => {
      const result = formatDisplayDate('2024-09-17T10:00:00.000Z')
      expect(result).toBe("Aujourd'hui")
    })
  })

  describe('getWeekBounds', () => {
    it('should return correct week bounds starting from Monday', () => {
      // Wednesday, September 18, 2024
      const testDate = new Date('2024-09-18T12:00:00.000Z')
      const bounds = getWeekBounds(testDate)
      
      // Should start on Monday, September 16
      expect(bounds.start.getDay()).toBe(1) // Monday
      expect(bounds.start.getDate()).toBe(16)
      
      // Should end on Sunday, September 22
      expect(bounds.end.getDay()).toBe(0) // Sunday
      expect(bounds.end.getDate()).toBe(22)
    })

    it('should use current date when no date provided', () => {
      const bounds = getWeekBounds()
      expect(bounds.start).toBeInstanceOf(Date)
      expect(bounds.end).toBeInstanceOf(Date)
      expect(bounds.end.getTime()).toBeGreaterThan(bounds.start.getTime())
    })
  })

  describe('isWithinWorkingHours', () => {
    it('should return true for times within working hours', () => {
      expect(isWithinWorkingHours('09:00')).toBe(true)
      expect(isWithinWorkingHours('12:30')).toBe(true)
      expect(isWithinWorkingHours('17:59')).toBe(true)
      expect(isWithinWorkingHours('08:00')).toBe(true)
      expect(isWithinWorkingHours('18:00')).toBe(true)
    })

    it('should return false for times outside working hours', () => {
      expect(isWithinWorkingHours('07:59')).toBe(false)
      expect(isWithinWorkingHours('18:01')).toBe(false)
      expect(isWithinWorkingHours('22:00')).toBe(false)
      expect(isWithinWorkingHours('06:00')).toBe(false)
    })

    it('should handle edge cases', () => {
      expect(isWithinWorkingHours('00:00')).toBe(false)
      expect(isWithinWorkingHours('23:59')).toBe(false)
    })
  })

  describe('calculateDuration', () => {
    it('should calculate duration correctly for same day', () => {
      expect(calculateDuration('09:00', '17:00')).toBe(480) // 8 hours = 480 minutes
      expect(calculateDuration('14:30', '16:45')).toBe(135) // 2h15 = 135 minutes
      expect(calculateDuration('08:00', '08:30')).toBe(30)  // 30 minutes
    })

    it('should handle zero duration', () => {
      expect(calculateDuration('10:00', '10:00')).toBe(0)
    })

    it('should handle negative duration (end before start)', () => {
      expect(calculateDuration('17:00', '09:00')).toBe(-480)
    })

    it('should handle minutes correctly', () => {
      expect(calculateDuration('09:15', '10:45')).toBe(90) // 1h30 = 90 minutes
      expect(calculateDuration('14:07', '14:52')).toBe(45) // 45 minutes
    })
  })
})
