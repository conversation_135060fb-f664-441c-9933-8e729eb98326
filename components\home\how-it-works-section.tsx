'use client'

import { Search, MessageSquare, CheckCircle, Star } from 'lucide-react'
import { motion } from 'framer-motion'

const steps = [
  {
    id: 1,
    icon: Search,
    title: 'Décrivez votre besoin',
    description: 'Publiez votre demande gratuitement avec tous les détails nécessaires',
    color: 'text-blue-600 bg-blue-50'
  },
  {
    id: 2,
    icon: MessageSquare,
    title: 'Recevez des propositions',
    description: 'Les prestataires près de chez vous vous contactent avec leurs devis',
    color: 'text-green-600 bg-green-50'
  },
  {
    id: 3,
    icon: CheckCircle,
    title: 'Choisissez votre prestataire',
    description: 'Comparez les profils, avis et prix pour faire votre choix',
    color: 'text-orange-600 bg-orange-50'
  },
  {
    id: 4,
    icon: Star,
    title: 'Évaluez le service',
    description: 'Laissez un avis pour aider la communauté QribLik',
    color: 'text-purple-600 bg-purple-50'
  }
]

export function HowItWorksSection() {
  return (
    <section className="py-16 lg:py-24">
      <div className="container">
        <motion.div 
          className="text-center space-y-4 mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl font-bold">
            Comment ça marche ?
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            4 étapes simples pour trouver le prestataire idéal
          </p>
        </motion.div>

        <div className="relative">
          {/* Connection Lines - Desktop */}
          <div className="hidden lg:block absolute top-1/2 left-0 w-full h-0.5 bg-gradient-to-r from-primary via-secondary to-accent transform -translate-y-1/2" />

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => {
              const Icon = step.icon
              return (
                <motion.div
                  key={step.id}
                  className="relative text-center"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.2, duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  {/* Step Number */}
                  <div className="relative mb-6">
                    <div className={`w-16 h-16 ${step.color} rounded-full flex items-center justify-center mx-auto border-4 border-background shadow-lg`}>
                      <Icon className="h-8 w-8" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {step.id}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h3 className="text-xl font-semibold">
                      {step.title}
                    </h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {step.description}
                    </p>
                  </div>

                  {/* Vertical line for mobile */}
                  {index < steps.length - 1 && (
                    <div className="lg:hidden absolute left-1/2 -bottom-4 w-0.5 h-8 bg-gradient-to-b from-primary to-secondary transform -translate-x-1/2" />
                  )}
                </motion.div>
              )
            })}
          </div>
        </div>

        <motion.div 
          className="text-center mt-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center space-x-2 px-6 py-3 bg-green-50 text-green-700 rounded-full">
            <CheckCircle className="h-5 w-5" />
            <span className="font-medium">C'est gratuit et sans engagement !</span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}