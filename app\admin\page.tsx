'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  Briefcase, 
  MessageSquare, 
  CreditCard, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Activity
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { fr } from 'date-fns/locale'

interface AdminStats {
  totalUsers: number
  totalServices: number
  totalRequests: number
  totalPayments: number
  totalRevenue: number
  activeMatches: number
  pendingApprovals: number
  recentActivity: ActivityItem[]
}

interface ActivityItem {
  id: string
  type: 'user_registered' | 'service_created' | 'payment_completed' | 'match_created'
  description: string
  timestamp: string
  user?: {
    name: string
    email: string
  }
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAdminStats()
  }, [])

  const fetchAdminStats = async () => {
    try {
      // Simuler les données pour la démo
      // En production, vous feriez des appels API réels
      setTimeout(() => {
        setStats({
          totalUsers: 1247,
          totalServices: 892,
          totalRequests: 634,
          totalPayments: 156,
          totalRevenue: 45670.50,
          activeMatches: 89,
          pendingApprovals: 23,
          recentActivity: [
            {
              id: '1',
              type: 'user_registered',
              description: 'Nouvel utilisateur inscrit',
              timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
              user: { name: 'Ahmed Benali', email: '<EMAIL>' }
            },
            {
              id: '2',
              type: 'service_created',
              description: 'Nouveau service créé: Plomberie',
              timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
              user: { name: 'Fatima Zahra', email: '<EMAIL>' }
            },
            {
              id: '3',
              type: 'payment_completed',
              description: 'Paiement de 450 MAD complété',
              timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
              user: { name: 'Youssef Alami', email: '<EMAIL>' }
            },
            {
              id: '4',
              type: 'match_created',
              description: 'Nouveau match créé',
              timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
              user: { name: 'Khadija Mansouri', email: '<EMAIL>' }
            }
          ]
        })
        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error)
      setLoading(false)
    }
  }

  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'user_registered':
        return <Users className="h-4 w-4 text-blue-500" />
      case 'service_created':
        return <Briefcase className="h-4 w-4 text-green-500" />
      case 'payment_completed':
        return <CreditCard className="h-4 w-4 text-emerald-500" />
      case 'match_created':
        return <MessageSquare className="h-4 w-4 text-purple-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getActivityBadgeColor = (type: ActivityItem['type']) => {
    switch (type) {
      case 'user_registered':
        return 'bg-blue-100 text-blue-800'
      case 'service_created':
        return 'bg-green-100 text-green-800'
      case 'payment_completed':
        return 'bg-emerald-100 text-emerald-800'
      case 'match_created':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Erreur lors du chargement des données</p>
        <Button onClick={fetchAdminStats} className="mt-4">
          Réessayer
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard Admin</h1>
          <p className="text-gray-600">Vue d'ensemble de la plateforme QirbLik</p>
        </div>
        <Button onClick={fetchAdminStats}>
          Actualiser
        </Button>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Utilisateurs</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalUsers.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Briefcase className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Services</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalServices.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-8 w-8 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Demandes</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalRequests.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-8 w-8 text-emerald-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Revenus</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalRevenue.toLocaleString()} MAD</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Métriques secondaires */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Paiements</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalPayments}</p>
              </div>
              <CreditCard className="h-8 w-8 text-emerald-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Matches actifs</p>
                <p className="text-2xl font-bold text-gray-900">{stats.activeMatches}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">En attente d'approbation</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pendingApprovals}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alertes et notifications */}
      {stats.pendingApprovals > 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="font-medium text-yellow-800">
                  {stats.pendingApprovals} services en attente d'approbation
                </p>
                <p className="text-sm text-yellow-600">
                  Des services nécessitent votre validation avant publication.
                </p>
              </div>
              <Button variant="outline" size="sm" className="ml-auto">
                Voir les services
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Activité récente */}
      <Card>
        <CardHeader>
          <CardTitle>Activité récente</CardTitle>
          <CardDescription>
            Dernières actions sur la plateforme
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats.recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center space-x-4 p-3 rounded-lg bg-gray-50">
                <div className="flex-shrink-0">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.description}
                  </p>
                  {activity.user && (
                    <p className="text-sm text-gray-500">
                      Par {activity.user.name} ({activity.user.email})
                    </p>
                  )}
                  <p className="text-xs text-gray-400">
                    {formatDistanceToNow(new Date(activity.timestamp), {
                      addSuffix: true,
                      locale: fr
                    })}
                  </p>
                </div>
                <Badge className={getActivityBadgeColor(activity.type)}>
                  {activity.type.replace('_', ' ')}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
