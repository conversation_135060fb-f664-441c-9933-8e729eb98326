import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const updateAvailabilitySchema = z.object({
  dayOfWeek: z.number().min(0).max(6).optional(),
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format d\'heure invalide (HH:MM)').optional(),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format d\'heure invalide (HH:MM)').optional(),
  isAvailable: z.boolean().optional(),
  slotType: z.enum(['WORK', 'BREAK', 'UNAVAILABLE']).optional(),
});

// GET /api/availability/[id] - Récupérer un créneau spécifique
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const availabilitySlot = await prisma.availabilitySlot.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    if (!availabilitySlot) {
      return NextResponse.json(
        { success: false, error: 'Créneau non trouvé' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: availabilitySlot,
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du créneau:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}

// PUT /api/availability/[id] - Mettre à jour un créneau de disponibilité
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const availabilitySlot = await prisma.availabilitySlot.findUnique({
      where: { id: params.id },
      select: { userId: true, dayOfWeek: true, startTime: true, endTime: true },
    });

    if (!availabilitySlot) {
      return NextResponse.json(
        { success: false, error: 'Créneau non trouvé' },
        { status: 404 }
      );
    }

    if (availabilitySlot.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: 'Non autorisé' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = updateAvailabilitySchema.parse(body);

    // Si on modifie les heures ou le jour, vérifier les conflits
    if (validatedData.dayOfWeek !== undefined || 
        validatedData.startTime !== undefined || 
        validatedData.endTime !== undefined) {
      
      const newDayOfWeek = validatedData.dayOfWeek ?? availabilitySlot.dayOfWeek;
      const newStartTime = validatedData.startTime ?? availabilitySlot.startTime;
      const newEndTime = validatedData.endTime ?? availabilitySlot.endTime;

      const conflictingSlots = await prisma.availabilitySlot.findMany({
        where: {
          userId: session.user.id,
          dayOfWeek: newDayOfWeek,
          id: { not: params.id }, // Exclure le créneau actuel
          OR: [
            {
              AND: [
                { startTime: { lte: newStartTime } },
                { endTime: { gt: newStartTime } },
              ],
            },
            {
              AND: [
                { startTime: { lt: newEndTime } },
                { endTime: { gte: newEndTime } },
              ],
            },
            {
              AND: [
                { startTime: { gte: newStartTime } },
                { endTime: { lte: newEndTime } },
              ],
            },
          ],
        },
      });

      if (conflictingSlots.length > 0) {
        return NextResponse.json(
          { success: false, error: 'Ce créneau entre en conflit avec un créneau existant' },
          { status: 400 }
        );
      }
    }

    const updatedSlot = await prisma.availabilitySlot.update({
      where: { id: params.id },
      data: validatedData,
    });

    return NextResponse.json({
      success: true,
      data: updatedSlot,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Données invalides', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Erreur lors de la mise à jour du créneau:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}

// DELETE /api/availability/[id] - Supprimer un créneau de disponibilité
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const availabilitySlot = await prisma.availabilitySlot.findUnique({
      where: { id: params.id },
      select: { userId: true },
    });

    if (!availabilitySlot) {
      return NextResponse.json(
        { success: false, error: 'Créneau non trouvé' },
        { status: 404 }
      );
    }

    if (availabilitySlot.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: 'Non autorisé' },
        { status: 403 }
      );
    }

    await prisma.availabilitySlot.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: 'Créneau supprimé avec succès',
    });
  } catch (error) {
    console.error('Erreur lors de la suppression du créneau:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
