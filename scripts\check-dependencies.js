const fs = require('fs');
const path = require('path');

console.log('🔍 Vérification des dépendances QribLik...\n');

// Lire le package.json
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

console.log('📦 Dépendances installées:');
Object.keys(dependencies).forEach(dep => {
  console.log(`  ✅ ${dep}@${dependencies[dep]}`);
});

console.log('\n🔍 Vérification des modules critiques...');

const criticalModules = [
  'sonner',
  'next-themes', 
  'date-fns',
  'lucide-react',
  'next-auth',
  '@prisma/client',
  'zod',
  'framer-motion'
];

const missing = [];
const installed = [];

criticalModules.forEach(module => {
  if (dependencies[module]) {
    installed.push(module);
    console.log(`  ✅ ${module} - Installé`);
  } else {
    missing.push(module);
    console.log(`  ❌ ${module} - MANQUANT`);
  }
});

console.log('\n📊 Résumé:');
console.log(`  ✅ Modules installés: ${installed.length}/${criticalModules.length}`);
console.log(`  ❌ Modules manquants: ${missing.length}`);

if (missing.length > 0) {
  console.log('\n🔧 Commande pour installer les modules manquants:');
  console.log(`npm install ${missing.join(' ')}`);
} else {
  console.log('\n🎉 Toutes les dépendances critiques sont installées !');
}

// Vérifier les composants UI
console.log('\n🎨 Vérification des composants UI...');
const uiPath = path.join(__dirname, '..', 'components', 'ui');
const requiredComponents = [
  'button.tsx',
  'card.tsx', 
  'badge.tsx',
  'tabs.tsx',
  'dialog.tsx',
  'input.tsx',
  'label.tsx',
  'textarea.tsx',
  'select.tsx',
  'switch.tsx',
  'popover.tsx',
  'scroll-area.tsx',
  'sonner.tsx'
];

const missingComponents = [];
requiredComponents.forEach(component => {
  const componentPath = path.join(uiPath, component);
  if (fs.existsSync(componentPath)) {
    console.log(`  ✅ ${component}`);
  } else {
    missingComponents.push(component);
    console.log(`  ❌ ${component} - MANQUANT`);
  }
});

if (missingComponents.length > 0) {
  console.log(`\n⚠️  ${missingComponents.length} composants UI manquants`);
} else {
  console.log('\n✅ Tous les composants UI requis sont présents');
}

console.log('\n🚀 Prochaines étapes:');
if (missing.length > 0) {
  console.log('1. Installer les modules manquants');
}
if (missingComponents.length > 0) {
  console.log('2. Créer les composants UI manquants');
}
console.log('3. Redémarrer le serveur: npm run dev');
console.log('4. Tester les pages: /dashboard/portfolio, /dashboard/agenda, /dashboard/map');
