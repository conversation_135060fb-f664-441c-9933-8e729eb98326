import { NextApiRequest } from 'next'
import { NextApiResponseServerIO } from '@/types'
import { Server as NetServer } from 'http'
import { Server as ServerIO } from 'socket.io'

export const config = {
  api: {
    bodyParser: false,
  },
}

const ioHandler = (req: NextApiRequest, res: NextApiResponseServerIO) => {
  if (!res.socket.server.io) {
    const path = '/api/socket/io'
    const httpServer: NetServer = res.socket.server as any
    const io = new ServerIO(httpServer, {
      path: path,
      addTrailingSlash: false,
      cors: {
        origin: process.env.NODE_ENV === 'production' 
          ? process.env.NEXTAUTH_URL 
          : 'http://localhost:3000',
        methods: ['GET', 'POST']
      }
    })

    // Gestion des connexions
    io.on('connection', (socket) => {
      console.log('Utilisateur connecté:', socket.id)

      // Rejoindre une conversation
      socket.on('join-conversation', (conversationId: string) => {
        socket.join(conversationId)
        console.log(`Utilisateur ${socket.id} a rejoint la conversation ${conversationId}`)
      })

      // Quitter une conversation
      socket.on('leave-conversation', (conversationId: string) => {
        socket.leave(conversationId)
        console.log(`Utilisateur ${socket.id} a quitté la conversation ${conversationId}`)
      })

      // Envoyer un message
      socket.on('send-message', (data: {
        conversationId: string
        message: any
      }) => {
        // Diffuser le message à tous les participants de la conversation
        socket.to(data.conversationId).emit('new-message', data.message)
      })

      // Indicateur de frappe
      socket.on('typing-start', (data: {
        conversationId: string
        userId: string
        userName: string
      }) => {
        socket.to(data.conversationId).emit('user-typing', {
          userId: data.userId,
          userName: data.userName,
          isTyping: true
        })
      })

      socket.on('typing-stop', (data: {
        conversationId: string
        userId: string
      }) => {
        socket.to(data.conversationId).emit('user-typing', {
          userId: data.userId,
          isTyping: false
        })
      })

      // Marquer comme lu
      socket.on('mark-as-read', (data: {
        conversationId: string
        messageIds: string[]
        userId: string
      }) => {
        socket.to(data.conversationId).emit('messages-read', {
          messageIds: data.messageIds,
          readBy: data.userId
        })
      })

      // Déconnexion
      socket.on('disconnect', () => {
        console.log('Utilisateur déconnecté:', socket.id)
      })
    })

    res.socket.server.io = io
  }

  res.end()
}

export default ioHandler
