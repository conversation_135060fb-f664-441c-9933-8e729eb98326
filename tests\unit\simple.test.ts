// Test simple pour vérifier que Jest fonctionne
describe('Jest Configuration', () => {
  it('should be able to run basic tests', () => {
    expect(1 + 1).toBe(2)
  })

  it('should have access to testing utilities', () => {
    expect(typeof jest).toBe('object')
    expect(typeof describe).toBe('function')
    expect(typeof it).toBe('function')
    expect(typeof expect).toBe('function')
  })

  it('should be able to mock functions', () => {
    const mockFn = jest.fn()
    mockFn('test')
    expect(mockFn).toHaveBeenCalledWith('test')
  })
})

// Test des utilitaires de date
describe('Date Utilities', () => {
  it('should handle date operations', () => {
    const now = new Date()
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
    
    expect(tomorrow.getTime()).toBeGreaterThan(now.getTime())
  })

  it('should format dates correctly', () => {
    const date = new Date('2024-09-17T10:00:00Z')
    const formatted = date.toISOString()
    
    expect(formatted).toContain('2024-09-17')
  })
})
