{"name": "qriblik", "version": "1.0.0", "description": "Plateforme de services de proximité pour le Maroc", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPatterns=tests/unit", "test:integration": "jest --testPathPatterns=tests/integration", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:ci": "npm run test:coverage && npm run test:e2e", "playwright:install": "playwright install"}, "dependencies": {"@auth/prisma-adapter": "^1.0.6", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@prisma/client": "^5.5.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.8", "@stripe/react-stripe-js": "^4.0.2", "@stripe/stripe-js": "^7.9.0", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query": "^5.8.1", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.8.0", "@types/react": "^18.2.25", "@types/react-dom": "^18.2.11", "autoprefixer": "^10.4.16", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "lucide-react": "^0.288.0", "next": "^14.0.0", "next-auth": "5.0.0-beta.4", "next-themes": "^0.4.6", "postcss": "^8.4.31", "prisma": "^5.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-switch": "^7.1.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.7", "stripe": "^18.5.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@playwright/test": "^1.55.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "eslint": "^8.52.0", "eslint-config-next": "^14.0.0", "jest": "^30.1.3", "jest-environment-jsdom": "^30.1.2", "msw": "^2.11.2"}}