'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { DemandHeatmap } from '@/components/maps/demand-heatmap';
import { 
  MapPin, 
  TrendingUp, 
  Users, 
  DollarSign,
  Navigation,
  Target,
  Zap,
  Calendar,
  BarChart3
} from 'lucide-react';

interface DemandZone {
  id: string;
  city: string;
  region: string;
  lat: number;
  lng: number;
  demandCount: number;
  averagePrice: number;
  categories: {
    category: string;
    count: number;
    avgPrice: number;
  }[];
  trend: 'up' | 'down' | 'stable';
  urgentRequests: number;
  lastUpdated: string;
}

export default function MapPage() {
  const { data: session } = useSession();
  const [selectedZone, setSelectedZone] = useState<DemandZone | null>(null);
  const [recommendations, setRecommendations] = useState<{
    bestZones: DemandZone[];
    optimalRoute: string[];
    estimatedRevenue: number;
  } | null>(null);

  const handleZoneSelect = (zone: DemandZone) => {
    setSelectedZone(zone);
    generateRecommendations(zone);
  };

  const generateRecommendations = (zone: DemandZone) => {
    // Simulation de recommandations basées sur la zone sélectionnée
    const mockRecommendations = {
      bestZones: [zone], // Zones recommandées
      optimalRoute: [zone.city, 'Casablanca', 'Rabat'], // Route optimale
      estimatedRevenue: zone.averagePrice * zone.demandCount * 0.1, // Estimation des revenus
    };
    
    setRecommendations(mockRecommendations);
  };

  const getInsights = () => {
    if (!selectedZone) return null;

    const insights = [];
    
    if (selectedZone.trend === 'up') {
      insights.push({
        type: 'positive',
        icon: TrendingUp,
        title: 'Tendance positive',
        description: `La demande à ${selectedZone.city} est en hausse de 15% cette semaine.`
      });
    }
    
    if (selectedZone.urgentRequests > 5) {
      insights.push({
        type: 'urgent',
        icon: Zap,
        title: 'Demandes urgentes',
        description: `${selectedZone.urgentRequests} demandes urgentes nécessitent une attention immédiate.`
      });
    }
    
    if (selectedZone.averagePrice > 300) {
      insights.push({
        type: 'revenue',
        icon: DollarSign,
        title: 'Prix attractifs',
        description: `Prix moyen de ${selectedZone.averagePrice} MAD, supérieur à la moyenne nationale.`
      });
    }

    return insights;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Carte des Opportunités</h1>
          <p className="text-gray-600 mt-2">
            Découvrez les zones avec le plus de demandes pour optimiser vos déplacements
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Carte principale */}
        <div className="lg:col-span-2">
          <DemandHeatmap onZoneSelect={handleZoneSelect} />
        </div>

        {/* Panneau latéral */}
        <div className="space-y-6">
          {/* Zone sélectionnée */}
          {selectedZone ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="w-5 h-5" />
                  <span>{selectedZone.city}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <Users className="w-6 h-6 mx-auto text-blue-600 mb-1" />
                    <div className="font-semibold text-lg">{selectedZone.demandCount}</div>
                    <div className="text-sm text-gray-600">Demandes</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <DollarSign className="w-6 h-6 mx-auto text-green-600 mb-1" />
                    <div className="font-semibold text-lg">{selectedZone.averagePrice}</div>
                    <div className="text-sm text-gray-600">MAD moyen</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Catégories populaires</h4>
                  <div className="space-y-2">
                    {selectedZone.categories
                      .sort((a, b) => b.count - a.count)
                      .slice(0, 5)
                      .map((cat, index) => (
                        <div key={cat.category} className="flex justify-between items-center">
                          <span className="text-sm">{cat.category}</span>
                          <div className="flex items-center space-x-2">
                            <Badge variant="secondary" className="text-xs">
                              {cat.count}
                            </Badge>
                            <span className="text-xs text-green-600 font-medium">
                              {cat.avgPrice} MAD
                            </span>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <MapPin className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Sélectionnez une zone
                </h3>
                <p className="text-gray-600">
                  Cliquez sur une ville pour voir les détails et recommandations
                </p>
              </CardContent>
            </Card>
          )}

          {/* Insights */}
          {selectedZone && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="w-5 h-5" />
                  <span>Insights</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {getInsights()?.map((insight, index) => {
                    const Icon = insight.icon;
                    return (
                      <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                        <Icon className={`w-5 h-5 mt-0.5 ${
                          insight.type === 'positive' ? 'text-green-600' :
                          insight.type === 'urgent' ? 'text-orange-600' :
                          'text-blue-600'
                        }`} />
                        <div>
                          <h4 className="font-medium text-sm">{insight.title}</h4>
                          <p className="text-xs text-gray-600 mt-1">{insight.description}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recommandations */}
          {recommendations && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Navigation className="w-5 h-5" />
                  <span>Recommandations</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Route optimale suggérée</h4>
                  <div className="flex items-center space-x-2">
                    {recommendations.optimalRoute.map((city, index) => (
                      <div key={city} className="flex items-center">
                        <Badge variant="outline" className="text-xs">
                          {city}
                        </Badge>
                        {index < recommendations.optimalRoute.length - 1 && (
                          <div className="w-4 h-px bg-gray-300 mx-2"></div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    <DollarSign className="w-4 h-4 text-green-600" />
                    <span className="font-medium text-sm">Revenus estimés</span>
                  </div>
                  <div className="text-lg font-semibold text-green-700">
                    {recommendations.estimatedRevenue.toFixed(0)} MAD/semaine
                  </div>
                </div>

                <Button className="w-full" size="sm">
                  <Navigation className="w-4 h-4 mr-2" />
                  Planifier ma tournée
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Actions rapides */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Actions rapides</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Calendar className="w-4 h-4 mr-2" />
                Planifier ma semaine
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Users className="w-4 h-4 mr-2" />
                Voir mes clients dans la zone
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Zap className="w-4 h-4 mr-2" />
                Demandes urgentes uniquement
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
