import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { SessionProvider } from 'next-auth/react'
import { ThemeProvider } from 'next-themes'

// Mock session data
export const mockSession = {
  user: {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    userType: 'PRESTATAIRE',
    locationCity: 'Casablanca',
    locationRegion: 'Casablanca-Settat',
    rating: 4.8,
    reviewCount: 24,
  },
  expires: '2024-12-31',
}

// Custom render function with providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <SessionProvider session={mockSession}>
      <ThemeProvider attribute="class" defaultTheme="light">
        {children}
      </ThemeProvider>
    </SessionProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Custom matchers
export const expectToBeInDocument = (element: HTMLElement | null) => {
  expect(element).toBeInTheDocument()
}

export const expectToHaveText = (element: HTMLElement | null, text: string) => {
  expect(element).toHaveTextContent(text)
}

// Mock data generators
export const createMockUser = (overrides = {}) => ({
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
  userType: 'PRESTATAIRE',
  locationCity: 'Casablanca',
  locationRegion: 'Casablanca-Settat',
  rating: 4.8,
  reviewCount: 24,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const createMockPortfolioItem = (overrides = {}) => ({
  id: '1',
  title: 'Test Project',
  description: 'Test Description',
  category: 'BRICOLAGE_REPARATIONS',
  images: ['https://example.com/image.jpg'],
  videos: [],
  beforeImages: [],
  afterImages: [],
  tags: ['test', 'project'],
  isPublic: true,
  isFeatured: false,
  viewCount: 10,
  likeCount: 5,
  shareCount: 2,
  userId: '1',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const createMockAvailabilitySlot = (overrides = {}) => ({
  id: '1',
  userId: '1',
  dayOfWeek: 1, // Monday
  startTime: '09:00',
  endTime: '17:00',
  isAvailable: true,
  slotType: 'WORK',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const createMockNotification = (overrides = {}) => ({
  id: '1',
  title: 'Test Notification',
  message: 'Test message',
  notificationType: 'INFO',
  userId: '1',
  isRead: false,
  isEmailSent: false,
  isPushSent: false,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

// API response helpers
export const createMockApiResponse = (data: any, success = true) => ({
  ok: success,
  status: success ? 200 : 400,
  json: async () => ({
    success,
    data: success ? data : undefined,
    error: success ? undefined : 'Test error',
  }),
})

export const createMockApiError = (error = 'Test error', status = 400) => ({
  ok: false,
  status,
  json: async () => ({
    success: false,
    error,
  }),
})

// Form helpers
export const fillForm = async (form: HTMLFormElement, data: Record<string, string>) => {
  const { fireEvent } = await import('@testing-library/react')
  
  Object.entries(data).forEach(([name, value]) => {
    const input = form.querySelector(`[name="${name}"]`) as HTMLInputElement
    if (input) {
      fireEvent.change(input, { target: { value } })
    }
  })
}

// Wait helpers
export const waitForApiCall = (mockFetch: jest.MockedFunction<typeof fetch>, callIndex = 0) => {
  return new Promise((resolve) => {
    const checkCall = () => {
      if (mockFetch.mock.calls.length > callIndex) {
        resolve(mockFetch.mock.calls[callIndex])
      } else {
        setTimeout(checkCall, 10)
      }
    }
    checkCall()
  })
}

// Local storage helpers
export const mockLocalStorage = () => {
  const store: Record<string, string> = {}
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key]
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key])
    }),
  }
}

// Date helpers for consistent testing
export const mockDate = (dateString: string) => {
  const mockDate = new Date(dateString)
  jest.useFakeTimers()
  jest.setSystemTime(mockDate)
  
  return () => {
    jest.useRealTimers()
  }
}

// Intersection Observer mock
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = jest.fn()
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  
  window.IntersectionObserver = mockIntersectionObserver
  
  return mockIntersectionObserver
}

// Resize Observer mock
export const mockResizeObserver = () => {
  const mockResizeObserver = jest.fn()
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  
  window.ResizeObserver = mockResizeObserver
  
  return mockResizeObserver
}

// Geolocation mock
export const mockGeolocation = (coords = { latitude: 33.5731, longitude: -7.5898 }) => {
  const mockGeolocation = {
    getCurrentPosition: jest.fn((success) => {
      success({ coords })
    }),
    watchPosition: jest.fn(),
    clearWatch: jest.fn(),
  }
  
  Object.defineProperty(global.navigator, 'geolocation', {
    value: mockGeolocation,
    writable: true,
  })
  
  return mockGeolocation
}

// File upload helpers
export const createMockFile = (name = 'test.jpg', type = 'image/jpeg', size = 1024) => {
  const file = new File(['test content'], name, { type })
  Object.defineProperty(file, 'size', { value: size })
  return file
}

export const createMockFileList = (files: File[]) => {
  const fileList = {
    length: files.length,
    item: (index: number) => files[index] || null,
    ...files,
  }
  
  return fileList as FileList
}

// Async helpers
export const flushPromises = () => new Promise(setImmediate)

// Error boundary for testing
export class TestErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  render() {
    if (this.state.hasError) {
      return <div data-testid="error-boundary">Error: {this.state.error?.message}</div>
    }

    return this.props.children
  }
}

// Custom hooks testing
export const renderHook = <T,>(hook: () => T) => {
  let result: { current: T }
  
  const TestComponent = () => {
    result = { current: hook() }
    return null
  }
  
  render(<TestComponent />)
  
  return { result }
}
