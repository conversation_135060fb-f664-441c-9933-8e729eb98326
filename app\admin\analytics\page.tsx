'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Briefcase, 
  CreditCard, 
  MessageSquare,
  Calendar,
  DollarSign,
  Activity,
  Target
} from 'lucide-react'

interface AnalyticsData {
  overview: {
    totalRevenue: number
    revenueGrowth: number
    totalUsers: number
    userGrowth: number
    totalServices: number
    serviceGrowth: number
    totalMatches: number
    matchGrowth: number
  }
  monthlyData: {
    month: string
    revenue: number
    users: number
    services: number
    matches: number
  }[]
  categoryStats: {
    category: string
    services: number
    revenue: number
    avgPrice: number
  }[]
  topPerformers: {
    id: string
    name: string
    revenue: number
    services: number
    rating: number
  }[]
}

export default function AdminAnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  useEffect(() => {
    fetchAnalytics()
  }, [timeRange])

  const fetchAnalytics = async () => {
    try {
      // Simuler les données pour la démo
      setTimeout(() => {
        setAnalytics({
          overview: {
            totalRevenue: 45670.50,
            revenueGrowth: 12.5,
            totalUsers: 1247,
            userGrowth: 8.3,
            totalServices: 892,
            serviceGrowth: 15.2,
            totalMatches: 234,
            matchGrowth: 22.1
          },
          monthlyData: [
            { month: 'Jan', revenue: 12500, users: 150, services: 89, matches: 45 },
            { month: 'Fév', revenue: 15200, users: 180, services: 112, matches: 52 },
            { month: 'Mar', revenue: 18900, users: 220, services: 134, matches: 68 },
            { month: 'Avr', revenue: 22100, users: 280, services: 156, matches: 78 },
            { month: 'Mai', revenue: 28400, users: 340, services: 189, matches: 89 },
            { month: 'Jun', revenue: 35600, users: 420, services: 234, matches: 102 }
          ],
          categoryStats: [
            { category: 'Bricolage & Réparations', services: 234, revenue: 18500, avgPrice: 180 },
            { category: 'Ménage & Nettoyage', services: 189, revenue: 12300, avgPrice: 85 },
            { category: 'Cours & Formation', services: 156, revenue: 15600, avgPrice: 220 },
            { category: 'Jardinage', services: 123, revenue: 9800, avgPrice: 120 },
            { category: 'Transport & Livraison', services: 98, revenue: 7400, avgPrice: 95 },
            { category: 'Informatique & Tech', services: 92, revenue: 14200, avgPrice: 350 }
          ],
          topPerformers: [
            { id: '1', name: 'Ahmed Benali', revenue: 4500, services: 12, rating: 4.9 },
            { id: '2', name: 'Khadija Mansouri', revenue: 3800, services: 8, rating: 4.8 },
            { id: '3', name: 'Youssef Alami', revenue: 3200, services: 15, rating: 4.7 },
            { id: '4', name: 'Fatima Zahra', revenue: 2900, services: 10, rating: 4.6 },
            { id: '5', name: 'Omar Benjelloun', revenue: 2600, services: 7, rating: 4.8 }
          ]
        })
        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Erreur lors du chargement des analytics:', error)
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    const isPositive = value >= 0
    return (
      <div className={`flex items-center space-x-1 ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
        <span>{Math.abs(value).toFixed(1)}%</span>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Statistiques et Analytics</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Erreur lors du chargement des données</p>
        <Button onClick={fetchAnalytics} className="mt-4">
          Réessayer
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Statistiques et Analytics</h1>
          <p className="text-gray-600">Analyse des performances de la plateforme QirbLik</p>
        </div>
        <div className="flex items-center space-x-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 derniers jours</SelectItem>
              <SelectItem value="30d">30 derniers jours</SelectItem>
              <SelectItem value="90d">3 derniers mois</SelectItem>
              <SelectItem value="1y">Dernière année</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={fetchAnalytics}>
            Actualiser
          </Button>
        </div>
      </div>

      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Revenus totaux</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.overview.totalRevenue)}
                </p>
                {formatPercentage(analytics.overview.revenueGrowth)}
              </div>
              <DollarSign className="h-8 w-8 text-emerald-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Utilisateurs</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.overview.totalUsers.toLocaleString()}
                </p>
                {formatPercentage(analytics.overview.userGrowth)}
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Services</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.overview.totalServices.toLocaleString()}
                </p>
                {formatPercentage(analytics.overview.serviceGrowth)}
              </div>
              <Briefcase className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Matches</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.overview.totalMatches.toLocaleString()}
                </p>
                {formatPercentage(analytics.overview.matchGrowth)}
              </div>
              <MessageSquare className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Graphiques de tendances */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Évolution des revenus</CardTitle>
            <CardDescription>Revenus mensuels sur les 6 derniers mois</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.monthlyData.map((data, index) => (
                <div key={data.month} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{data.month}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-emerald-500 h-2 rounded-full"
                        style={{ 
                          width: `${(data.revenue / Math.max(...analytics.monthlyData.map(d => d.revenue))) * 100}%` 
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-semibold w-20 text-right">
                      {formatCurrency(data.revenue)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Croissance des utilisateurs</CardTitle>
            <CardDescription>Nouveaux utilisateurs par mois</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.monthlyData.map((data, index) => (
                <div key={data.month} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{data.month}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ 
                          width: `${(data.users / Math.max(...analytics.monthlyData.map(d => d.users))) * 100}%` 
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-semibold w-16 text-right">
                      {data.users}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Statistiques par catégorie */}
      <Card>
        <CardHeader>
          <CardTitle>Performance par catégorie</CardTitle>
          <CardDescription>Analyse des services et revenus par catégorie</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Catégorie</th>
                  <th className="text-right py-2">Services</th>
                  <th className="text-right py-2">Revenus</th>
                  <th className="text-right py-2">Prix moyen</th>
                  <th className="text-right py-2">Performance</th>
                </tr>
              </thead>
              <tbody>
                {analytics.categoryStats.map((category, index) => (
                  <tr key={category.category} className="border-b">
                    <td className="py-3 font-medium">{category.category}</td>
                    <td className="text-right py-3">{category.services}</td>
                    <td className="text-right py-3">{formatCurrency(category.revenue)}</td>
                    <td className="text-right py-3">{formatCurrency(category.avgPrice)}</td>
                    <td className="text-right py-3">
                      <div className="w-16 bg-gray-200 rounded-full h-2 ml-auto">
                        <div 
                          className="bg-gradient-to-r from-emerald-400 to-emerald-600 h-2 rounded-full"
                          style={{ 
                            width: `${(category.revenue / Math.max(...analytics.categoryStats.map(c => c.revenue))) * 100}%` 
                          }}
                        ></div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Top performers */}
      <Card>
        <CardHeader>
          <CardTitle>Top Prestataires</CardTitle>
          <CardDescription>Les prestataires les plus performants du mois</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.topPerformers.map((performer, index) => (
              <div key={performer.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-emerald-100 text-emerald-600 rounded-full font-bold">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{performer.name}</p>
                    <p className="text-sm text-gray-500">{performer.services} services</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold">{formatCurrency(performer.revenue)}</p>
                  <div className="flex items-center space-x-1 text-sm text-gray-500">
                    <span>★ {performer.rating.toFixed(1)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
