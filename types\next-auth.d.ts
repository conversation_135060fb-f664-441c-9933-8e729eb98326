import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
      userType?: string
      locationCity?: string
      locationRegion?: string
      rating?: number
      reviewCount?: number
    }
  }

  interface User {
    id: string
    userType?: string
    locationCity?: string
    locationRegion?: string
    rating?: number
    reviewCount?: number
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string
    userType?: string
    locationCity?: string
    locationRegion?: string
    rating?: number
    reviewCount?: number
  }
}
