{"config": {"configFile": "E:\\NodeProjects\\QirbLik\\playwright.config.ts", "rootDir": "E:/NodeProjects/QirbLik/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 8}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "E:/NodeProjects/QirbLik/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "chromium", "name": "chromium", "testDir": "E:/NodeProjects/QirbLik/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/NodeProjects/QirbLik/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "firefox", "name": "firefox", "testDir": "E:/NodeProjects/QirbLik/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/NodeProjects/QirbLik/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "webkit", "name": "webkit", "testDir": "E:/NodeProjects/QirbLik/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/NodeProjects/QirbLik/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "E:/NodeProjects/QirbLik/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/NodeProjects/QirbLik/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "E:/NodeProjects/QirbLik/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 8, "webServer": null}, "suites": [{"title": "auth.spec.ts", "file": "auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Authentication Flow", "file": "auth.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display login page when accessing protected route", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 2880, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:37.713Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-s-197ac-n-accessing-protected-route-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-s-197ac-n-accessing-protected-route-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-7d4f8265ca41d7088079", "file": "auth.spec.ts", "line": 10, "column": 7}, {"title": "should login with valid credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 2876, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:37.709Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-login-with-valid-credentials-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-login-with-valid-credentials-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-9aa3fd5f971f9fd61180", "file": "auth.spec.ts", "line": 24, "column": 7}, {"title": "should show error with invalid credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 3004, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:37.706Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-s-22fb7-or-with-invalid-credentials-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-s-22fb7-or-with-invalid-credentials-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-32f56b95de6bdc3a9955", "file": "auth.spec.ts", "line": 56, "column": 7}, {"title": "should register new user", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 2946, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:37.747Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-register-new-user-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-register-new-user-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-c17ee5a8099091792871", "file": "auth.spec.ts", "line": 79, "column": 7}, {"title": "should logout successfully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 2990, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:37.759Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-logout-successfully-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-logout-successfully-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-c0ce6ccc7b7e02794edb", "file": "auth.spec.ts", "line": 108, "column": 7}, {"title": "should handle Google OAuth flow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 2927, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:37.749Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-handle-Google-OAuth-flow-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-handle-Google-OAuth-flow-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-2a59c7b7b80dcff4939c", "file": "auth.spec.ts", "line": 134, "column": 7}, {"title": "should validate form fields", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "failed", "duration": 3152, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:37.740Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-validate-form-fields-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-validate-form-fields-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-0db04a2c83a1030e61d2", "file": "auth.spec.ts", "line": 150, "column": 7}, {"title": "should remember user session", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "failed", "duration": 2941, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:37.720Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-remember-user-session-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-remember-user-session-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-3b3838f545543f90209e", "file": "auth.spec.ts", "line": 173, "column": 7}, {"title": "should handle password reset flow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 4, "status": "failed", "duration": 2826, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Ensure server is ready before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m\u001b[33m,\u001b[39m { waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Additional wait for stability\u001b[39m\n \u001b[90m 8 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:43.836Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-handle-password-reset-flow-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\auth-Authentication-Flow-should-handle-password-reset-flow-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\auth.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-77191ea14b0f9441dfa4", "file": "auth.spec.ts", "line": 197, "column": 7}, {"title": "should display login page when accessing protected route", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 23, "parallelIndex": 5, "status": "interrupted", "duration": 7, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:53.551Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "d748ac400d08b85935ef-02f29e7c059edff722c5", "file": "auth.spec.ts", "line": 10, "column": 7}, {"title": "should login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-c4a1c1e29e29e59ced9a", "file": "auth.spec.ts", "line": 24, "column": 7}, {"title": "should show error with invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-d2604529717af9e35508", "file": "auth.spec.ts", "line": 56, "column": 7}, {"title": "should register new user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-b48d3a76dcd176e2bc24", "file": "auth.spec.ts", "line": 79, "column": 7}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-4410c42745b7a4d89d03", "file": "auth.spec.ts", "line": 108, "column": 7}, {"title": "should handle Google OAuth flow", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-3d4336a07a7c6f0df794", "file": "auth.spec.ts", "line": 134, "column": 7}, {"title": "should validate form fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-9583175e874ce62f98d2", "file": "auth.spec.ts", "line": 150, "column": 7}, {"title": "should remember user session", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-d7bc621130fa26d246aa", "file": "auth.spec.ts", "line": 173, "column": 7}, {"title": "should handle password reset flow", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-db90337615ab319d9c01", "file": "auth.spec.ts", "line": 197, "column": 7}, {"title": "should display login page when accessing protected route", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-a550595bb99b410b6956", "file": "auth.spec.ts", "line": 10, "column": 7}, {"title": "should login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-b3a8c8ef2adbfe7de537", "file": "auth.spec.ts", "line": 24, "column": 7}, {"title": "should show error with invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-4201a53bab8fcc8ef77f", "file": "auth.spec.ts", "line": 56, "column": 7}, {"title": "should register new user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-f28be87908f03bdf829f", "file": "auth.spec.ts", "line": 79, "column": 7}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-1f08bff9df919d288ae6", "file": "auth.spec.ts", "line": 108, "column": 7}, {"title": "should handle Google OAuth flow", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-36c6d256f96711c6dfc0", "file": "auth.spec.ts", "line": 134, "column": 7}, {"title": "should validate form fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-3d6e4f85b0c8935d250b", "file": "auth.spec.ts", "line": 150, "column": 7}, {"title": "should remember user session", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-a70ee5b2621579fa07af", "file": "auth.spec.ts", "line": 173, "column": 7}, {"title": "should handle password reset flow", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-e0faba1b8365ae4132da", "file": "auth.spec.ts", "line": 197, "column": 7}, {"title": "should display login page when accessing protected route", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-201e8d5b5e27be963d6e", "file": "auth.spec.ts", "line": 10, "column": 7}, {"title": "should login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-a4da94ddcfa795bb02c4", "file": "auth.spec.ts", "line": 24, "column": 7}, {"title": "should show error with invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-805da6a94e0dac81ecdf", "file": "auth.spec.ts", "line": 56, "column": 7}, {"title": "should register new user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-6422d9f628614f03ede3", "file": "auth.spec.ts", "line": 79, "column": 7}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-8a3b860ad917d672a1c0", "file": "auth.spec.ts", "line": 108, "column": 7}, {"title": "should handle Google OAuth flow", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-d1083d1ce24f3e5364b6", "file": "auth.spec.ts", "line": 134, "column": 7}, {"title": "should validate form fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-e10ec17d5dae9efad7d8", "file": "auth.spec.ts", "line": 150, "column": 7}, {"title": "should remember user session", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-f22c7584ee199182233a", "file": "auth.spec.ts", "line": 173, "column": 7}, {"title": "should handle password reset flow", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-254b75b3e03ac18da274", "file": "auth.spec.ts", "line": 197, "column": 7}, {"title": "should display login page when accessing protected route", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-a73af9001a7c961f7427", "file": "auth.spec.ts", "line": 10, "column": 7}, {"title": "should login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-44e0a45c85d58f3c043b", "file": "auth.spec.ts", "line": 24, "column": 7}, {"title": "should show error with invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-fac73c7286d4343de891", "file": "auth.spec.ts", "line": 56, "column": 7}, {"title": "should register new user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-37b6f4fd78f2016d31e6", "file": "auth.spec.ts", "line": 79, "column": 7}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-5e89033e25aea64867f4", "file": "auth.spec.ts", "line": 108, "column": 7}, {"title": "should handle Google OAuth flow", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-1d20ec4456384e23e551", "file": "auth.spec.ts", "line": 134, "column": 7}, {"title": "should validate form fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-f273f092de7fec8177a4", "file": "auth.spec.ts", "line": 150, "column": 7}, {"title": "should remember user session", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-ee9c92504eda495a54ab", "file": "auth.spec.ts", "line": 173, "column": 7}, {"title": "should handle password reset flow", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-5ae345df009089220722", "file": "auth.spec.ts", "line": 197, "column": 7}]}]}, {"title": "connection-test.spec.ts", "file": "connection-test.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Connection Test", "file": "connection-test.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should connect to localhost:3000", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 7, "status": "failed", "duration": 2995, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts:8:18", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts", "column": 18, "line": 8}, "snippet": "\u001b[0m \u001b[90m  6 |\u001b[39m     \n \u001b[90m  7 |\u001b[39m     \u001b[36mtry\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  8 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m\u001b[33m,\u001b[39m { \n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  9 |\u001b[39m         waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 10 |\u001b[39m         timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m \n \u001b[90m 11 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts", "column": 18, "line": 8}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  6 |\u001b[39m     \n \u001b[90m  7 |\u001b[39m     \u001b[36mtry\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  8 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m\u001b[33m,\u001b[39m { \n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  9 |\u001b[39m         waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 10 |\u001b[39m         timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m \n \u001b[90m 11 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts:8:18\u001b[22m"}], "stdout": [{"text": "Attempting to connect to http://localhost:3000\n"}], "stderr": [{"text": "Connection failed: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\u001b[22m\n\n    at \u001b[90mE:\\NodeProjects\\QirbLik\\\u001b[39mtests\\e2e\\connection-test.spec.ts:8:18 {\n  name: \u001b[32m'Error'\u001b[39m,\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    location: {\n      file: \u001b[32m'E:\\\\NodeProjects\\\\QirbLik\\\\tests\\\\e2e\\\\connection-test.spec.ts'\u001b[39m,\n      line: \u001b[33m8\u001b[39m,\n      column: \u001b[33m18\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m'Navigate to \"/\"'\u001b[39m,\n    apiName: \u001b[32m'page.goto'\u001b[39m,\n    params: {\n      url: \u001b[32m'http://localhost:3000'\u001b[39m,\n      waitUntil: \u001b[32m'networkidle'\u001b[39m,\n      timeout: \u001b[33m10000\u001b[39m\n    },\n    group: \u001b[90mundefined\u001b[39m,\n    stepId: \u001b[32m'pw:api@35'\u001b[39m,\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@35'\u001b[39m,\n      _title: \u001b[32m'Navigate to \"/\"'\u001b[39m,\n      _parentStep: \u001b[90mundefined\u001b[39m,\n      skip: \u001b[36m[Function (anonymous)]\u001b[39m\n    },\n    recoverFromStepError: \u001b[36m[AsyncFunction: recoverFromStepError]\u001b[39m,\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1758204288880\u001b[39m,\n    error: {\n      message: \u001b[32m'Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\\x1B[22m\\n'\u001b[39m,\n      stack: \u001b[32m'Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - navigating to \"http://localhost:3000/\", waiting until \"networkidle\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at E:\\\\NodeProjects\\\\QirbLik\\\\tests\\\\e2e\\\\connection-test.spec.ts:8:18'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "startTime": "2025-09-18T14:04:43.822Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\connection-test-Connection-47cf0-d-connect-to-localhost-3000-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\connection-test-Connection-47cf0-d-connect-to-localhost-3000-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts", "column": 18, "line": 8}}], "status": "unexpected"}], "id": "4d6153524f7145ac9ecf-8d21da9e4f7fc385d883", "file": "connection-test.spec.ts", "line": 4, "column": 7}, {"title": "should access dashboard and get redirected to signin", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 6, "status": "failed", "duration": 3000, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/dashboard\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/dashboard\", waiting until \"networkidle\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/dashboard\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/dashboard\", waiting until \"networkidle\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts:29:18", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts", "column": 18, "line": 29}, "snippet": "\u001b[0m \u001b[90m 27 |\u001b[39m     \u001b[36mtry\u001b[39m {\n \u001b[90m 28 |\u001b[39m       \u001b[90m// Try to access dashboard without authentication\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 29 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000/dashboard'\u001b[39m\u001b[33m,\u001b[39m { \n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 30 |\u001b[39m         waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 31 |\u001b[39m         timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m \n \u001b[90m 32 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts", "column": 18, "line": 29}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/dashboard\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/dashboard\", waiting until \"networkidle\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 27 |\u001b[39m     \u001b[36mtry\u001b[39m {\n \u001b[90m 28 |\u001b[39m       \u001b[90m// Try to access dashboard without authentication\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 29 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000/dashboard'\u001b[39m\u001b[33m,\u001b[39m { \n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 30 |\u001b[39m         waitUntil\u001b[33m:\u001b[39m \u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 31 |\u001b[39m         timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m \n \u001b[90m 32 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts:29:18\u001b[22m"}], "stdout": [{"text": "Testing dashboard redirect...\n"}], "stderr": [{"text": "Dashboard redirect test failed: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/dashboard\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/dashboard\", waiting until \"networkidle\"\u001b[22m\n\n    at \u001b[90mE:\\NodeProjects\\QirbLik\\\u001b[39mtests\\e2e\\connection-test.spec.ts:29:18 {\n  name: \u001b[32m'Error'\u001b[39m,\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    location: {\n      file: \u001b[32m'E:\\\\NodeProjects\\\\QirbLik\\\\tests\\\\e2e\\\\connection-test.spec.ts'\u001b[39m,\n      line: \u001b[33m29\u001b[39m,\n      column: \u001b[33m18\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m'Navigate to \"/dashboard\"'\u001b[39m,\n    apiName: \u001b[32m'page.goto'\u001b[39m,\n    params: {\n      url: \u001b[32m'http://localhost:3000/dashboard'\u001b[39m,\n      waitUntil: \u001b[32m'networkidle'\u001b[39m,\n      timeout: \u001b[33m10000\u001b[39m\n    },\n    group: \u001b[90mundefined\u001b[39m,\n    stepId: \u001b[32m'pw:api@35'\u001b[39m,\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@35'\u001b[39m,\n      _title: \u001b[32m'Navigate to \"/dashboard\"'\u001b[39m,\n      _parentStep: \u001b[90mundefined\u001b[39m,\n      skip: \u001b[36m[Function (anonymous)]\u001b[39m\n    },\n    recoverFromStepError: \u001b[36m[AsyncFunction: recoverFromStepError]\u001b[39m,\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1758204288958\u001b[39m,\n    error: {\n      message: \u001b[32m'Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/dashboard\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - navigating to \"http://localhost:3000/dashboard\", waiting until \"networkidle\"\\x1B[22m\\n'\u001b[39m,\n      stack: \u001b[32m'Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/dashboard\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - navigating to \"http://localhost:3000/dashboard\", waiting until \"networkidle\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at E:\\\\NodeProjects\\\\QirbLik\\\\tests\\\\e2e\\\\connection-test.spec.ts:29:18'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "startTime": "2025-09-18T14:04:43.891Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\connection-test-Connection-66f23-nd-get-redirected-to-signin-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\connection-test-Connection-66f23-nd-get-redirected-to-signin-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\connection-test.spec.ts", "column": 18, "line": 29}}], "status": "unexpected"}], "id": "4d6153524f7145ac9ecf-35460daecd4e48e8e2f3", "file": "connection-test.spec.ts", "line": 24, "column": 7}, {"title": "should connect to localhost:3000", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4d6153524f7145ac9ecf-fab29e5a4242c7ab9924", "file": "connection-test.spec.ts", "line": 4, "column": 7}, {"title": "should access dashboard and get redirected to signin", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4d6153524f7145ac9ecf-895e0a591144ae48bfe9", "file": "connection-test.spec.ts", "line": 24, "column": 7}, {"title": "should connect to localhost:3000", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4d6153524f7145ac9ecf-5d23907493b1517a641f", "file": "connection-test.spec.ts", "line": 4, "column": 7}, {"title": "should access dashboard and get redirected to signin", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4d6153524f7145ac9ecf-c46dd9ec2436b09ccd7a", "file": "connection-test.spec.ts", "line": 24, "column": 7}, {"title": "should connect to localhost:3000", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4d6153524f7145ac9ecf-8b45495e2200dae30de2", "file": "connection-test.spec.ts", "line": 4, "column": 7}, {"title": "should access dashboard and get redirected to signin", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4d6153524f7145ac9ecf-6e660bd7ee60356d4845", "file": "connection-test.spec.ts", "line": 24, "column": 7}, {"title": "should connect to localhost:3000", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4d6153524f7145ac9ecf-db1bfa5c87367d5e0afd", "file": "connection-test.spec.ts", "line": 4, "column": 7}, {"title": "should access dashboard and get redirected to signin", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4d6153524f7145ac9ecf-2b0c32e88895728e3873", "file": "connection-test.spec.ts", "line": 24, "column": 7}]}]}, {"title": "portfolio.spec.ts", "file": "portfolio.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Portfolio Management", "file": "portfolio.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display portfolio page with existing items", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "failed", "duration": 2957, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetBy<PERSON>abel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:43.976Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\portfolio-Portfolio-Manage-895e3-io-page-with-existing-items-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\portfolio-Portfolio-Manage-895e3-io-page-with-existing-items-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "34cb4603fa9a5b79d30c-4f055c499ede233d8f9d", "file": "portfolio.spec.ts", "line": 13, "column": 7}, {"title": "should create new portfolio item", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 2, "status": "failed", "duration": 2988, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetBy<PERSON>abel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:43.995Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\portfolio-Portfolio-Manage-d33e4-d-create-new-portfolio-item-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\portfolio-Portfolio-Manage-d33e4-d-create-new-portfolio-item-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "34cb4603fa9a5b79d30c-9704b4d555f1cc315ec4", "file": "portfolio.spec.ts", "line": 25, "column": 7}, {"title": "should edit existing portfolio item", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 1, "status": "failed", "duration": 2783, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetBy<PERSON>abel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:45.482Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\portfolio-Portfolio-Manage-6e658-dit-existing-portfolio-item-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\portfolio-Portfolio-Manage-6e658-dit-existing-portfolio-item-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "34cb4603fa9a5b79d30c-cc8218add73d06880413", "file": "portfolio.spec.ts", "line": 68, "column": 7}, {"title": "should delete portfolio item", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 5, "status": "failed", "duration": 2976, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetBy<PERSON>abel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:45.622Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\portfolio-Portfolio-Management-should-delete-portfolio-item-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\portfolio-Portfolio-Management-should-delete-portfolio-item-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "34cb4603fa9a5b79d30c-393c79e8ac93150a2fa6", "file": "portfolio.spec.ts", "line": 99, "column": 7}, {"title": "should filter portfolio by category", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 15, "parallelIndex": 0, "status": "failed", "duration": 2793, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetBy<PERSON>abel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:45.598Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\NodeProjects\\QirbLik\\test-results\\portfolio-Portfolio-Manage-c6c60-ilter-portfolio-by-category-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\NodeProjects\\QirbLik\\test-results\\portfolio-Portfolio-Manage-c6c60-ilter-portfolio-by-category-chromium\\video.webm"}], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "34cb4603fa9a5b79d30c-67733e1220e79232cae8", "file": "portfolio.spec.ts", "line": 125, "column": 7}, {"title": "should toggle item visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 16, "parallelIndex": 4, "status": "interrupted", "duration": 1865, "error": {"message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetBy<PERSON>abel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16\u001b[22m"}, {"message": "Error: browserContext.close: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:50.125Z", "annotations": [], "attachments": [], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}}], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-5191e247fba6fc1bee32", "file": "portfolio.spec.ts", "line": 148, "column": 7}, {"title": "should share portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 17, "parallelIndex": 7, "status": "interrupted", "duration": 6, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:50.243Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-e927c4318613a1cf5fa5", "file": "portfolio.spec.ts", "line": 164, "column": 7}, {"title": "should handle empty portfolio state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 18, "parallelIndex": 6, "status": "interrupted", "duration": 1551, "error": {"message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetBy<PERSON>abel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16\u001b[22m"}, {"message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1187\\chrome-win\\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AvoidUnnecessaryBeforeUnloadCheckSync,DestroyProfileOnBrowserClose,DialMediaRouteProvider,GlobalMediaControls,HttpsUpgrades,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-XXXXXXbrpjRS --remote-debugging-pipe --no-startup-window\n<launched> pid=15788"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:50.289Z", "annotations": [], "attachments": [], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}}], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-25e16d8b3c89f73e034b", "file": "portfolio.spec.ts", "line": 186, "column": 7}, {"title": "should validate form inputs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "interrupted", "duration": 1509, "error": {"message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetBy<PERSON>abel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16\u001b[22m"}, {"message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1187\\chrome-win\\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AvoidUnnecessaryBeforeUnloadCheckSync,DestroyProfileOnBrowserClose,DialMediaRouteProvider,GlobalMediaControls,HttpsUpgrades,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-XXXXXXL0fJfo --remote-debugging-pipe --no-startup-window\n<launched> pid=31492"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:50.415Z", "annotations": [], "attachments": [], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}}], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-840ec39468b2cbf2905f", "file": "portfolio.spec.ts", "line": 200, "column": 7}, {"title": "should handle image upload", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "interrupted", "duration": 1580, "error": {"message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetBy<PERSON>abel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16\u001b[22m"}, {"message": "Error: browserContext.close: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:50.430Z", "annotations": [], "attachments": [], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}}], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-1755fe342b39735a6170", "file": "portfolio.spec.ts", "line": 220, "column": 7}, {"title": "should display portfolio statistics", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 1, "status": "failed", "duration": 582, "error": {"message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetBy<PERSON>abel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16\u001b[22m"}, {"message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1187\\chrome-win\\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AvoidUnnecessaryBeforeUnloadCheckSync,DestroyProfileOnBrowserClose,DialMediaRouteProvider,GlobalMediaControls,HttpsUpgrades,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-XXXXXXilOGbR --remote-debugging-pipe --no-startup-window\n<launched> pid=38092"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:51.368Z", "annotations": [], "attachments": [], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "34cb4603fa9a5b79d30c-c86fde7da75ae86110b5", "file": "portfolio.spec.ts", "line": 235, "column": 7}, {"title": "should work on mobile viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 22, "parallelIndex": 0, "status": "failed", "duration": 588, "error": {"message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16", "location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetBy<PERSON>abel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/auth/signin\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/auth/signin\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// <PERSON><PERSON> as prestataire before each test\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Email'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByLabel(\u001b[32m'Mot de passe'\u001b[39m)\u001b[33m.\u001b[39mfill(\u001b[32m'password123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Se connecter'\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts:6:16\u001b[22m"}, {"message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1187\\chrome-win\\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AvoidUnnecessaryBeforeUnloadCheckSync,DestroyProfileOnBrowserClose,DialMediaRouteProvider,GlobalMediaControls,HttpsUpgrades,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-XXXXXXcFn1tO --remote-debugging-pipe --no-startup-window\n<launched> pid=40760"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-18T14:04:51.446Z", "annotations": [], "attachments": [], "errorLocation": {"file": "E:\\NodeProjects\\QirbLik\\tests\\e2e\\portfolio.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "34cb4603fa9a5b79d30c-9be5af1b11ea6fe91022", "file": "portfolio.spec.ts", "line": 248, "column": 7}, {"title": "should display portfolio page with existing items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-c42cb728502d7975aa91", "file": "portfolio.spec.ts", "line": 13, "column": 7}, {"title": "should create new portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-77e08f6ae1f687a5ed08", "file": "portfolio.spec.ts", "line": 25, "column": 7}, {"title": "should edit existing portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-8dbeb5f029edd865e422", "file": "portfolio.spec.ts", "line": 68, "column": 7}, {"title": "should delete portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-4a0ca16370a3e72e9340", "file": "portfolio.spec.ts", "line": 99, "column": 7}, {"title": "should filter portfolio by category", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-66e05ffc9a4f78347c4f", "file": "portfolio.spec.ts", "line": 125, "column": 7}, {"title": "should toggle item visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-e5b6b8ebf39446b334fe", "file": "portfolio.spec.ts", "line": 148, "column": 7}, {"title": "should share portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-cba422b015dba176407a", "file": "portfolio.spec.ts", "line": 164, "column": 7}, {"title": "should handle empty portfolio state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-0432ad58d1996e3b13f7", "file": "portfolio.spec.ts", "line": 186, "column": 7}, {"title": "should validate form inputs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-e217c231205d20d9289e", "file": "portfolio.spec.ts", "line": 200, "column": 7}, {"title": "should handle image upload", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-fcc6aa3ab0c03fafdb1f", "file": "portfolio.spec.ts", "line": 220, "column": 7}, {"title": "should display portfolio statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-daf9ada1022afaea1675", "file": "portfolio.spec.ts", "line": 235, "column": 7}, {"title": "should work on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-8c79e4c196b072615fb9", "file": "portfolio.spec.ts", "line": 248, "column": 7}, {"title": "should display portfolio page with existing items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-72c0547d522905e4c6ac", "file": "portfolio.spec.ts", "line": 13, "column": 7}, {"title": "should create new portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-9a9f51ee9f6113e635e5", "file": "portfolio.spec.ts", "line": 25, "column": 7}, {"title": "should edit existing portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-f429b7d043452fcd3a34", "file": "portfolio.spec.ts", "line": 68, "column": 7}, {"title": "should delete portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-a02d6538bd0be8d9d3e8", "file": "portfolio.spec.ts", "line": 99, "column": 7}, {"title": "should filter portfolio by category", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-026f9b8bdc3dec1c6b3a", "file": "portfolio.spec.ts", "line": 125, "column": 7}, {"title": "should toggle item visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-12da66ee2cafcd0ab4ef", "file": "portfolio.spec.ts", "line": 148, "column": 7}, {"title": "should share portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-68bed5dd9b1786b3a98b", "file": "portfolio.spec.ts", "line": 164, "column": 7}, {"title": "should handle empty portfolio state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-0e7b1754e5c801b812b5", "file": "portfolio.spec.ts", "line": 186, "column": 7}, {"title": "should validate form inputs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-1737d95202ed71603334", "file": "portfolio.spec.ts", "line": 200, "column": 7}, {"title": "should handle image upload", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-3edab830be9684bd66be", "file": "portfolio.spec.ts", "line": 220, "column": 7}, {"title": "should display portfolio statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-552920a58da59c0b4c28", "file": "portfolio.spec.ts", "line": 235, "column": 7}, {"title": "should work on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-2cd8957c62db21526ae7", "file": "portfolio.spec.ts", "line": 248, "column": 7}, {"title": "should display portfolio page with existing items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-94e1a880c7df25be1500", "file": "portfolio.spec.ts", "line": 13, "column": 7}, {"title": "should create new portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-e0f21a98b41efeb07c1a", "file": "portfolio.spec.ts", "line": 25, "column": 7}, {"title": "should edit existing portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-6927d9ed3de44a28a75e", "file": "portfolio.spec.ts", "line": 68, "column": 7}, {"title": "should delete portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-b48d32c43ab26d0bd398", "file": "portfolio.spec.ts", "line": 99, "column": 7}, {"title": "should filter portfolio by category", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-ce75f3e3228382abcc11", "file": "portfolio.spec.ts", "line": 125, "column": 7}, {"title": "should toggle item visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-fbcfd14f4ff1fd9d7634", "file": "portfolio.spec.ts", "line": 148, "column": 7}, {"title": "should share portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-13ca6eabdf9967a88dd7", "file": "portfolio.spec.ts", "line": 164, "column": 7}, {"title": "should handle empty portfolio state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-8c638857cb571a75b114", "file": "portfolio.spec.ts", "line": 186, "column": 7}, {"title": "should validate form inputs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-b3d37b660904136ef593", "file": "portfolio.spec.ts", "line": 200, "column": 7}, {"title": "should handle image upload", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-ad582cc7a984f875ac1c", "file": "portfolio.spec.ts", "line": 220, "column": 7}, {"title": "should display portfolio statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-71afada8be30115b4dff", "file": "portfolio.spec.ts", "line": 235, "column": 7}, {"title": "should work on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-982520ddaee44b5307c6", "file": "portfolio.spec.ts", "line": 248, "column": 7}, {"title": "should display portfolio page with existing items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-a850c85f7915e92028c0", "file": "portfolio.spec.ts", "line": 13, "column": 7}, {"title": "should create new portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-e79e0c99ac8f199e6b96", "file": "portfolio.spec.ts", "line": 25, "column": 7}, {"title": "should edit existing portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-6d6109fa106569c2eda4", "file": "portfolio.spec.ts", "line": 68, "column": 7}, {"title": "should delete portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-cc06e221a41a9e7a8278", "file": "portfolio.spec.ts", "line": 99, "column": 7}, {"title": "should filter portfolio by category", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-7924d398a3a5477b7c7b", "file": "portfolio.spec.ts", "line": 125, "column": 7}, {"title": "should toggle item visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-5873d161331edf050756", "file": "portfolio.spec.ts", "line": 148, "column": 7}, {"title": "should share portfolio item", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-13c8b2f99418f1552da7", "file": "portfolio.spec.ts", "line": 164, "column": 7}, {"title": "should handle empty portfolio state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-ee1d48191d0be7878ec4", "file": "portfolio.spec.ts", "line": 186, "column": 7}, {"title": "should validate form inputs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-b560571440a0f7961ac7", "file": "portfolio.spec.ts", "line": 200, "column": 7}, {"title": "should handle image upload", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-02215bbb6333ab<PERSON>eba", "file": "portfolio.spec.ts", "line": 220, "column": 7}, {"title": "should display portfolio statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-34f9c698f59ad4218fae", "file": "portfolio.spec.ts", "line": 235, "column": 7}, {"title": "should work on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "34cb4603fa9a5b79d30c-1697f11b4c83b22f6f9b", "file": "portfolio.spec.ts", "line": 248, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-09-18T14:04:36.838Z", "duration": 17852.019, "expected": 0, "skipped": 97, "unexpected": 18, "flaky": 0}}