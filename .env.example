# Database (SQLite pour développement)
DATABASE_URL="file:./prisma/dev.db"

# NextAuth.js (OBLIGATOIRE)
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="qriblik-super-secret-key-2024-production-ready-auth"

# OAuth Providers (optionnel)
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Stripe (paiements)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Socket.io (messagerie)
SOCKET_IO_URL="http://localhost:3000"

# Email (notifications)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# SMS (optionnel)
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+**********"

# CMI Maroc (paiements locaux)
CMI_MERCHANT_ID="your-cmi-merchant-id"
CMI_SECRET_KEY="your-cmi-secret-key"
CMI_TEST_MODE=true

# Upload d'images
CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
CLOUDINARY_API_KEY="your-cloudinary-api-key"
CLOUDINARY_API_SECRET="your-cloudinary-api-secret"

# Google Maps
GOOGLE_MAPS_API_KEY="your-google-maps-api-key"

# Analytics
GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"

# Monitoring
SENTRY_DSN="your-sentry-dsn"
