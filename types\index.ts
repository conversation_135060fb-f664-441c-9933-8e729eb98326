// Types pour l'application QribLik
import { NextApiResponse } from 'next'
import { Server as ServerIO } from 'socket.io'

export interface NextApiResponseServerIO extends NextApiResponse {
  socket: {
    server: {
      io: ServerIO
    } & any
  } & any
}

export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  phone?: string
  bio?: string
  userType: UserType
  isVerified: boolean
  location?: Location
  serviceRadius: number
  rating?: number
  reviewCount: number
  responseTime?: number
  completionRate: number
  subscriptionStatus: SubscriptionStatus
  isActive: boolean
  lastSeen: Date
  createdAt: Date
  updatedAt: Date
  locationLat?: number
  locationLng?: number
  locationAddress?: string
  locationCity?: string
  locationRegion?: string
}

export interface Service {
  id: string
  title: string
  description: string
  shortDesc?: string
  category: string
  subCategory?: string
  tags: string[]
  price?: number
  priceType: 'FIXED' | 'HOURLY' | 'DAILY' | 'QUOTE'
  images: string[]
  video?: string
  isRemote: boolean
  availability?: string
  status: 'ACTIVE' | 'PAUSED' | 'INACTIVE' | 'PENDING_APPROVAL'
  isPromoted: boolean
  promoteEnds?: Date
  viewCount: number
  contactCount: number
  requirements?: string
  duration?: string
  deliverables: string[]
  userId: string
  user?: User
  createdAt: Date
  updatedAt: Date
  locationLat?: number
  locationLng?: number
  locationAddress?: string
  locationCity?: string
  locationRegion?: string
}

export interface Request {
  id: string
  title: string
  description: string
  category: string
  subCategory?: string
  tags: string[]
  budget?: number
  budgetType: 'FIXED' | 'RANGE' | 'HOURLY'
  urgency: 'LOW' | 'NORMAL' | 'HIGH' | 'EMERGENCY'
  preferredTiming?: string
  deadline?: Date
  images: string[]
  attachments: string[]
  status: 'OPEN' | 'MATCHED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'EXPIRED'
  isPublic: boolean
  expiresAt?: Date
  viewCount: number
  proposalCount: number
  clientId: string
  client?: User
  createdAt: Date
  updatedAt: Date
  locationLat?: number
  locationLng?: number
  locationAddress?: string
  locationCity?: string
  locationRegion?: string
}

export interface Match {
  id: string
  requestId: string
  request: Request
  serviceId?: string
  service?: Service
  prestataireId: string
  prestataire: User
  clientId: string
  client: User
  message?: string
  proposedPrice?: number
  proposedDelay?: string
  conditions?: string
  status: 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  clientResponse?: string
  proposedAt: Date
  respondedAt?: Date
  acceptedAt?: Date
  completedAt?: Date
  autoGenerated: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Conversation {
  id: string
  matchId?: string
  requestId?: string
  lastMessage?: string
  lastMessageAt?: Date
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  participants?: ConversationParticipant[]
  messages?: Message[]
  unreadCount?: number
  otherParticipants?: ConversationParticipant[]
}

export interface ConversationParticipant {
  id: string
  conversationId: string
  userId: string
  joinedAt: Date
  lastReadAt?: Date
  user?: User
}

export interface Message {
  id: string
  content: string
  messageType: MessageType
  attachments: string[]
  metadata?: any
  conversationId: string
  senderId: string
  sender: User
  readBy: string[]
  readAt?: Date
  isEdited: boolean
  isDeleted: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Review {
  id: string
  matchId: string
  reviewerId: string
  reviewer: User
  revieweeId: string
  reviewee: User
  rating: number
  comment?: string
  qualityRating?: number
  communicationRating?: number
  timelinessRating?: number
  valueRating?: number
  images: string[]
  isPublic: boolean
  isVerified: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Notification {
  id: string
  title: string
  message: string
  notificationType: NotificationType
  userId: string
  relatedId?: string
  relatedType?: string
  actionUrl?: string
  isRead: boolean
  isEmailSent: boolean
  isPushSent: boolean
  createdAt: Date
}

export interface Location {
  lat: number
  lng: number
  address: string
  city: string
  region: string
}

// Enums
export enum UserType {
  CLIENT = 'CLIENT',
  PRESTATAIRE = 'PRESTATAIRE',
  BOTH = 'BOTH'
}

export enum Category {
  BRICOLAGE_REPARATIONS = 'BRICOLAGE_REPARATIONS',
  MENAGE_NETTOYAGE = 'MENAGE_NETTOYAGE',
  JARDINAGE_ESPACES_VERTS = 'JARDINAGE_ESPACES_VERTS',
  DEMENAGEMENT_TRANSPORT = 'DEMENAGEMENT_TRANSPORT',
  COURS_PARTICULIERS = 'COURS_PARTICULIERS',
  SERVICES_PERSONNE = 'SERVICES_PERSONNE',
  EVENEMENTS_ANIMATION = 'EVENEMENTS_ANIMATION',
  BEAUTE_BIEN_ETRE = 'BEAUTE_BIEN_ETRE',
  INFORMATIQUE_TECH = 'INFORMATIQUE_TECH',
  AUTOMOBILE = 'AUTOMOBILE',
  IMMOBILIER = 'IMMOBILIER',
  AUTRES = 'AUTRES'
}

export enum ServiceStatus {
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  INACTIVE = 'INACTIVE',
  PENDING_APPROVAL = 'PENDING_APPROVAL'
}

export enum RequestStatus {
  OPEN = 'OPEN',
  MATCHED = 'MATCHED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED'
}

export enum MatchStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum PriceType {
  FIXED = 'FIXED',
  HOURLY = 'HOURLY',
  DAILY = 'DAILY',
  QUOTE = 'QUOTE'
}

export enum BudgetType {
  FIXED = 'FIXED',
  RANGE = 'RANGE',
  HOURLY = 'HOURLY'
}

export enum Urgency {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  EMERGENCY = 'EMERGENCY'
}

export enum MessageType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  FILE = 'FILE',
  LOCATION = 'LOCATION',
  SYSTEM = 'SYSTEM'
}

export enum NotificationType {
  NEW_MATCH = 'NEW_MATCH',
  MESSAGE_RECEIVED = 'MESSAGE_RECEIVED',
  MATCH_ACCEPTED = 'MATCH_ACCEPTED',
  MATCH_REJECTED = 'MATCH_REJECTED',
  SERVICE_COMPLETED = 'SERVICE_COMPLETED',
  REVIEW_RECEIVED = 'REVIEW_RECEIVED',
  PAYMENT_RECEIVED = 'PAYMENT_RECEIVED',
  SYSTEM_UPDATE = 'SYSTEM_UPDATE'
}

export enum SubscriptionStatus {
  FREE = 'FREE',
  ACTIVE = 'ACTIVE',
  PAST_DUE = 'PAST_DUE',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED'
}

export enum SubscriptionPlan {
  FREE = 'FREE',
  BASIC = 'BASIC',
  PRO = 'PRO',
  PREMIUM = 'PREMIUM'
}