import Stripe from 'stripe'
import { loadStripe } from '@stripe/stripe-js'

// Server-side Stripe instance
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
})

// Client-side Stripe instance
export const getStripe = () => {
  return loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)
}

// Payment status types
export type PaymentStatus = 
  | 'pending'
  | 'processing'
  | 'succeeded'
  | 'failed'
  | 'canceled'
  | 'requires_action'

// Payment method types for Morocco
export type PaymentMethodType = 
  | 'card'
  | 'cmi'
  | 'bank_transfer'
  | 'cash_on_delivery'

// Create payment intent for service
export async function createPaymentIntent(
  amount: number,
  currency: string = 'mad',
  metadata: Record<string, string> = {}
) {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency,
      metadata,
      automatic_payment_methods: {
        enabled: true,
      },
    })

    return paymentIntent
  } catch (error) {
    console.error('Error creating payment intent:', error)
    throw error
  }
}

// Create Stripe customer
export async function createStripeCustomer(
  email: string,
  name?: string,
  metadata: Record<string, string> = {}
) {
  try {
    const customer = await stripe.customers.create({
      email,
      name,
      metadata,
    })

    return customer
  } catch (error) {
    console.error('Error creating Stripe customer:', error)
    throw error
  }
}

// Create connected account for service providers
export async function createConnectedAccount(
  email: string,
  country: string = 'MA',
  metadata: Record<string, string> = {}
) {
  try {
    const account = await stripe.accounts.create({
      type: 'express',
      country,
      email,
      metadata,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
    })

    return account
  } catch (error) {
    console.error('Error creating connected account:', error)
    throw error
  }
}

// Create account link for onboarding
export async function createAccountLink(accountId: string, refreshUrl: string, returnUrl: string) {
  try {
    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    })

    return accountLink
  } catch (error) {
    console.error('Error creating account link:', error)
    throw error
  }
}

// Transfer funds to connected account
export async function createTransfer(
  amount: number,
  destination: string,
  metadata: Record<string, string> = {}
) {
  try {
    const transfer = await stripe.transfers.create({
      amount: Math.round(amount * 100),
      currency: 'mad',
      destination,
      metadata,
    })

    return transfer
  } catch (error) {
    console.error('Error creating transfer:', error)
    throw error
  }
}

// Calculate platform fee (5% of transaction)
export function calculatePlatformFee(amount: number): number {
  return Math.round(amount * 0.05 * 100) / 100
}

// Calculate service provider amount after platform fee
export function calculateProviderAmount(amount: number): number {
  const platformFee = calculatePlatformFee(amount)
  return amount - platformFee
}
